import { z } from 'zod';
import { SortDirection } from '@/shared/dto/request/query.dto';
import i18n from '@/lib/i18n';

// Helper function to get translations
const t = (key: string) => i18n.t(key);

/**
 * Schema for point package information
 */
export const pointSchema = z.object({
  id: z.number(),
  name: z.string(),
  cash: z.coerce.number().min(0),
  rate: z.coerce.number().min(1).optional().default(1000),
  min: z.coerce.number().default(0),
  max: z.coerce.number().default(0),
  point: z.coerce.number().min(0),
  isCustomize: z.boolean(),
  description: z.string().nullable(),
});

/**
 * Schema for point package query parameters
 */
export const pointQuerySchema = z.object({
  page: z.number().optional().default(1),
  limit: z.number().optional().default(10),
  search: z.string().optional(),
  isCustomize: z.boolean().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.nativeEnum(SortDirection).optional(),
});

/**
 * Schema for creating a new point package
 */
export const createPointSchema = z.object({
  name: z.string().min(1, t('r-point:validation.nameRequired')),
  cash: z.coerce.number()
    .min(20000, t('r-point:validation.cashMin'))
    .max(500000, t('r-point:validation.cashMax')),
  point: z.coerce.number().min(1, t('r-point:validation.pointMin')),
  description: z.string().min(1, t('r-point:validation.descriptionRequired')),
  isCustomize: z.boolean().optional().default(false),
  rate: z.coerce.number().min(1).optional(),
  min: z.coerce.number().min(20000).max(500000).optional(),
  max: z.coerce.number().min(20000).max(500000).optional(),
});

/**
 * Schema for updating a point package
 */
export const updatePointSchema = z.object({
  name: z.string().min(1, t('r-point:validation.nameRequired')).optional(),
  cash: z.coerce.number().min(0, t('r-point:validation.cashMin')).optional(),
  point: z.coerce.number().min(0, t('r-point:validation.pointMin')).optional(),
  description: z.string().min(1, t('r-point:validation.descriptionRequired')).optional(),
  isCustomize: z.boolean().optional(),
  rate: z.coerce.number().min(1).optional(),
  min: z.coerce.number().min(20000).max(500000).optional(),
  max: z.coerce.number().min(20000).max(500000).optional(),
});
