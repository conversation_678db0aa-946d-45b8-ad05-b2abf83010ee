import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, ResponsiveGrid, Typography } from '@/shared/components/common';
import ModuleCard from '@/modules/components/card/ModuleCard';
import { usePointData, useCouponData, useTransactionData } from '../hooks';

/**
 * Trang tổng quan quản lý R-Point
 */
const RPointDashboardPage: React.FC = () => {
  const { t } = useTranslation(['rpointAdmin', 'common']);

  // State lưu trữ số lượng
  const [pointCount, setPointCount] = useState<number>(0);
  const [transactionCount, setTransactionCount] = useState<number>(0);
  const [couponCount, setCouponCount] = useState<number>(0);

  // Sử dụng các hooks để lấy dữ liệu
  const { usePoints } = usePointData();
  const { useTransactions } = useTransactionData();
  const { useCoupons } = useCouponData();

  // Gọi API lấy danh sách gói point
  const { data: pointData } = usePoints({
    page: 1,
    limit: 1,
  });

  // Gọi API lấy danh sách giao dịch
  const { data: transactionData } = useTransactions({
    page: 1,
    limit: 1,
  });

  // Gọi API lấy danh sách coupon
  const { data: couponData } = useCoupons({
    page: 1,
    limit: 1,
  });

  // Cập nhật số lượng khi có dữ liệu
  useEffect(() => {
    if (pointData?.meta) {
      setPointCount(pointData.meta.totalItems);
    }
  }, [pointData]);

  useEffect(() => {
    if (transactionData?.meta) {
      setTransactionCount(transactionData.meta.totalItems);
    }
  }, [transactionData]);

  useEffect(() => {
    if (couponData?.meta) {
      setCouponCount(couponData.meta.totalItems);
    }
  }, [couponData]);

  return (
    <div className="p-6">
      <Typography variant="h1" className="mb-6">
        {t('rpointAdmin:dashboard.title')}
      </Typography>

      <Card className="mb-6 p-6">
       

        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 3 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
          gap={{ xs: 4, md: 6 }}
        >
          {/* Gói R-Point */}
          <ModuleCard
            title={t('rpointAdmin:dashboard.modules.points.title')}
            description={t('rpointAdmin:dashboard.modules.points.description')}
            icon="coin"
            count={pointCount}
            countLabel={t('rpointAdmin:dashboard.modules.points.countLabel')}
            linkTo="/admin/r-point/points"
            linkText={t('rpointAdmin:dashboard.modules.points.linkText')}
          />

          {/* Đơn hàng R-Point */}
          <ModuleCard
            title={t('rpointAdmin:dashboard.modules.transactions.title')}
            description={t('rpointAdmin:dashboard.modules.transactions.description')}
            icon="receipt"
            count={transactionCount}
            countLabel={t('rpointAdmin:dashboard.modules.transactions.countLabel')}
            linkTo="/admin/r-point/transactions"
            linkText={t('rpointAdmin:dashboard.modules.transactions.linkText')}
          />

          {/* Mã khuyến mãi */}
          <ModuleCard
            title={t('rpointAdmin:dashboard.modules.coupons.title')}
            description={t('rpointAdmin:dashboard.modules.coupons.description')}
            icon="ticket"
            count={couponCount}
            countLabel={t('rpointAdmin:dashboard.modules.coupons.countLabel')}
            linkTo="/admin/r-point/coupons"
            linkText={t('rpointAdmin:dashboard.modules.coupons.linkText')}
          />
        </ResponsiveGrid>
      </Card>
    </div>
  );
};

export default RPointDashboardPage;
