import { z } from 'zod';
import { ModelBaseStatus } from '../types/model-base.types';

/**
 * <PERSON><PERSON><PERSON> cho cấu hình của model base
 */
export const ModelBaseConfigSchema = z.object({
  hasTopP: z.boolean(),
  hasTopK: z.boolean(),
  hasFunction: z.boolean(),
  hasTemperature: z.boolean(),
  hasText: z.boolean(),
  hasImage: z.boolean(),
  hasAudio: z.boolean(),
  hasVideo: z.boolean(),
  hasParallelToolCall: z.boolean(),
  hasReasoningEffort: z.array(z.string()).optional(),
});

/**
 * Schema cho thông tin model base
 */
export const ModelBaseSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Tên model base không được để trống'),
  description: z.string().optional(),
  providerId: z.string().min(1, '<PERSON>hà cung cấp không được để trống'),
  baseInputRate: z.number().min(0, 'Giá input base phải lớn hơn hoặc bằng 0'),
  baseOutputRate: z.number().min(0, 'Giá output base phải lớn hơn hoặc bằng 0'),
  baseTrainRate: z.number().min(0, 'Giá train base phải lớn hơn hoặc bằng 0'),
  fineTuningInputRate: z.number().min(0, 'Giá input fine-tuning phải lớn hơn hoặc bằng 0'),
  fineTuningOutputRate: z.number().min(0, 'Giá output fine-tuning phải lớn hơn hoặc bằng 0'),
  fineTuningTrainRate: z.number().min(0, 'Giá train fine-tuning phải lớn hơn hoặc bằng 0'),
  tokenCount: z.number().optional(),
  status: z.nativeEnum(ModelBaseStatus).optional(),
  config: ModelBaseConfigSchema,
  createdAt: z.number().optional(),
  updatedAt: z.number().optional(),
});

/**
 * Schema cho tạo model base mới
 */
export const CreateModelBaseSchema = z.object({
  name: z.string().min(1, 'Tên model base không được để trống'),
  description: z.string().optional(),
  providerId: z.string().min(1, 'Nhà cung cấp không được để trống'),
  baseInputRate: z.number().min(0, 'Giá input base phải lớn hơn hoặc bằng 0'),
  baseOutputRate: z.number().min(0, 'Giá output base phải lớn hơn hoặc bằng 0'),
  baseTrainRate: z.number().min(0, 'Giá train base phải lớn hơn hoặc bằng 0'),
  fineTuningInputRate: z.number().min(0, 'Giá input fine-tuning phải lớn hơn hoặc bằng 0'),
  fineTuningOutputRate: z.number().min(0, 'Giá output fine-tuning phải lớn hơn hoặc bằng 0'),
  fineTuningTrainRate: z.number().min(0, 'Giá train fine-tuning phải lớn hơn hoặc bằng 0'),
  config: ModelBaseConfigSchema,
});

/**
 * Schema cho danh sách model base
 */
export const ModelBaseListSchema = z.array(ModelBaseSchema);

/**
 * Schema cho kết quả phân trang
 */
export const PaginatedModelBaseSchema = z.object({
  items: z.array(ModelBaseSchema),
  meta: z.object({
    totalItems: z.number(),
    itemCount: z.number(),
    itemsPerPage: z.number(),
    totalPages: z.number(),
    currentPage: z.number(),
  }),
});
