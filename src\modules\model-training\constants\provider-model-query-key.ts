import { ProviderModelQueryParams } from '../types/provider-model.types';

/**
 * Query keys cho Provider Model
 */
export const ProviderModelQueryKeys = {
  all: ['provider-models'] as const,
  lists: () => [...ProviderModelQueryKeys.all, 'list'] as const,
  list: (filters: ProviderModelQueryParams) => [...ProviderModelQueryKeys.lists(), { filters }] as const,
  details: () => [...ProviderModelQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...ProviderModelQueryKeys.details(), id] as const,
};
