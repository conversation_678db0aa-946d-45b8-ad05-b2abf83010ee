import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Divider,
  FormItem,
  Form,
  Chip,
} from '@/shared/components/common';
import { Product, ProductStatus } from '@/modules/admin/marketplace/types/product.types';

export interface ViewProductFormProps {
  /**
   * Product data to display
   */
  product: Product | null;

  /**
   * Function to handle form close
   */
  onClose: () => void;
}

/**
 * Form component for viewing product details
 */
const ViewProductForm: React.FC<ViewProductFormProps> = ({ product, onClose }) => {
  const { t } = useTranslation(['admin', 'common']);

  if (!product) {
    return (
      <Card>
        <Typography variant="body1">
          {t('admin:marketplace.product.noData', 'Không có dữ liệu sản phẩm')}
        </Typography>
        <div className="flex justify-end mt-4">
          <Button variant="outline" onClick={onClose}>
            {t('common:close', 'Đóng')}
          </Button>
        </div>
      </Card>
    );
  }

  // Get status variant for chip
  const getStatusVariant = (status: ProductStatus) => {
    switch (status) {
      case ProductStatus.APPROVED:
        return 'success';
      case ProductStatus.PENDING:
        return 'warning';
      case ProductStatus.REJECTED:
        return 'danger';
      case ProductStatus.DRAFT:
        return 'default';
      case ProductStatus.DELETED:
        return 'danger';
      default:
        return 'default';
    }
  };

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h6" className="mb-4 flex items-center justify-between">
          <span>{t('admin:marketplace.product.details', 'Chi tiết sản phẩm')}</span>
          <Chip
            size="sm"
            variant={getStatusVariant(product.status) as 'default' | 'primary' | 'success' | 'warning' | 'danger'}
          >
            {t(`admin:marketplace.product.status.${product.status}`, product.status)}
          </Chip>
        </Typography>

        <Form className="space-y-6" onSubmit={() => {}}>
          {/* Basic Information */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:marketplace.product.basicInfo', 'Thông tin cơ bản')}
            </Typography>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem
                name="id"
                label={t('admin:marketplace.product.form.id', 'ID')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.id}
                </div>
              </FormItem>

              <FormItem
                name="name"
                label={t('admin:marketplace.product.form.name', 'Tên sản phẩm')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.name}
                </div>
              </FormItem>

              <FormItem
                name="category"
                label={t('admin:marketplace.product.form.category', 'Loại sản phẩm')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {t(`admin:marketplace.product.category.${product.category}`, product.category)}
                </div>
              </FormItem>

              <FormItem
                name="sourceId"
                label={t('admin:marketplace.product.form.sourceId', 'Source ID')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.sourceId}
                </div>
              </FormItem>
            </div>

            <FormItem
              name="description"
              label={t('admin:marketplace.product.form.description', 'Mô tả')}
            >
              <div className="p-2 bg-card-muted rounded border border-border min-h-[80px]">
                {product.description}
              </div>
            </FormItem>
          </div>

          <Divider />

          {/* Price Information */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:marketplace.product.priceInfo', 'Thông tin giá')}
            </Typography>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem
                name="listedPrice"
                label={t('admin:marketplace.product.form.price', 'Giá gốc')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.listedPrice} points
                </div>
              </FormItem>

              <FormItem
                name="discountedPrice"
                label={t('admin:marketplace.product.form.discountedPrice', 'Giá khuyến mãi')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.discountedPrice} points
                </div>
              </FormItem>
            </div>
          </div>

          <Divider />

          {/* Seller Information */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:marketplace.product.sellerInfo', 'Thông tin người bán')}
            </Typography>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem
                name="sellerName"
                label={t('admin:marketplace.product.form.sellerName', 'Tên người bán')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.seller.name}
                </div>
              </FormItem>

              <FormItem
                name="sellerEmail"
                label={t('admin:marketplace.product.form.sellerEmail', 'Email')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.seller.email || t('common:notAvailable', 'Không có')}
                </div>
              </FormItem>

              <FormItem
                name="sellerPhone"
                label={t('admin:marketplace.product.form.sellerPhone', 'Số điện thoại')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.seller.phoneNumber || t('common:notAvailable', 'Không có')}
                </div>
              </FormItem>

              <FormItem
                name="sellerType"
                label={t('admin:marketplace.product.form.sellerType', 'Loại người bán')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.seller.type}
                </div>
              </FormItem>
            </div>
          </div>

          <Divider />

       


          {/* Documents and Images */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:marketplace.product.documents', 'Tài liệu & Hình ảnh')}
            </Typography>

          
      

            {/* Images */}
            {product.images && product.images.length > 0 && (
              <FormItem
                name="images"
                label={t('admin:marketplace.product.form.images', 'Hình ảnh')}
              >
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {product.images.map((image, index) => (
                    <div key={index} className="border border-border rounded overflow-hidden">
                      <img
                        src={image}
                        alt={`${product.name} - ${index + 1}`}
                        className="w-full h-32 object-cover"
                      />
                    </div>
                  ))}
                </div>
              </FormItem>
            )}
          </div>

          <div className="flex justify-end pt-4">
            <Button variant="outline" onClick={onClose}>
              {t('common:close', 'Đóng')}
            </Button>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default ViewProductForm;
