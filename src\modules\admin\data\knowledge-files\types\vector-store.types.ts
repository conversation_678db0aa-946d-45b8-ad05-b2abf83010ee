import { KnowledgeFileDto } from './knowledge-file.types';

/**
 * Interface đại diện cho một vector store
 */
export interface VectorStoreDto {
  /**
   * ID của vector store
   */
  storeId: string | number;

  /**
   * Tên vector store
   */
  storeName: string;

  /**
   * Dung lượng đã dùng (bytes/tokens)
   */
  size: number;

  /**
   * Số lượng agent sử dụng vector store này
   */
  agents?: number;

  /**
   * Số lượng file trong vector store
   */
  files?: number;

  /**
   * Thời điểm tạo (unix timestamp)
   */
  createdAt: number;

  /**
   * Thời điểm cập nhật (unix timestamp)
   */
  updatedAt: number;
}

/**
 * Interface đại diện cho tham số truy vấn danh sách vector store
 */
export interface VectorStoreQueryParams {
  /**
   * Tên vector store cần tìm kiếm
   */
  search?: string;

  /**
   * Số trang
   */
  page?: number;

  /**
   * <PERSON><PERSON> lượng kết quả trên một trang
   */
  limit?: number;

  /**
   * Trường để sắp xếp
   */
  sortBy?: string;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface đại diện cho dữ liệu tạo vector store mới
 */
export interface CreateVectorStoreDto {
  /**
   * Tên vector store
   */
  name: string;
}

/**
 * Interface đại diện cho dữ liệu gán file vào vector store
 */
export interface AssignFilesToVectorStoreDto {
  /**
   * Mảng các ID file cần gán
   */
  fileIds: string[];
}

/**
 * Interface đại diện cho phản hồi danh sách vector store
 */
export interface VectorStoreListResponse {
  items: VectorStoreDto[];
  meta: {
    totalItems: number;
    currentPage: number;
    itemsPerPage: number;
    totalPages: number;
  };
}

/**
 * Interface đại diện cho phản hồi danh sách file trong vector store
 */
export interface VectorStoreFilesResponse {
  items: KnowledgeFileDto[];
  meta: {
    totalItems: number;
    currentPage: number;
    itemsPerPage: number;
    totalPages: number;
  };
}
