import React, { use<PERSON>emo, useCallback, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, IconCard, Tooltip, Typography } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { TransactionDto, TransactionStatus } from '../types';
import { useTransactionData } from '../hooks';
import { formatCurrency } from '@/shared/utils';
import { formatDateTime } from '@/shared/utils/date';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import TransactionViewForm from '../components/forms/TransactionViewForm';

/**
 * Trang quản lý đơn hàng R-Point
 */
const TransactionListPage: React.FC = () => {
  const { t } = useTranslation(['rpointAdmin', 'common']);

  // Sử dụng hook useSlideForm để quản lý trạng thái hiển thị form
  const { isVisible, showForm, hideForm } = useSlideForm<number | null>();

  // State lưu ID của transaction đang xem
  const [selectedTransactionId, setSelectedTransactionId] = useState<number | null>(null);

  // Xử lý xem chi tiết
  const handleView = useCallback((id: number) => {
    setSelectedTransactionId(id);
    showForm();
  }, [setSelectedTransactionId, showForm]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<TransactionDto>[]>(
    () => [
      { key: 'id', title: t('rpointAdmin:transactions.table.id'), dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'user',
        title: t('rpointAdmin:transactions.table.user'),
        dataIndex: 'user',
        width: '15%',
        sortable: false,
        render: (_value: unknown, record: TransactionDto) => {
          const user = record.user;
          return (
            <div>
              {user ? (
                <>
                  <div className="font-medium">{user.fullName}</div>
                  <div className="text-xs text-gray-500">{user.email}</div>
                </>
              ) : (
                <span className="text-gray-500">ID: {record.userId}</span>
              )}
            </div>
          );
        },
      },
      {
        key: 'amount',
        title: t('rpointAdmin:transactions.table.amount'),
        dataIndex: 'amount',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="text-center">{formatCurrency(value as number)} VND</div>;
        },
      },
      {
        key: 'pointsAmount',
        title: t('rpointAdmin:transactions.table.pointsAmount'),
        dataIndex: 'pointsAmount',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="text-center font-bold text-primary">{value as number}</div>;
        },
      },
      {
        key: 'status',
        title: t('rpointAdmin:transactions.table.status'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as TransactionStatus;
          let statusClass = '';

          switch (status) {
            case TransactionStatus.CONFIRMED:
              statusClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
              break;
            case TransactionStatus.PENDING:
              statusClass = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
              break;
            case TransactionStatus.FAILED:
              statusClass = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
              break;
            case TransactionStatus.REFUNDED:
              statusClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
              break;
          }

          return (
            <div className={`px-2 py-1 rounded-full text-center text-xs font-medium ${statusClass}`}>
              {t(`rpointAdmin:transactions.status.${status}`)}
            </div>
          );
        },
      },
      {
        key: 'paymentMethod',
        title: t('rpointAdmin:transactions.table.paymentMethod'),
        dataIndex: 'paymentMethod',
        width: '10%',
        sortable: true,
      },
      {
        key: 'createdAt',
        title: t('rpointAdmin:transactions.table.createdAt'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return <div>{formatDateTime(value as number)}</div>;
        },
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '10%',
        render: (_: unknown, record: TransactionDto) => (
          <div className="flex space-x-2">
            <Tooltip content={t('common:view')} position="top">
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => handleView(record.id)}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t, handleView]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'pending', label: t('rpointAdmin:transactions.filter.pending'), icon: 'clock', value: TransactionStatus.PENDING },
      { id: 'confirmed', label: t('rpointAdmin:transactions.filter.confirmed'), icon: 'check', value: TransactionStatus.CONFIRMED },
      { id: 'failed', label: t('rpointAdmin:transactions.filter.failed'), icon: 'x', value: TransactionStatus.FAILED },
      { id: 'refunded', label: t('rpointAdmin:transactions.filter.refunded'), icon: 'refresh-ccw', value: TransactionStatus.REFUNDED },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }) => {
    const queryParams: Record<string, unknown> = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      keyword: params.searchTerm || undefined, // Thêm cả keyword để đảm bảo tương thích
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue;
    }

    if (params.dateRange && params.dateRange[0]) {
      queryParams.startTime = params.dateRange[0].getTime();
    }

    if (params.dateRange && params.dateRange[1]) {
      queryParams.endTime = params.dateRange[1].getTime();
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );



  // Lấy hook từ useTransactionData
  const { useTransactions, useTransactionDetail } = useTransactionData();

  // Gọi API lấy danh sách giao dịch với queryParams từ dataTable
  const { data: transactionData, isLoading, refetch } = useTransactions(dataTable.queryParams);

  // Log queryParams và transactionData để debug
  useEffect(() => {
    console.log('Current queryParams:', dataTable.queryParams);
  }, [dataTable.queryParams]);

  // Log transactionData để debug
  useEffect(() => {
    console.log('Transaction data:', transactionData);
    console.log('Meta data:', transactionData?.meta);
    console.log('Total items:', transactionData?.meta?.totalItems);
    console.log('Current page:', transactionData?.meta?.currentPage);
  }, [transactionData]);

  // Gọi API lấy chi tiết giao dịch khi có selectedTransactionId
  const {
    data: transactionDetail,
    isLoading: isLoadingDetail,
    error: transactionError
  } = useTransactionDetail(selectedTransactionId || 0, {
    enabled: !!selectedTransactionId
  });

  // Xử lý đóng form
  const handleCloseForm = () => {
    hideForm();
    // Reset selectedTransactionId sau khi form đóng
    setTimeout(() => {
      setSelectedTransactionId(null);
    }, 300); // Đợi animation hoàn thành
  };

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [TransactionStatus.PENDING]: t('rpointAdmin:transactions.filter.pending'),
      [TransactionStatus.CONFIRMED]: t('rpointAdmin:transactions.filter.confirmed'),
      [TransactionStatus.FAILED]: t('rpointAdmin:transactions.filter.failed'),
      [TransactionStatus.REFUNDED]: t('rpointAdmin:transactions.filter.refunded')
    },
    t,
  });

  return (
    <div className="p-6">
      <Typography variant="h1" className="mb-6">
        {t('rpointAdmin:transactions.title')}
      </Typography>

      <div className="flex justify-between mb-4">
        <MenuIconBar
          onSearch={dataTable.tableData.handleSearch}
          items={dataTable.menuItems}
          onDateRangeChange={dataTable.dateRange.setDateRange}
          onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
          columns={dataTable.columnVisibility.visibleColumns}
          showDateFilter={true}
          showColumnFilter={true}
        />

      </div>

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Slide-in form for transaction details */}
      <SlideInForm isVisible={isVisible} className="w-full">
        <TransactionViewForm
          transaction={transactionDetail || null}
          isLoading={isLoadingDetail}
          error={transactionError}
          onClose={handleCloseForm}
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={transactionData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: transactionData?.meta?.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: transactionData?.meta?.totalItems || 0,
            onChange: (page, pageSize) => {
              console.log(`Changing page to ${page}, pageSize: ${pageSize || dataTable.tableData.pageSize}`);
              console.log('Current meta:', transactionData?.meta);

              // Sử dụng handlePageChange từ dataTable.tableData
              dataTable.tableData.handlePageChange(page, pageSize || dataTable.tableData.pageSize);

              // Thêm timeout để log sau khi state được cập nhật
              setTimeout(() => {
                console.log('After update - queryParams:', dataTable.queryParams);
                refetch();
              }, 0);
            },
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default TransactionListPage;
