import * as z from 'zod';
import { ApiKeyLocation, AuthType, TokenSource } from '../types';

/**
 * Schema cho cấu hình xác thực API Key
 */
export const apiKeyAuthConfigSchema = z.object({
  authType: z.literal(AuthType.API_KEY),
  schemeName: z.string(),
  apiKey: z.string().min(1, 'API Key là bắt buộc'),
  apiKeyLocation: z.nativeEnum(ApiKeyLocation),
  paramName: z.string().min(1, 'Tên tham số là bắt buộc'),
});

/**
 * Schema cho cấu hình xác thực OAuth
 */
export const oauthAuthConfigSchema = z.object({
  authType: z.literal(AuthType.OAUTH),
  schemeName: z.string(),
  token: z.string().min(1, 'Token là bắt buộc'),
  tokenSource: z.nativeEnum(TokenSource),
});

/**
 * Schema cho cấu hình không xác thực
 */
export const noAuthConfigSchema = z.object({
  authType: z.literal(AuthType.NONE),
});

/**
 * Schema cho cấu hình xác thực
 */
export const authConfigSchema = z.discriminatedUnion('authType', [
  apiKeyAuthConfigSchema,
  oauthAuthConfigSchema,
  noAuthConfigSchema,
]);

/**
 * Schema cho tham số tích hợp từ OpenAPI
 */
export const integrateFromOpenApiSchema = z.object({
  openapiSpec: z.record(z.any()),
  baseUrl: z.string().url('URL không hợp lệ').optional(),
  authConfig: authConfigSchema.optional(),
});

/**
 * Schema cho tham số cập nhật xác thực
 */
export const updateToolAuthSchema = z.object({
  toolId: z.string().min(1, 'ID tool là bắt buộc'),
  authConfig: authConfigSchema,
});

/**
 * Schema cho tham số cập nhật base URL
 */
export const updateBaseUrlSchema = z.object({
  toolId: z.string().min(1, 'ID tool là bắt buộc'),
  baseUrl: z.string().url('URL không hợp lệ'),
});
