import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Table, Modal, Chip } from '@/shared/components/common';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ModernMenuTrigger } from '@/shared/components/common/ModernMenu';

// Import hooks từ module media
import {
  useAdminMediaList,
  useDeleteMedia,
  useDeleteMultipleMedia,
} from '@/modules/admin/data/media/hooks/useMedia';

// Import types từ module media
import {
  AdminMediaDto,
  MediaQueryDto,
  MediaStatus,
} from '@/modules/admin/data/media/types/media.types';

/**
 * Trang quản lý media trong admin
 */
const MediaPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const [mediaList, setMediaList] = useState<AdminMediaDto[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [mediaToDelete, setMediaToDelete] = useState<AdminMediaDto | null>(null);
  const [mediaToView, setMediaToView] = useState<AdminMediaDto | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedMediaIds, setSelectedMediaIds] = useState<string[]>([]);
  const [showBatchDeleteConfirm, setShowBatchDeleteConfirm] = useState(false);

  // State cho tìm kiếm
  const [searchTerm, setSearchTerm] = useState('');

  // State cho sắp xếp
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isViewFormVisible,
    showForm: showViewSlideForm,
    hideForm: hideViewForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo<MediaQueryDto>(() => {
    const params: MediaQueryDto = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection === SortDirection.ASC ? 'ASC' : 'DESC',
    };

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection]);

  // Hooks để gọi API
  const {
    data: mediaData,
    isLoading: isLoadingMedia,
    error: mediaError,
  } = useAdminMediaList(queryParams);

  const { mutateAsync: deleteMedia } = useDeleteMedia();
  const { mutateAsync: deleteMultipleMedia } = useDeleteMultipleMedia();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (mediaData) {
      setMediaList(mediaData.items);
      setTotalItems(mediaData.meta.totalItems);
    }

    setIsLoading(isLoadingMedia);
  }, [mediaData, mediaError, isLoadingMedia]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((media: AdminMediaDto) => {
    setMediaToDelete(media);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setMediaToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!mediaToDelete) return;

    try {
      await deleteMedia(mediaToDelete.id);
      setShowDeleteConfirm(false);
      setMediaToDelete(null);
    } catch (error) {
      console.error('Error deleting media:', error);
    }
  }, [mediaToDelete, deleteMedia]);

  // Xử lý hiển thị popup xác nhận xóa hàng loạt
  const handleShowBatchDeleteConfirm = useCallback(() => {
    if (selectedMediaIds.length === 0) return;
    setShowBatchDeleteConfirm(true);
  }, [selectedMediaIds]);

  // Xử lý hủy xóa hàng loạt
  const handleCancelBatchDelete = useCallback(() => {
    setShowBatchDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa hàng loạt
  const handleConfirmBatchDelete = useCallback(async () => {
    if (selectedMediaIds.length === 0) return;

    try {
      await deleteMultipleMedia(selectedMediaIds);
      setShowBatchDeleteConfirm(false);
      setSelectedMediaIds([]);
    } catch (error) {
      console.error('Error batch deleting media:', error);
    }
  }, [selectedMediaIds, deleteMultipleMedia]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý hiển thị form xem chi tiết media
  const handleShowViewForm = useCallback(
    (media: AdminMediaDto) => {
      setMediaToView(media);
      showViewSlideForm();
    },
    [showViewSlideForm]
  );

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'name',
        title: t('admin:data.media.table.name', 'Tên file'),
        dataIndex: 'name',
        width: '20%',
        sortable: true,
      },
      {
        key: 'description',
        title: t('admin:data.media.table.description', 'Mô tả'),
        dataIndex: 'description',
        width: '25%',
      },
      {
        key: 'size',
        title: t('admin:data.media.table.size', 'Kích thước'),
        dataIndex: 'size',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const size = value as number;
          // Chuyển đổi byte sang KB, MB, GB
          if (size < 1024) {
            return `${size} B`;
          } else if (size < 1024 * 1024) {
            return `${(size / 1024).toFixed(2)} KB`;
          } else if (size < 1024 * 1024 * 1024) {
            return `${(size / (1024 * 1024)).toFixed(2)} MB`;
          } else {
            return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
          }
        },
      },
      {
        key: 'author',
        title: t('admin:data.media.table.author', 'Người tạo'),
        dataIndex: 'author',
        width: '15%',
        render: (value: unknown, record: AdminMediaDto) => {
          return (
            <div className="flex flex-col">
              <span>{value as string}</span>
              <span className="text-xs text-gray-500">ID: {record.ownedBy}</span>
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('admin:data.media.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const date = new Date(value as number);
          return date.toLocaleString();
        },
      },
      {
        key: 'status',
        title: t('admin:data.media.table.status', 'Trạng thái'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as MediaStatus;
          let variant: 'default' | 'primary' | 'success' | 'warning' | 'danger' = 'default';

          switch (status) {
            case MediaStatus.ACTIVE:
              variant = 'success';
              break;
            case MediaStatus.PENDING:
              variant = 'warning';
              break;
            case MediaStatus.INACTIVE:
              variant = 'danger';
              break;
            case MediaStatus.DELETED:
              variant = 'danger';
              break;
          }

          return (
            <Chip size="sm" variant={variant}>
              {t(`admin:data.media.status.${status}`, status)}
            </Chip>
          );
        },
      },
      {
        key: 'actions',
        title: t('common.actions', ''),
        render: (_: unknown, record: AdminMediaDto) => {
          const menuItems = [
            {
              label: t('common.view', 'Xem'),
              icon: 'eye',
              onClick: () => handleShowViewForm(record),
            },
            {
              label: t('common.delete', 'Xóa'),
              icon: 'trash',
              onClick: () => handleShowDeleteConfirm(record),
              variant: 'primary' as const,
            },
          ];

          return <ModernMenuTrigger items={menuItems} placement="left" />;
        },
      },
    ];

    // Lọc các cột dựa trên visibleColumns
    if (visibleColumns.length === 0) {
      return allColumns;
    }

    const visibleColumnIds = visibleColumns.filter(col => col.visible).map(col => col.id);

    // Luôn hiển thị cột actions
    return allColumns.filter(col => col.key === 'actions' || visibleColumnIds.includes(col.key));
  }, [t, visibleColumns, handleShowDeleteConfirm, handleShowViewForm]);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: 'Tất cả', visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          <MenuIconBar
            onSearch={handleSearch}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={true}
            showColumnFilter={true}
            items={[]}
          />
        </div>

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {mediaToView && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <Typography variant="h5">
                  {t('admin:data.media.viewMedia', 'Chi tiết media')}
                </Typography>
                <Button variant="outline" onClick={hideViewForm}>
                  {t('common.close', 'Đóng')}
                </Button>
              </div>

              <div className="space-y-4">
                {mediaToView.viewUrl && (
                  <div className="mb-4">
                    <img
                      src={mediaToView.viewUrl}
                      alt={mediaToView.name}
                      className="max-w-full h-auto max-h-64 object-contain"
                    />
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Typography variant="body2" className="text-gray-500">
                      {t('admin:data.media.name', 'Tên file')}
                    </Typography>
                    <Typography>{mediaToView.name}</Typography>
                  </div>

                  <div>
                    <Typography variant="body2" className="text-gray-500">
                      {t('admin:data.media.size', 'Kích thước')}
                    </Typography>
                    <Typography>
                      {mediaToView.size < 1024
                        ? `${mediaToView.size} B`
                        : mediaToView.size < 1024 * 1024
                          ? `${(mediaToView.size / 1024).toFixed(2)} KB`
                          : mediaToView.size < 1024 * 1024 * 1024
                            ? `${(mediaToView.size / (1024 * 1024)).toFixed(2)} MB`
                            : `${(mediaToView.size / (1024 * 1024 * 1024)).toFixed(2)} GB`}
                    </Typography>
                  </div>

                  <div>
                    <Typography variant="body2" className="text-gray-500">
                      {t('admin:data.media.author', 'Người tạo')}
                    </Typography>
                    <Typography>{mediaToView.author}</Typography>
                  </div>

                  <div>
                    <Typography variant="body2" className="text-gray-500">
                      {t('admin:data.media.createdAt', 'Ngày tạo')}
                    </Typography>
                    <Typography>{new Date(mediaToView.createdAt).toLocaleString()}</Typography>
                  </div>

                  <div className="col-span-2">
                    <Typography variant="body2" className="text-gray-500">
                      {t('admin:data.media.description', 'Mô tả')}
                    </Typography>
                    <Typography>{mediaToView.description}</Typography>
                  </div>

                  <div className="col-span-2">
                    <Typography variant="body2" className="text-gray-500">
                      {t('admin:data.media.storageKey', 'Khóa lưu trữ')}
                    </Typography>
                    <Typography className="break-all">{mediaToView.storageKey}</Typography>
                  </div>

                  {mediaToView.tags && mediaToView.tags.length > 0 && (
                    <div className="col-span-2">
                      <Typography variant="body2" className="text-gray-500">
                        {t('admin:data.media.tags', 'Thẻ')}
                      </Typography>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {mediaToView.tags.map((tag, index) => (
                          <Chip key={index} size="sm" variant="default">
                            {tag}
                          </Chip>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<AdminMediaDto>
            columns={filteredColumns}
            data={mediaList}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={handleSortChange}
            defaultSort={{
              column: sortBy || '',
              order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
            rowSelection={{
              selectedRowKeys: selectedMediaIds,
              onChange: selectedRowKeys => {
                setSelectedMediaIds(selectedRowKeys as string[]);
              },
            }}
          />
        </Card>

        {selectedMediaIds.length > 0 && (
          <div className="fixed bottom-4 right-4 bg-white shadow-lg rounded-lg p-4 z-10">
            <div className="flex items-center space-x-4">
              <Typography>
                {t('admin:data.media.selectedItems', 'Đã chọn {{count}} mục', {
                  count: selectedMediaIds.length,
                })}
              </Typography>
              <Button variant="danger" onClick={handleShowBatchDeleteConfirm}>
                {t('common.delete', 'Xóa')}
              </Button>
              <Button variant="outline" onClick={() => setSelectedMediaIds([])}>
                {t('common.cancel', 'Hủy')}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('admin:data.common.confirmDelete', 'Xác nhận xóa')}
        size="sm"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancelDelete}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="danger" onClick={handleConfirmDelete}>
              {t('common.delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography>
            {t('admin:data.media.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa media này?')}
          </Typography>
          {mediaToDelete && (
            <Typography variant="body2" className="mt-2 font-semibold">
              {mediaToDelete.name}
            </Typography>
          )}
        </div>
      </Modal>

      {/* Modal xác nhận xóa hàng loạt */}
      <Modal
        isOpen={showBatchDeleteConfirm}
        onClose={handleCancelBatchDelete}
        title={t('admin:data.common.confirmBatchDelete', 'Xác nhận xóa hàng loạt')}
        size="sm"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancelBatchDelete}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="danger" onClick={handleConfirmBatchDelete}>
              {t('common.delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography>
            {t(
              'admin:data.media.confirmBatchDeleteMessage',
              'Bạn có chắc chắn muốn xóa {{count}} media đã chọn?',
              { count: selectedMediaIds.length }
            )}
          </Typography>
        </div>
      </Modal>
    </div>
  );
};

export default MediaPage;
