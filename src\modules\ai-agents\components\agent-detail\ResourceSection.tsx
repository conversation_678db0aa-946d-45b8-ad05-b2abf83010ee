import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Button,
  Icon,
  Card,
  Checkbox,
  Typography,
  EmptyState,
  Badge,
  Tooltip,
} from '@/shared/components/common';
import { Resource } from '../../types/agent.types';
import { useUpdateAgent } from '../../hooks';

/**
 * Props cho component ResourceSection
 */
interface ResourceSectionProps {
  /**
   * ID của Agent
   */
  agentId: string;

  /**
   * Danh sách tài nguyên của Agent
   */
  resources: Resource[];

  /**
   * Callback khi trạng thái đóng/mở thay đổi
   */
  onToggle?: (isOpen: boolean) => void;
}

/**
 * Component hiển thị tài nguyên của Agent
 */
const ResourceSection: React.FC<ResourceSectionProps> = ({ agentId, resources, onToggle }) => {
  const { t } = useTranslation();
  const [selectedResources, setSelectedResources] = useState<Resource[]>(
    resources.filter(r => r.isSelected)
  );
  const [originalSelectedIds, setOriginalSelectedIds] = useState<string[]>(
    resources.filter(r => r.isSelected).map(r => r.id)
  );
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // Sử dụng hooks
  const { mutate: updateAgent, isPending: isUpdating } = useUpdateAgent();

  // Cập nhật state khi resources thay đổi từ props
  React.useEffect(() => {
    const selectedIds = resources.filter(r => r.isSelected).map(r => r.id);
    setSelectedResources(resources.filter(r => r.isSelected));
    setOriginalSelectedIds(selectedIds);
    setHasChanges(false);
  }, [resources]);

  /**
   * Kiểm tra xem có thay đổi so với dữ liệu ban đầu không
   */
  const checkForChanges = (currentSelectedResources: Resource[] = selectedResources) => {
    const currentSelectedIds = currentSelectedResources.map(r => r.id);

    // So sánh danh sách ID đã chọn hiện tại với ban đầu
    const hasSelectionChanges =
      currentSelectedIds.length !== originalSelectedIds.length ||
      currentSelectedIds.some(id => !originalSelectedIds.includes(id)) ||
      originalSelectedIds.some(id => !currentSelectedIds.includes(id));

    setHasChanges(hasSelectionChanges);
  };

  /**
   * Xử lý chọn/bỏ chọn tài nguyên
   */
  const handleToggleResource = (resource: Resource) => {
    setSelectedResources(prev => {
      const isSelected = prev.some(r => r.id === resource.id);
      const newSelectedResources = isSelected
        ? prev.filter(r => r.id !== resource.id)
        : [...prev, resource];

      // Kiểm tra thay đổi sau khi cập nhật
      setTimeout(() => checkForChanges(newSelectedResources), 0);

      return newSelectedResources;
    });
  };

  /**
   * Xử lý chọn tất cả
   */
  const handleSelectAll = () => {
    const newSelectedResources = selectedResources.length === resources.length
      ? []
      : [...resources];

    setSelectedResources(newSelectedResources);

    // Kiểm tra thay đổi sau khi cập nhật
    setTimeout(() => checkForChanges(newSelectedResources), 0);
  };

  /**
   * Xử lý lưu thay đổi
   */
  const handleSave = () => {
    if (!hasChanges) return;

    const updatedResources = resources.map(resource => ({
      ...resource,
      isSelected: selectedResources.some(r => r.id === resource.id),
    }));

    updateAgent(
      {
        id: agentId,
        data: {
          resources: updatedResources,
        },
      },
      {
        onSuccess: () => {
          // Cập nhật original selected IDs
          setOriginalSelectedIds(selectedResources.map(r => r.id));
          setHasChanges(false);

          // Hiển thị thông báo thành công
          alert(t('aiAgents.resource.saveSuccess', 'Lưu tài nguyên thành công!'));
        },
        onError: (error) => {
          // Hiển thị thông báo lỗi
          console.error('Update resources error:', error);
          alert(t('aiAgents.resource.saveError', 'Có lỗi xảy ra khi lưu tài nguyên. Vui lòng thử lại.'));
        }
      }
    );
  };

  // Xử lý khi đóng/mở card
  const handleCardToggle = (isOpen: boolean) => {
    if (onToggle) {
      onToggle(isOpen);
    }
  };

  // Định dạng kích thước file
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  // Lấy icon cho loại tài nguyên
  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'document':
        return 'file-text';
      case 'image':
        return 'image';
      case 'video':
        return 'video';
      case 'audio':
        return 'music';
      default:
        return 'file';
    }
  };

  // Lấy màu cho loại tài nguyên
  const getResourceColor = (type: string) => {
    switch (type) {
      case 'document':
        return 'blue';
      case 'image':
        return 'green';
      case 'video':
        return 'red';
      case 'audio':
        return 'purple';
      default:
        return 'gray';
    }
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center">
          <Icon name="database" className="mr-2 text-blue-500" />
          <span>{t('aiAgents.resource.title', 'Tài nguyên')}</span>
        </div>
      }
      className="mb-6"
      onToggle={handleCardToggle}
    >
      <div className="mb-4">
        <Typography variant="body1">
          {t(
            'aiAgents.resource.description',
            'Chọn các tài nguyên để Agent có thể sử dụng trong quá trình tương tác với khách hàng.'
          )}
        </Typography>
      </div>

      {/* Danh sách tài nguyên */}
      {resources.length === 0 ? (
        <EmptyState
          icon="database"
          title={t('aiAgents.resource.noResources', 'Chưa có tài nguyên nào')}
          description={t(
            'aiAgents.resource.noResourcesDescription',
            'Hiện tại không có tài nguyên nào khả dụng.'
          )}
          actions={
            <Button
              variant="primary"
              leftIcon={<Icon name="upload" size="sm" />}
            >
              {t('aiAgents.resource.uploadResource', 'Tải lên tài nguyên')}
            </Button>
          }
        />
      ) : (
        <>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <Checkbox
                checked={selectedResources.length === resources.length}
                onChange={handleSelectAll}
                indeterminate={
                  selectedResources.length > 0 && selectedResources.length < resources.length
                }
                label={t('common.selectAll', 'Chọn tất cả')}
              />
              <Typography variant="body2" className="ml-4">
                {t('aiAgents.resource.selected', 'Đã chọn')}: {selectedResources.length}/
                {resources.length}
              </Typography>
            </div>
            <Button
              variant="outline"
              size="sm"
              leftIcon={<Icon name="upload" size="sm" />}
            >
              {t('aiAgents.resource.uploadResource', 'Tải lên tài nguyên')}
            </Button>
          </div>

          <div className="space-y-2 mb-6">
            {resources.map(resource => (
              <Card
                key={resource.id}
                className={`p-3 cursor-pointer transition-colors ${
                  selectedResources.some(r => r.id === resource.id)
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : ''
                }`}
                onClick={() => handleToggleResource(resource)}
              >
                <div className="flex items-center">
                  <Checkbox
                    checked={selectedResources.some(r => r.id === resource.id)}
                    onChange={() => handleToggleResource(resource)}
                    className="mr-3"
                  />
                  <Icon
                    name={getResourceIcon(resource.type)}
                    className={`mr-3 text-${getResourceColor(resource.type)}-500`}
                  />
                  <div className="flex-1 min-w-0">
                    <Typography variant="subtitle1" className="truncate">
                      {resource.name}
                    </Typography>
                    <div className="flex items-center text-gray-500 text-sm">
                      <Badge
                        variant={getResourceColor(resource.type) === 'blue' ? 'primary' : 
                                getResourceColor(resource.type) === 'green' ? 'success' : 
                                getResourceColor(resource.type) === 'red' ? 'danger' : 
                                getResourceColor(resource.type) === 'purple' ? 'info' : 'warning'}
                        className="mr-2"
                      >
                        {resource.type}
                      </Badge>
                      {resource.size && <span>{formatFileSize(resource.size)}</span>}
                    </div>
                  </div>
                  {resource.url && (
                    <Tooltip content={t('common.view', 'Xem')} position="top">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={e => {
                          e.stopPropagation();
                          window.open(resource.url, '_blank');
                        }}
                      >
                        <Icon name="external-link" />
                      </Button>
                    </Tooltip>
                  )}
                </div>
              </Card>
            ))}
          </div>

          <div className="flex justify-end">
            <Button
              variant="primary"
              onClick={handleSave}
              isLoading={isUpdating}
              disabled={!hasChanges}
              leftIcon={<Icon name="save" size="sm" />}
            >
              {t('common.save', 'Lưu')}
            </Button>
          </div>
        </>
      )}
    </CollapsibleCard>
  );
};

export default ResourceSection;
