import { apiClient } from '@/shared/api/axios';
import { CreateDatasetDto, DatasetResponseDto, QueryDatasetDto } from '../types/dataset.types';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';

const API_BASE_URL = '/user/datasets';

/**
 * Tạo dataset mới
 * @param data Dữ liệu để tạo dataset
 * @returns Thông tin dataset đã tạo
 */
export const createDataset = async (data: CreateDatasetDto): Promise<DatasetResponseDto> => {
  const response = await apiClient.post<DatasetResponseDto>(API_BASE_URL, data);
  return response.result;
};

/**
 * Lấy danh sách dataset
 * @param queryDto Tham số truy vấn
 * @returns Danh sách dataset phân trang
 */
export const getDatasets = async (queryDto?: QueryDatasetDto): Promise<PaginatedResult<DatasetResponseDto>> => {
  const queryParams = new URLSearchParams();

  if (queryDto?.page) queryParams.append('page', queryDto.page.toString());
  if (queryDto?.limit) queryParams.append('limit', queryDto.limit.toString());
  if (queryDto?.search) queryParams.append('search', queryDto.search);
  if (queryDto?.sortBy) queryParams.append('sortBy', queryDto.sortBy);
  if (queryDto?.sortDirection) queryParams.append('sortDirection', queryDto.sortDirection);
  if (queryDto?.status) queryParams.append('status', queryDto.status);

  const queryString = queryParams.toString();
  const url = queryString ? `${API_BASE_URL}?${queryString}` : API_BASE_URL;

  const response = await apiClient.get<PaginatedResult<DatasetResponseDto>>(url);
  return response.result;
};

/**
 * Lấy thông tin chi tiết dataset
 * @param id ID của dataset
 * @returns Thông tin chi tiết dataset
 */
export const getDatasetDetail = async (id: string): Promise<DatasetResponseDto> => {
  const response = await apiClient.get<DatasetResponseDto>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * Cập nhật dataset
 * @param id ID của dataset
 * @param data Dữ liệu cập nhật
 * @returns Thông tin dataset đã cập nhật
 */
export const updateDataset = async (id: string, data: Partial<CreateDatasetDto>): Promise<DatasetResponseDto> => {
  const response = await apiClient.put<DatasetResponseDto>(`${API_BASE_URL}/${id}`, data);
  return response.result;
};

/**
 * Xóa dataset
 * @param id ID của dataset
 * @returns Kết quả xóa
 */
export const deleteDataset = async (id: string): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(`${API_BASE_URL}/${id}`);
  return response.result;
};
