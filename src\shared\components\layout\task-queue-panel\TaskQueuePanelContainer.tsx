/**
 * Container để hiển thị TaskQueuePanel trong layout
 */
import React, { useState, useEffect } from 'react';
import { TaskQueuePanel } from '@/shared/components/task-queue';
import { useTaskQueueContext } from '@/shared/contexts/taskQueueContext.hooks';

/**
 * Container để hiển thị TaskQueuePanel trong layout
 */
const TaskQueuePanelContainer: React.FC = () => {
  const { tasks } = useTaskQueueContext();
  const [isPanelVisible, setIsPanelVisible] = useState<boolean>(false);

  // Hiển thị panel khi có task mới được thêm vào
  useEffect(() => {
    if (tasks.length > 0 && !isPanelVisible) {
      setIsPanelVisible(true);
    }
  }, [tasks.length, isPanelVisible]);

  // Nếu panel không hiển thị, không render gì cả
  if (!isPanelVisible) {
    return null;
  }

  return (
    <TaskQueuePanel
      isVisible={isPanelVisible}
      width={360}
      maxHeight={480}
      allowSuperCollapse={true}
      showWhenEmpty={false}
      autoHideWhenEmpty={true}
      autoHideDelay={5000}
      onClose={() => setIsPanelVisible(false)}
    />
  );
};

export default TaskQueuePanelContainer;
