import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toolIntegrationService } from '../services/user-tool-integration.service';
import { BaseQueryParams, IntegrateFromOpenApiParams, UpdateBaseUrlParams, UpdateToolAuthParams } from '../types';

// Định nghĩa các query key
export const TOOL_INTEGRATION_QUERY_KEYS = {
  all: ['tool-integration'] as const,
  tools: () => [...TOOL_INTEGRATION_QUERY_KEYS.all, 'tools'] as const,
  list: (params: BaseQueryParams) => [...TOOL_INTEGRATION_QUERY_KEYS.tools(), params] as const,
  detail: (id: string) => [...TOOL_INTEGRATION_QUERY_KEYS.tools(), id] as const,
};

/**
 * Hook lấy danh sách công cụ tùy chỉnh
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useCustomTools = (params: BaseQueryParams) => {
  return useQuery({
    queryKey: TOOL_INTEGRATION_QUERY_KEYS.list(params),
    queryFn: () => toolIntegrationService.getCustomTools(params),
  });
};

/**
 * Hook lấy chi tiết công cụ tùy chỉnh
 * @param id ID của công cụ
 * @returns Query object
 */
export const useCustomToolDetail = (id: string) => {
  return useQuery({
    queryKey: TOOL_INTEGRATION_QUERY_KEYS.detail(id),
    queryFn: () => toolIntegrationService.getCustomToolDetail(id),
    enabled: !!id,
  });
};

/**
 * Hook tích hợp công cụ từ đặc tả OpenAPI
 * @returns Mutation object
 */
export const useIntegrateFromOpenApi = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (params: IntegrateFromOpenApiParams) => toolIntegrationService.integrateFromOpenApi(params),
    onSuccess: () => {
      // Invalidate và refetch danh sách công cụ tùy chỉnh
      queryClient.invalidateQueries({
        queryKey: TOOL_INTEGRATION_QUERY_KEYS.tools(),
      });
    },
  });
};

/**
 * Hook cập nhật xác thực cho công cụ tùy chỉnh
 * @returns Mutation object
 */
export const useUpdateCustomToolAuth = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (params: UpdateToolAuthParams) => toolIntegrationService.updateCustomToolAuth(params),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết công cụ
      queryClient.invalidateQueries({
        queryKey: TOOL_INTEGRATION_QUERY_KEYS.detail(variables.toolId),
      });
    },
  });
};

/**
 * Hook xóa công cụ tùy chỉnh
 * @returns Mutation object
 */
export const useDeleteCustomTool = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => toolIntegrationService.deleteCustomTool(id),
    onSuccess: (_, id) => {
      // Invalidate và refetch danh sách công cụ tùy chỉnh
      queryClient.invalidateQueries({
        queryKey: TOOL_INTEGRATION_QUERY_KEYS.tools(),
      });
      // Invalidate chi tiết công cụ
      queryClient.invalidateQueries({
        queryKey: TOOL_INTEGRATION_QUERY_KEYS.detail(id),
      });
    },
  });
};

/**
 * Hook cập nhật base URL cho công cụ tùy chỉnh
 * @returns Mutation object
 */
export const useUpdateBaseUrl = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (params: UpdateBaseUrlParams) => toolIntegrationService.updateBaseUrl(params),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết công cụ
      queryClient.invalidateQueries({
        queryKey: TOOL_INTEGRATION_QUERY_KEYS.detail(variables.toolId),
      });
      // Invalidate danh sách công cụ tùy chỉnh
      queryClient.invalidateQueries({
        queryKey: TOOL_INTEGRATION_QUERY_KEYS.tools(),
      });
    },
  });
};
