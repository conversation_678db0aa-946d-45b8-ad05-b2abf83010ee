import { Injectable, Logger } from '@nestjs/common';
import {
  UserProductRepository,
} from '@modules/business/repositories';
import {
  BusinessCreateProductDto,
  BusinessUpdateProductDto,
  ProductResponseDto,
  QueryProductDto,
  ClassificationResponseDto,
} from '../dto';
import { EntityStatusEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { Transactional } from 'typeorm-transactional';
import { PaginatedResult } from '@common/response';
import { UserProductHelper } from '../helpers/user-product.helper';
import { ValidationHelper } from '../helpers/validation.helper';
import { S3Service } from '@shared/services/s3.service';
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { FileSizeEnum, ImageTypeEnum, TimeIntervalEnum } from '@shared/utils';
import { ClassificationService } from './classification.service';

/**
 * Service xử lý logic nghiệp vụ cho sản phẩm của người dùng
 */
@Injectable()
export class UserProductService {
  private readonly logger = new Logger(UserProductService.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly userProductHelper: UserProductHelper,
    private readonly s3Service: S3Service,
    private readonly classificationService: ClassificationService,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Tạo sản phẩm mới
   * @param createProductDto DTO chứa thông tin sản phẩm mới
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã tạo
   */
  @Transactional()
  async createProduct(
    createProductDto: BusinessCreateProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    try {
      // Kiểm tra giá sản phẩm theo loại giá
      this.validationHelper.validateProductPrice(createProductDto.price, createProductDto.typePrice);

      // Tạo sản phẩm mới
      const product = new UserProduct();
      product.name = createProductDto.name;
      product.price = createProductDto.price;
      product.typePrice = createProductDto.typePrice;
      product.description = createProductDto.description || '';
      product.images = []; // Khởi tạo mảng rỗng, sẽ được cập nhật sau khi upload
      product.tags = createProductDto.tags || [];
      product.createdBy = userId;
      product.createdAt = Date.now();
      product.updatedAt = Date.now();
      product.status = EntityStatusEnum.PENDING;
      // Đặt các trường embedding là null để tránh lỗi vector dimension
      product.nameEmbedding = null;
      product.descriptionEmbedding = null;
      product.tagsEmbedding = null;

      // Cấu hình vận chuyển mặc định nếu không được cung cấp
      product.shipmentConfig = createProductDto.shipmentConfig || {
        widthCm: 25,
        heightCm: 5,
        lengthCm: 30,
        weightGram: 200,
      };

      // Xử lý và tạo URL upload cho hình ảnh nếu có
      const imageEntries: Array<{ key: string; position: number }> = [];
      const imagesUploadUrls: Array<{ url: string; key: string; index: number }> = [];
      const now = Date.now();

      if (createProductDto.imagesMediaTypes && createProductDto.imagesMediaTypes.length > 0) {
        for (let i = 0; i < createProductDto.imagesMediaTypes.length; i++) {
          try {
            const mediaType = createProductDto.imagesMediaTypes[i] as ImageTypeEnum;

            // Tạo tên file với vị trí và timestamp
            const fileName = `product-image-${i}-${now}`;

            // Sử dụng phương thức createImageUploadUrl
            const imageUploadUrl = await this.createImageUploadUrl(
              fileName,
              mediaType
            );

            const key = imageUploadUrl.key;
            const url = imageUploadUrl.url;

            this.logger.debug(`Created presigned URL for image upload: ${key} with position ${i}`);

            // Lưu key và position vào mảng để cập nhật database
            imageEntries.push({
              key: key,
              position: i
            });

            imagesUploadUrls.push({
              url: url,
              key: key,
              index: i
            });
          } catch (error) {
            this.logger.error(`Failed to create image upload URL at index ${i}: ${error.message}`, error.stack);
          }
        }
      }

      // Cập nhật danh sách ảnh vào sản phẩm
      product.images = imageEntries;

      // Đã loại bỏ xử lý các trường tùy chỉnh vì không tồn tại trong database

      // Lưu sản phẩm vào database
      const savedProduct = await this.userProductRepository.save(product);

      // Xử lý phân loại sản phẩm nếu có
      let classifications: ClassificationResponseDto[] = [];
      if (createProductDto.classifications && createProductDto.classifications.length > 0) {
        try {
          // Tạo các phân loại cho sản phẩm
          classifications = await Promise.all(
            createProductDto.classifications.map(classificationDto =>
              this.classificationService.create(savedProduct.id, classificationDto, userId)
            )
          );
        } catch (classificationError) {
          // Ghi log nhưng không dừng việc tạo sản phẩm
          this.logger.warn(`Không thể tạo phân loại cho sản phẩm ${savedProduct.id}: ${classificationError.message}`);
        }
      }

      // Chuyển đổi thành DTO response và trả về
      const productResponse = await this.userProductHelper.mapToProductResponseDto(savedProduct);

      // Thêm thông tin URL upload và phân loại vào response
      return {
        ...productResponse,
        uploadUrls: {
          productId: savedProduct.id.toString(),
          imagesUploadUrls
        },
        classifications: classifications.length > 0 ? classifications : undefined
      };
    } catch (error) {
      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log và ném lỗi chung
      this.logger.error(`Lỗi khi tạo sản phẩm: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Lỗi khi tạo sản phẩm: ${error.message}`,
      );
    }
  }

  // Đã loại bỏ phương thức validateAndProcessCustomFields vì không còn cần thiết

  /**
   * Lấy danh sách sản phẩm
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách sản phẩm với phân trang
   */
  async getProducts(
    queryDto: QueryProductDto,
  ): Promise<PaginatedResult<ProductResponseDto>> {
    try {
      // Lấy danh sách sản phẩm từ repository
      const productsResult =
        await this.userProductRepository.findProducts(queryDto);

      // Chuyển đổi từ entity sang DTO
      const items = await Promise.all(
        productsResult.items.map((product) =>
          this.userProductHelper.mapToProductResponseDto(product),
        ),
      );

      // Trả về kết quả
      return {
        items,
        meta: productsResult.meta,
      };
    } catch (error) {
      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log và ném lỗi chung
      this.logger.error(
        `Lỗi khi lấy danh sách sản phẩm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy danh sách sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết sản phẩm theo ID
   * @param id ID của sản phẩm
   * @returns Chi tiết sản phẩm
   */
  async getProductDetail(id: number): Promise<ProductResponseDto> {
    try {
      // Tìm sản phẩm theo ID
      const product = await this.userProductRepository.findById(id);

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${id}`,
        );
      }

      // Chuyển đổi từ entity sang DTO
      const productDto = await this.userProductHelper.mapToProductResponseDto(product);

      // Lấy danh sách phân loại sản phẩm nếu có
      try {
        const classifications = await this.classificationService.getByProductId(id);
        if (classifications && classifications.length > 0) {
          // Thêm danh sách phân loại vào DTO
          productDto['classifications'] = classifications;
        }
      } catch (classificationError) {
        // Ghi log nhưng không dừng việc trả về sản phẩm
        this.logger.warn(`Không thể lấy danh sách phân loại cho sản phẩm ${id}: ${classificationError.message}`);
      }

      return productDto;
    } catch (error) {
      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log và ném lỗi chung
      this.logger.error(
        `Lỗi khi lấy chi tiết sản phẩm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy chi tiết sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật sản phẩm
   * @param id ID của sản phẩm cần cập nhật
   * @param updateProductDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Transactional()
  async updateProduct(
    id: number,
    updateProductDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    try {
      // Kiểm tra giá sản phẩm theo loại giá nếu có cập nhật
      if (updateProductDto.price !== undefined && updateProductDto.typePrice !== undefined) {
        this.validationHelper.validateProductPrice(updateProductDto.price, updateProductDto.typePrice);
      }

      // Tìm sản phẩm theo ID và ID người dùng
      const product = await this.userProductRepository.findByIdAndUserId(
        id,
        userId,
      );

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${id}`,
        );
      }

      // Cập nhật các trường được cung cấp
      if (updateProductDto.name !== undefined) {
        product.name = updateProductDto.name;
      }

      if (updateProductDto.price !== undefined) {
        product.price = updateProductDto.price;
      }

      if (updateProductDto.typePrice !== undefined) {
        product.typePrice = updateProductDto.typePrice;
      }

      if (updateProductDto.description !== undefined) {
        product.description = updateProductDto.description;
      }

      // Xử lý thao tác với ảnh
      const imagesUploadUrls: Array<{ url: string; key: string; index: number }> = [];
      const now = Date.now();

      if (updateProductDto.images && updateProductDto.images.length > 0) {
        // Xử lý các thao tác DELETE trước
        const deleteOperations = updateProductDto.images.filter(img => img.operation === 'DELETE');
        for (const deleteOp of deleteOperations) {
          // Xóa theo key nếu có
          if (deleteOp.key) {
            // Lọc ra các ảnh không bị xóa theo key
            const imageToDelete = product.images.find(img => img.key === deleteOp.key);
            if (imageToDelete) {
              // Xóa ảnh trên S3
              await this.s3Service.deleteFile(imageToDelete.key);
              // Lọc ra các ảnh không bị xóa
              product.images = product.images.filter(img => img.key !== deleteOp.key);
            }
          }
          // Xóa theo position nếu có
          else if (deleteOp.position !== undefined) {
            // Tìm ảnh cần xóa
            const imageToDelete = product.images.find(img => img.position === deleteOp.position);
            if (imageToDelete) {
              // Xóa ảnh trên S3
              await this.s3Service.deleteFile(imageToDelete.key);
              // Lọc ra các ảnh không bị xóa
              product.images = product.images.filter(img => img.position !== deleteOp.position);
            }
          }
        }

        // Xử lý các thao tác ADD
        const addOperations = updateProductDto.images.filter(img => img.operation === 'ADD');
        for (const addOp of addOperations) {
          if (addOp.mimeType) {
            try {
              // Tìm vị trí lớn nhất hiện tại và tăng lên 1 để có vị trí mới
              const maxPosition = product.images.length > 0
                ? Math.max(...product.images.map(img => img.position))
                : -1;
              const newPosition = maxPosition + 1;

              const mediaType = addOp.mimeType as ImageTypeEnum;

              // Tạo tên file với vị trí và timestamp
              const fileName = `product-image-${newPosition}-${now}`;

              // Sử dụng phương thức createImageUploadUrl
              const imageUploadUrl = await this.createImageUploadUrl(
                fileName,
                mediaType
              );

              const key = imageUploadUrl.key;
              const url = imageUploadUrl.url;

              this.logger.debug(`Created presigned URL for image upload: ${key} with position ${newPosition}`);

              // Thêm vào danh sách ảnh của sản phẩm với vị trí mới
              product.images.push({
                key,
                position: newPosition
              });

              // Thêm vào danh sách URL upload
              imagesUploadUrls.push({
                url,
                key,
                index: newPosition
              });
            } catch (error) {
              this.logger.error(`Failed to create image upload URL: ${error.message}`, error.stack);
            }
          }
        }
      }

      if (updateProductDto.tags !== undefined) {
        product.tags = updateProductDto.tags;
      }

      if (updateProductDto.shipmentConfig !== undefined) {
        product.shipmentConfig = updateProductDto.shipmentConfig;
      }

      // Đã loại bỏ xử lý các trường tùy chỉnh vì không tồn tại trong database

      // Đảm bảo các trường embedding vẫn là null để tránh lỗi vector dimension
      product.nameEmbedding = null;
      product.descriptionEmbedding = null;
      product.tagsEmbedding = null;

      // Cập nhật thời gian cập nhật
      product.updatedAt = Date.now();

      // Lưu sản phẩm vào database
      const updatedProduct = await this.userProductRepository.save(product);

      // Xử lý phân loại sản phẩm nếu có
      let classifications: ClassificationResponseDto[] = [];
      if (updateProductDto.classifications && updateProductDto.classifications.length > 0) {
        try {
          // Cập nhật các phân loại cho sản phẩm
          classifications = await Promise.all(
            updateProductDto.classifications.map(classificationDto =>
              this.classificationService.update(classificationDto.id, classificationDto, userId)
            )
          );
        } catch (classificationError) {
          // Ghi log nhưng không dừng việc cập nhật sản phẩm
          this.logger.warn(`Không thể cập nhật phân loại cho sản phẩm ${id}: ${classificationError.message}`);
        }
      }

      // Chuyển đổi thành DTO response và trả về
      const productResponse = await this.userProductHelper.mapToProductResponseDto(updatedProduct);

      // Thêm thông tin URL upload và phân loại vào response
      const response: any = { ...productResponse };

      // Thêm URL upload nếu có
      if (imagesUploadUrls.length > 0) {
        response.uploadUrls = {
          productId: updatedProduct.id.toString(),
          imagesUploadUrls
        };
      }

      // Thêm phân loại nếu có
      if (classifications.length > 0) {
        response.classifications = classifications;
      }

      return response;
    } catch (error) {
      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log và ném lỗi chung
      this.logger.error(
        `Lỗi khi cập nhật sản phẩm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Tạo presigned URL cho việc tải lên hình ảnh sản phẩm
   * @param fileName Tên file
   * @param mediaType Loại media
   * @returns Thông tin URL và key
   */
  private async createImageUploadUrl(
    fileName: string,
    mediaType: ImageTypeEnum = ImageTypeEnum.PNG,
  ): Promise<{ key: string; url: string }> {
    try {
      // Tạo S3 key cho hình ảnh sản phẩm
      // Sử dụng cấu trúc thư mục giống với knowledge files và marketplace
      const key = generateS3Key({
        baseFolder: 'business',
        categoryFolder: CategoryFolderEnum.IMAGE,
        fileName: fileName || 'product-image',
        useTimeFolder: true,
      });

      // Tạo presigned URL
      const url = await this.s3Service.createPresignedWithID(
        key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        mediaType,
        FileSizeEnum.FIVE_MB,
      );

      return { key, url };
    } catch (error) {
      this.logger.error(`Lỗi khi tạo URL upload hình ảnh: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Lỗi khi tạo URL upload hình ảnh: ${error.message}`,
      );
    }
  }

  /**
   * Xóa sản phẩm (soft delete)
   * @param id ID của sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   */
  @Transactional()
  async deleteProduct(id: number, userId: number): Promise<void> {
    try {
      // Tìm sản phẩm theo ID và ID người dùng
      const product = await this.userProductRepository.findByIdAndUserId(
        id,
        userId,
      );

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${id}`,
        );
      }

      // Cập nhật trạng thái sản phẩm thành DELETED
      product.status = EntityStatusEnum.DELETED;
      product.updatedAt = Date.now();

      // Lưu sản phẩm vào database
      await this.userProductRepository.save(product);
    } catch (error) {
      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log và ném lỗi chung
      this.logger.error(`Lỗi khi xóa sản phẩm: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_DELETION_FAILED,
        `Lỗi khi xóa sản phẩm: ${error.message}`,
      );
    }
  }
}