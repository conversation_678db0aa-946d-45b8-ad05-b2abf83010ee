import React from 'react';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormItem,
  Input,
  Button,
} from '@/shared/components/common';
import { ProviderType } from '../types/provider.types';
import { CreateProviderModelDto, UpdateProviderModelDto } from '../types/provider-model.types';

interface ProviderModelFormProps {
  initialValues?: Partial<CreateProviderModelDto>;
  onSubmit: (data: CreateProviderModelDto | UpdateProviderModelDto) => void;
  isLoading?: boolean;
  isUpdate?: boolean;
}

/**
 * Component form tạo/chỉnh sửa provider model
 */
const ProviderModelForm: React.FC<ProviderModelFormProps> = ({
  initialValues,
  onSubmit,
  isLoading = false,
  isUpdate = false,
}) => {
  // Sử dụng kiểu dữ liệu phù hợp với trạng thái hiện tại
  const form = useForm({
    defaultValues: {
      name: initialValues?.name || '',
      type: initialValues?.type || ProviderType.OPENAI,
      apiKey: initialValues?.apiKey || '',
    },
  });

  const handleSubmit = (formValues: unknown) => {
    // Chuyển đổi kiểu dữ liệu
    const data = formValues as Record<string, unknown>;

    // Tạo bản sao của dữ liệu để tránh thay đổi trực tiếp
    const formData = { ...data };

    // Đảm bảo trường type luôn được gửi đi khi tạo mới
    if (!isUpdate && !formData.type) {
      formData.type = initialValues?.type || ProviderType.OPENAI;
    }

    // Chuyển đổi kiểu dữ liệu phù hợp trước khi gửi đi
    if (isUpdate) {
      // Chỉ lấy các trường cần thiết cho UpdateProviderModelDto
      const updateData: UpdateProviderModelDto = {
        name: formData.name as string,
        apiKey: formData.apiKey as string
      };
      onSubmit(updateData);
    } else {
      // Đảm bảo đủ các trường cho CreateProviderModelDto
      const createData: CreateProviderModelDto = {
        name: formData.name as string,
        apiKey: formData.apiKey as string,
        type: formData.type as ProviderType
      };
      onSubmit(createData);
    }
  };

  return (
    <Form onSubmit={data => {
      handleSubmit(data);
      return false;
    }}>
      <div className="space-y-4">
        <FormItem
          name="name"
          label="Tên Provider Model"
          required
        >
          <Input
            placeholder="Nhập tên provider model"
            {...form.register('name', { required: 'Tên provider model không được để trống' })}
            helperText={form.formState.errors.name?.message}
            fullWidth
          />
        </FormItem>

        <FormItem
          name="apiKey"
          label="API Key"
          required
        >
          <Input
            placeholder="Nhập API Key"
            {...form.register('apiKey', { required: 'API key không được để trống' })}
            helperText={form.formState.errors.apiKey?.message}
            type="password"
            fullWidth
          />
        </FormItem>

        <div className="flex justify-end pt-4">
          <Button type="submit" isLoading={isLoading}>
            {isUpdate ? 'Cập nhật' : 'Tích hợp'}
          </Button>
        </div>
      </div>
    </Form>
  );
};

export default ProviderModelForm;
