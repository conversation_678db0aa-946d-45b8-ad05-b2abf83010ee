import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho các tham số truy vấn danh sách trường tùy chỉnh
 */
export class QueryCustomFieldDto {
  /**
   * Trang hiện tại
   * @example 1
   */
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number = 1;

  /**
   * Số lượng bản ghi trên một trang
   * @example 10
   */
  @ApiProperty({
    description: 'Số lượng bản ghi trên một trang',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number = 10;

  /**
   * Vị trí bắt đầu lấy dữ liệu
   * @example 0
   */
  @ApiProperty({
    description: 'Vị trí bắt đầu lấy dữ liệu',
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  offset?: number = 0;

  /**
   * ID người dùng để lọc
   * @example 1001
   */
  @ApiProperty({
    description: 'ID người dùng để lọc',
    example: 1001,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;

  /**
   * ID nhân viên để lọc
   * @example 101
   */
  @ApiProperty({
    description: 'ID nhân viên để lọc',
    example: 101,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  employeeId?: number;

  /**
   * Loại trường để lọc
   * @example "text"
   */
  @ApiProperty({
    description: 'Loại trường để lọc',
    example: 'text',
    required: false,
  })
  @IsOptional()
  @IsString()
  type?: string;

  /**
   * Sắp xếp theo thời gian tạo
   * @example "DESC"
   */
  @ApiProperty({
    description: 'Sắp xếp theo thời gian tạo',
    example: 'DESC',
    enum: ['ASC', 'DESC'],
    required: false,
  })
  @IsOptional()
  @IsString()
  sort?: 'ASC' | 'DESC' = 'DESC';
}
