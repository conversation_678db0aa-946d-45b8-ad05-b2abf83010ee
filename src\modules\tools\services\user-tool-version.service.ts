import { apiClient } from '@/shared/api/axios';
import { UserToolVersion, EditUserToolVersionParams } from '../types';

/**
 * Service xử lý các API liên quan đến phiên bản tool của người dùng
 */
export class UserToolVersionService {
  private baseUrl = '/user/tools';

  /**
   * L<PERSON>y thông tin chi tiết phiên bản tool
   * @param toolId ID của tool
   * @param versionId ID của phiên bản
   * @returns Thông tin chi tiết phiên bản
   */
  async getVersionById(toolId: string, versionId: string): Promise<UserToolVersion> {
    try {
      const response = await apiClient.get<UserToolVersion>(
        `${this.baseUrl}/${toolId}/versions/${versionId}`,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error(`Error fetching version ${versionId} for tool ${toolId}:`, error);
      throw error;
    }
  }

  /**
   * Chỉnh sửa phiên bản tool
   * @param toolId ID của tool
   * @param versionId ID của phiên bản
   * @param data Dữ liệu chỉnh sửa
   * @returns ID của phiên bản mới
   */
  async editVersion(
    toolId: string,
    versionId: string,
    data: EditUserToolVersionParams
  ): Promise<string> {
    try {
      const response = await apiClient.post<{ id: string }>(
        `${this.baseUrl}/${toolId}/versions/${versionId}/edit`,
        data,
        { tokenType: 'user' }
      );
      return response.result.id;
    } catch (error) {
      console.error(`Error editing version ${versionId} for tool ${toolId}:`, error);
      throw error;
    }
  }

  /**
   * Đặt phiên bản mặc định cho tool
   * @param toolId ID của tool
   * @param versionId ID của phiên bản
   * @returns Kết quả cập nhật
   */
  async setDefaultVersion(toolId: string, versionId: string): Promise<boolean> {
    try {
      const response = await apiClient.post<{ success: boolean }>(
        `${this.baseUrl}/${toolId}/versions/${versionId}/set-default`,
        {},
        { tokenType: 'user' }
      );
      return response.result.success;
    } catch (error) {
      console.error(`Error setting default version ${versionId} for tool ${toolId}:`, error);
      throw error;
    }
  }

  /**
   * Xóa phiên bản tool
   * @param toolId ID của tool
   * @param versionId ID của phiên bản
   * @returns Kết quả xóa
   */
  async deleteVersion(toolId: string, versionId: string): Promise<boolean> {
    try {
      const response = await apiClient.delete<{ success: boolean }>(
        `${this.baseUrl}/${toolId}/versions/${versionId}`,
        { tokenType: 'user' }
      );
      return response.result.success;
    } catch (error) {
      console.error(`Error deleting version ${versionId} for tool ${toolId}:`, error);
      throw error;
    }
  }
}

export const userToolVersionService = new UserToolVersionService();
