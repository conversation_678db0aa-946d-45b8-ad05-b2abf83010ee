{"modelTraining": {"provider": {"title": "<PERSON><PERSON><PERSON> hợp nhà cung cấp", "list": "<PERSON><PERSON> s<PERSON>ch nhà cung cấp", "add": "<PERSON><PERSON><PERSON><PERSON> nhà cung cấp", "edit": "Chỉnh sửa nhà cung cấp", "delete": "<PERSON>óa nhà cung cấp", "form": {"name": "<PERSON><PERSON><PERSON> nhà cung cấp", "type": "Loại nhà cung cấp", "apiKey": "API Key", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên nhà cung cấp", "typePlaceholder": "<PERSON><PERSON><PERSON> lo<PERSON>i nhà cung cấp", "apiKeyPlaceholder": "Nhập API Key"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> nhà cung cấp thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nhật nhà cung cấp thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> nhà cung cấp thành công", "createError": "Lỗi khi tạo nhà cung cấp", "updateError": "Lỗi khi cập nhật nhà cung cấp", "deleteError": "Lỗi khi xóa nhà cung cấp", "confirmDelete": "Bạn có chắc chắn muốn xóa nhà cung cấp này? Hành động này không thể hoàn tác."}, "empty": {"title": "Chưa có nhà cung cấp nào", "description": "<PERSON><PERSON>n chưa thêm nhà cung cấp nào. <PERSON><PERSON><PERSON> thêm nhà cung cấp để bắt đầu."}}, "modelBase": {"title": "Tạo Model Base", "list": "<PERSON><PERSON> s<PERSON> Base", "add": "Thêm Model Base", "edit": "Chỉnh sửa Model Base", "delete": "Xóa Model Base", "form": {"name": "Tên Model Base", "providerId": "<PERSON><PERSON><PERSON> cung cấp", "pricing": "Giá", "input": "Input", "output": "Output", "train": "Train", "base": "Base", "fineTuning": "Fine-tuning", "config": "<PERSON><PERSON><PERSON> h<PERSON>nh", "topP": "Top P", "topK": "Top K", "tool": "Tool", "temperature": "Temperature", "text": "Text", "image": "Image", "audio": "Audio", "video": "Video", "toolCall": "Tool call", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên Model Base", "providerPlaceholder": "<PERSON><PERSON><PERSON> nhà cung cấp"}, "messages": {"createSuccess": "Tạo Model Base thành công", "updateSuccess": "<PERSON><PERSON>p nhật Model Base thành công", "deleteSuccess": "Xóa Model Base thành công", "createError": "Lỗi khi tạo Model Base", "updateError": "Lỗi khi cập nh<PERSON>t Model Base", "deleteError": "Lỗi khi xóa Model Base", "confirmDelete": "Bạn có chắc chắn muốn xóa Model Base này? Hành động này không thể hoàn tác."}, "empty": {"title": "Chưa có Model Base nào", "description": "Bạn chưa thêm Model Base nào. <PERSON><PERSON>y thêm Model Base để bắt đầu."}}, "dataset": {"title": "Dataset Fine-tuning Model", "list": "<PERSON><PERSON>", "add": "Thêm Dataset", "edit": "Chỉnh sửa Dataset", "delete": "Xóa Dataset", "form": {"name": "Tên Dataset", "description": "<PERSON><PERSON>", "trainData": "<PERSON><PERSON> liệu hu<PERSON>n luy<PERSON>n", "validationData": "<PERSON><PERSON> liệu kiểm tra", "import": "Import", "systemRole": "System Role", "userRole": "User Role", "assistantRole": "Assistant Role", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên <PERSON>set", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả <PERSON>", "messagePlaceholder": "<PERSON><PERSON><PERSON><PERSON> nội dung tin nhắn...", "send": "<PERSON><PERSON><PERSON>", "selectRole": "<PERSON><PERSON><PERSON> role"}, "messages": {"createSuccess": "Tạo Dataset thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nhật Dataset thành công", "deleteSuccess": "Xóa Dataset thành công", "createError": "Lỗi khi tạo Dataset", "updateError": "Lỗi khi cập nhật Dataset", "deleteError": "Lỗi khi xóa Dataset", "confirmDelete": "Bạn có chắc chắn muốn xóa Dataset này? Hành động này không thể hoàn tác."}, "empty": {"title": "Chưa có Dataset nào", "description": "Bạn chưa thêm Dataset nào. <PERSON><PERSON><PERSON> thêm Dataset để bắt đầu."}}}}