import { Controller, Get, Post, Put, Delete, Param, Body, UseGuards, Logger, ParseIntPipe, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiCreatedResponse, ApiExtraModels, ApiOkResponse, ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { ClassificationService } from '@modules/business/user/services';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { CurrentUser } from '@modules/auth/decorators';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ErrorCode } from '@common/exceptions';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { SkipValidation } from '../decorators';
import {
  CreateClassificationDto,
  UpdateClassificationDto,
  ClassificationResponseDto,
  ClassificationCustomFieldDto,
  ClassificationPriceDto,
  ClassificationValueDto
} from '../dto';

/**
 * Controller xử lý các endpoint liên quan đến phân loại sản phẩm
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS)
@Controller('user/classifications')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  ClassificationResponseDto,
  CreateClassificationDto,
  UpdateClassificationDto,
  ClassificationCustomFieldDto,
  ClassificationPriceDto,
  ClassificationValueDto
)
export class ClassificationController {
  private readonly logger = new Logger(ClassificationController.name);

  constructor(private readonly classificationService: ClassificationService) {}

  /**
   * Tạo phân loại mới cho sản phẩm
   * @param productId ID của sản phẩm
   * @param createDto DTO chứa thông tin phân loại
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin phân loại đã tạo
   */
  @Post(':productId')
  @ApiOperation({ summary: 'Tạo phân loại mới cho sản phẩm' })
  @ApiParam({
    name: 'productId',
    description: 'ID của sản phẩm cần tạo phân loại',
    type: 'number',
    example: 123
  })
  @ApiCreatedResponse({
    description: 'Phân loại đã được tạo thành công',
    schema: ApiResponseDto.getSchema(ClassificationResponseDto),
  })
  @ApiBody({
    type: CreateClassificationDto,
    description: 'Thông tin tạo phân loại mới',
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CLASSIFICATION_CREATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async create(
    @Param('productId', ParseIntPipe) productId: number,
    @SkipValidation() requestBody: any,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<ClassificationResponseDto>> {
    try {
      this.logger.log(`Tạo phân loại mới cho sản phẩm ${productId}, data=${JSON.stringify(requestBody)}, userId=${userId}`);

      // Chuyển đổi dữ liệu đầu vào thành DTO
      const createDto: CreateClassificationDto = {
        type: requestBody.type,
        price: requestBody.price,
        customFields: requestBody.customFields
      };

      const result = await this.classificationService.create(productId, createDto, userId);
      return ApiResponseDto.created(result, 'Phân loại đã được tạo thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi tạo phân loại: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật phân loại
   * @param id ID của phân loại
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin phân loại đã cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật phân loại' })
  @ApiParam({
    name: 'id',
    description: 'ID của phân loại cần cập nhật',
    type: 'number',
    example: 123
  })
  @ApiOkResponse({
    description: 'Phân loại đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(ClassificationResponseDto),
  })
  @ApiBody({
    type: UpdateClassificationDto,
    description: 'Thông tin cập nhật phân loại',
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CLASSIFICATION_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async update(
    @Param('id', ParseIntPipe) id: number,
    @SkipValidation() requestBody: any,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<ClassificationResponseDto>> {
    try {
      this.logger.log(`Cập nhật phân loại với ID ${id}, data=${JSON.stringify(requestBody)}, userId=${userId}`);

      // Chuyển đổi dữ liệu đầu vào thành DTO
      const updateDto: UpdateClassificationDto = {
        id,
        type: requestBody.type,
        price: requestBody.price,
        customFields: requestBody.customFields
      };

      // Kiểm tra quyền sở hữu trước khi cập nhật
      const result = await this.classificationService.update(id, updateDto, userId);
      return ApiResponseDto.success(result, 'Phân loại đã được cập nhật thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật phân loại: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa phân loại
   * @param id ID của phân loại
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa phân loại' })
  @ApiParam({
    name: 'id',
    description: 'ID của phân loại cần xóa',
    type: 'number',
    example: 123
  })
  @ApiOkResponse({
    description: 'Phân loại đã được xóa thành công',
    schema: ApiResponseDto.getSchema(Object),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CLASSIFICATION_DELETION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.PRODUCT_UNAUTHORIZED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async delete(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<null>> {

    try {
      this.logger.log(`Xóa phân loại với ID ${id}, userId=${userId}`);

      await this.classificationService.delete(id, userId);
      return ApiResponseDto.success(null, 'Phân loại đã được xóa thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi xóa phân loại: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách phân loại theo ID sản phẩm
   * @param productId ID của sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Danh sách phân loại
   */
  @Get('product/:productId')
  @ApiOperation({ summary: 'Lấy danh sách phân loại theo ID sản phẩm' })
  @ApiParam({
    name: 'productId',
    description: 'ID của sản phẩm cần lấy danh sách phân loại',
    type: 'number',
    example: 123
  })
  @ApiOkResponse({
    description: 'Danh sách phân loại',
    schema: ApiResponseDto.getSchema([ClassificationResponseDto]),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CLASSIFICATION_FIND_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async getByProductId(
    @Param('productId', ParseIntPipe) productId: number,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<ClassificationResponseDto[]>> {
    try {
      this.logger.log(`Lấy danh sách phân loại cho sản phẩm ${productId}, userId=${userId}`);

      const result = await this.classificationService.getByProductId(productId);
      return ApiResponseDto.success(result, 'Lấy danh sách phân loại thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách phân loại: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy chi tiết phân loại theo ID
   * @param id ID của phân loại
   * @returns Chi tiết phân loại
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết phân loại theo ID' })
  @ApiParam({
    name: 'id',
    description: 'ID của phân loại cần lấy chi tiết',
    type: 'number',
    example: 123
  })
  @ApiOkResponse({
    description: 'Chi tiết phân loại',
    schema: ApiResponseDto.getSchema(ClassificationResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CLASSIFICATION_FIND_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async getById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<ClassificationResponseDto>> {
    try {
      this.logger.log(`Lấy chi tiết phân loại với ID ${id}, userId=${userId}`);

      const result = await this.classificationService.getById(id);
      return ApiResponseDto.success(result, 'Lấy chi tiết phân loại thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết phân loại: ${error.message}`, error.stack);
      throw error;
    }
  }
}
