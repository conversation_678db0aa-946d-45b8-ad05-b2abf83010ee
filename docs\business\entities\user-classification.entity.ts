import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { UserProduct } from './user-product.entity';
import { CustomFieldClassification } from './custom-field-classification.entity';

/**
 * Entity đại diện cho bảng user_classifications trong cơ sở dữ liệu
 * Bảng quản lý phân loại sản phẩm của người dùng
 */
@Entity('user_classifications')
export class UserClassification {
  /**
   * ID của phân loại
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Loại phân loại
   */
  @Column({ name: 'type', type: 'text', nullable: false, comment: 'Loại phân loại' })
  type: string;

  /**
   * Giá của phân loại (dưới dạng JSON)
   */
  @Column({ name: 'price', type: 'jsonb', nullable: true, comment: '<PERSON><PERSON><PERSON> của phân loại (dưới dạng JSON)' })
  price: any;

  /**
   * ID sản phẩm
   */
  @Column({ name: 'product_id', type: 'integer', nullable: true, comment: 'ID sản phẩm' })
  productId: number;
}
