import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { ADMIN_WAREHOUSE_ERROR_CODES } from '../exceptions/warehouse.exception';
import {
  WarehouseRepository,
  PhysicalWarehouseRepository,
  WarehouseCustomFieldRepository
} from '@modules/business/repositories';
import {
  QueryPhysicalWarehouseDto,
  PhysicalWarehouseResponseDto,
  PhysicalWarehouseDetailResponseDto
} from '../dto/warehouse';
import { PaginatedResult } from '@common/response';
import { WarehouseValidationHelper } from '@modules/business/admin/helpers';
import { WarehouseTypeEnum } from '@modules/business/enums';

/**
 * Service xử lý logic nghiệp vụ cho kho vật lý
 */
@Injectable()
export class AdminPhysicalWarehouseService {
  private readonly logger = new Logger(AdminPhysicalWarehouseService.name);

  constructor(
    private readonly warehouseRepository: WarehouseRepository,
    private readonly physicalWarehouseRepository: PhysicalWarehouseRepository,
    private readonly warehouseCustomFieldRepository: WarehouseCustomFieldRepository,
    private readonly warehouseValidationHelper: WarehouseValidationHelper
  ) {}

  /**
   * Lấy danh sách kho vật lý với phân trang
   * @param queryDto DTO truy vấn
   * @returns Danh sách kho vật lý với phân trang
   */
  async findAll(queryDto: QueryPhysicalWarehouseDto): Promise<PaginatedResult<PhysicalWarehouseResponseDto>> {
    this.logger.log(`Lấy danh sách kho vật lý với phân trang: ${JSON.stringify(queryDto)}`);

    try {
      // Lấy danh sách kho vật lý từ repository
      const [physicalWarehouses, total] = await this.physicalWarehouseRepository.findAllWithPagination(queryDto);

      // Chuyển đổi dữ liệu thành DTO sử dụng constructor
      const items = physicalWarehouses.map(warehouse => new PhysicalWarehouseResponseDto(warehouse));

      // Trả về kết quả với phân trang
      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách kho vật lý: ${error.message}`, error.stack);
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_FIND_FAILED,
        'Lỗi khi lấy danh sách kho vật lý'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết kho vật lý theo ID
   * @param warehouseId ID của kho
   * @returns Thông tin chi tiết kho vật lý
   */
  async findOne(warehouseId: number): Promise<PhysicalWarehouseDetailResponseDto> {
    this.logger.log(`Lấy thông tin chi tiết kho vật lý với ID: ${warehouseId}`);

    // Lấy thông tin kho
    const warehouse = await this.warehouseRepository.findByWarehouseId_admin(warehouseId);
    this.warehouseValidationHelper.validateWarehouseExists(warehouse);

    // Kiểm tra loại kho
    this.warehouseValidationHelper.validateWarehouseType(warehouse, WarehouseTypeEnum.PHYSICAL);

    // Lấy thông tin kho vật lý
    const physicalWarehouse = await this.physicalWarehouseRepository.findByWarehouseIdWithWarehouse(warehouseId);
    this.warehouseValidationHelper.validatePhysicalWarehouseExists(physicalWarehouse);

    try {
      // Lấy danh sách trường tùy chỉnh của kho
      const customFields = await this.warehouseCustomFieldRepository.findByWarehouseId_admin(warehouseId);

      // Tạo response sử dụng constructor
      const response = new PhysicalWarehouseDetailResponseDto({
        ...physicalWarehouse,
        customFields: customFields || []
      });

      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin chi tiết kho vật lý: ${error.message}`, error.stack);
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_FIND_FAILED,
        'Lỗi khi lấy thông tin chi tiết kho vật lý'
      );
    }
  }
}
