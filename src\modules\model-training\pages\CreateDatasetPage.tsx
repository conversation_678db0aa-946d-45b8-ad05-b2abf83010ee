import React from 'react';
import { Container } from '@/shared/components/common';
import { DatasetForm } from '../components';
import { useNavigate } from 'react-router-dom';

/**
 * Trang tạo dataset
 */
const CreateDatasetPage: React.FC = () => {
  const navigate = useNavigate();

  const handleSuccess = () => {
    // Chuyển hướng đến trang danh sách dataset sau khi tạo thành công
    navigate('/model-training/datasets');
  };

  return (
    <Container>
      <DatasetForm onSuccess={handleSuccess} />
    </Container>
  );
};

export default CreateDatasetPage;
