import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  CustomFieldService,
  CustomGroupFormService,
  UserProductService,
  ClassificationService
} from '.';

import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import {
  CreateIntegratedBusinessDto, DeleteIntegratedBusinessDto,
  IntegratedBusinessResponseDto, UpdateIntegratedBusinessDto,
  CreateIntegratedBusinessWithoutClassificationDto, IntegratedBusinessWithoutClassificationResponseDto
} from '@modules/business/user/dto/business-integrated.dto';
import { CustomFieldResponseDto, ProductResponseDto, CustomGroupFormCreatedResponseDto, CustomGroupFormResponseDto, CreateCustomFieldDto, UpdateCustomGroupFormDto, ClassificationResponseDto, CreateClassificationDto } from '@modules/business/user/dto';

@Injectable()
export class BusinessIntegrationService {
  private readonly logger = new Logger(BusinessIntegrationService.name);

  constructor(
    private readonly customFieldService: CustomFieldService,
    private readonly customGroupFormService: CustomGroupFormService,
    private readonly userProductService: UserProductService,
    private readonly classificationService: ClassificationService,
  ) {}

  /**
   * Tạo tích hợp business mới bao gồm sản phẩm, nhóm trường và các trường tùy chỉnh
   * @param createDto DTO chứa thông tin tích hợp
   * @param userId ID người dùng hiện tại
   * @returns Thông tin tích hợp đã tạo
   */
  @Transactional()
  async create(
    createDto: CreateIntegratedBusinessDto,
    userId: number,
  ): Promise<IntegratedBusinessResponseDto> {
    try {
      this.logger.log(`Tạo tích hợp business mới cho userId=${userId}`);

      // 1. Tạo sản phẩm trước
      const productResponse = await this.userProductService.createProduct(
        createDto.product,
        userId,
      );

      // 2. Tạo nhóm trường với productId
      const groupFormDto = {
        ...createDto.groupForm,
        userId,
        productId: productResponse.id,
      };
      const groupFormResponse = await this.customGroupFormService.create(groupFormDto);

      // 3. Tạo các trường tùy chỉnh và liên kết với nhóm
      const customFieldResponses: CustomFieldResponseDto[] = [];

      if (createDto.customFields && createDto.customFields.length > 0) {
        for (const fieldDto of createDto.customFields) {
          // Thêm các trường bắt buộc nếu chưa có
          const customFieldDto: CreateCustomFieldDto = {
            // Đảm bảo có các trường bắt buộc
            component: fieldDto.component || 'Text Input',
            configId: fieldDto.configId, // Sẽ được tự động tạo nếu không được cung cấp
            label: fieldDto.label || 'Trường tùy chỉnh',
            type: fieldDto.type || 'text',
            required: fieldDto.required ?? false,
            configJson: fieldDto.configJson || {},

            // Thêm trường liên kết
            userId,
            formGroupId: groupFormResponse.id,

            // Thêm các trường tùy chọn nếu có
            grid: fieldDto.grid,
            value: fieldDto.value,
          };

          const customField = await this.customFieldService.create(customFieldDto);
          customFieldResponses.push(customField);
        }
      }

      // 4. Tạo các phân loại sản phẩm nếu có
      const classificationResponses: ClassificationResponseDto[] = [];

      if (createDto.classifications && createDto.classifications.length > 0) {
        for (const classificationDto of createDto.classifications) {
          const classification = await this.classificationService.create(
            productResponse.id,
            classificationDto,
            userId,
          );
          classificationResponses.push(classification);
        }
      }

      return {
        product: productResponse,
        groupForm: groupFormResponse,
        customFields: customFieldResponses,
        classifications: classificationResponses,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tạo tích hợp business: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.INTEGRATION_CREATION_FAILED,
        `Lỗi khi tạo tích hợp business: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật tích hợp business
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID người dùng hiện tại
   * @returns Thông tin tích hợp đã cập nhật
   */
  @Transactional()
  async update(
    updateDto: UpdateIntegratedBusinessDto,
    userId: number,
  ): Promise<IntegratedBusinessResponseDto> {
    try {
      this.logger.log(`Cập nhật tích hợp business cho userId=${userId}`);

      let productResponse: ProductResponseDto | null = null;
      let groupFormResponse: CustomGroupFormCreatedResponseDto | null = null;
      const customFieldResponses: CustomFieldResponseDto[] = [];
      const classificationResponses: ClassificationResponseDto[] = [];

      // 1. Cập nhật sản phẩm nếu có
      if (updateDto.productId && updateDto.product) {
        productResponse = await this.userProductService.updateProduct(
          updateDto.productId,
          updateDto.product,
          userId,
        );
      }

      // 2. Cập nhật nhóm trường nếu có
      if (updateDto.groupFormId && updateDto.groupForm) {
        // Đảm bảo label luôn có giá trị
        const groupFormDto: UpdateCustomGroupFormDto = {
          label: updateDto.groupForm.label || '', // Sử dụng chuỗi rỗng nếu không có label
          userId: userId
        };

        groupFormResponse = await this.customGroupFormService.update(
          updateDto.groupFormId,
          groupFormDto,
        );
      }

      // 3. Cập nhật các trường tùy chỉnh nếu có
      if (updateDto.customFields && updateDto.customFields.length > 0) {
        for (const fieldItem of updateDto.customFields) {
          if (fieldItem.id) {
            // Cập nhật trường tùy chỉnh hiện có
            const customField = await this.customFieldService.update(
              fieldItem.id,
              fieldItem.field,
            );
            customFieldResponses.push(customField);
          } else if (updateDto.groupFormId) {
            // Tạo trường tùy chỉnh mới
            // Tạo một đối tượng mới với các trường bắt buộc
            const customFieldDto: CreateCustomFieldDto = {
              // Trường bắt buộc
              component: fieldItem.field.component || 'Text Input',
              configId: `custom-field-${Date.now()}`,
              label: fieldItem.field.label || 'Trường tùy chỉnh',
              type: fieldItem.field.type || 'text',
              required: fieldItem.field.required ?? false,
              configJson: fieldItem.field.configJson || {},
              // Trường từ tham số đầu vào
              userId,
              formGroupId: updateDto.groupFormId,
            };

            const customField = await this.customFieldService.create(customFieldDto);
            customFieldResponses.push(customField);
          }
        }
      }

      // 4. Xóa các trường tùy chỉnh nếu có danh sách ID cần xóa
      if (updateDto.customFieldsToDelete && updateDto.customFieldsToDelete.length > 0) {
        for (const fieldId of updateDto.customFieldsToDelete) {
          await this.customFieldService.delete(fieldId, userId);
        }
      }

      // 5. Cập nhật hoặc tạo mới các phân loại sản phẩm nếu có
      if (updateDto.productId && updateDto.classifications && updateDto.classifications.length > 0) {
        for (const classificationDto of updateDto.classifications) {
          if (classificationDto.id) {
            // Cập nhật phân loại hiện có
            const classification = await this.classificationService.update(
              classificationDto.id,
              classificationDto,
              userId,
            );
            classificationResponses.push(classification);
          } else {
            // Tạo phân loại mới
            // Chuyển đổi từ UpdateClassificationDto sang CreateClassificationDto
            const createClassificationDto: CreateClassificationDto = {
              type: classificationDto.type || 'Phân loại mới', // Đảm bảo type luôn có giá trị
              price: classificationDto.price,
              customFields: classificationDto.customFields,
            };

            const classification = await this.classificationService.create(
              updateDto.productId,
              createClassificationDto,
              userId,
            );
            classificationResponses.push(classification);
          }
        }
      }

      // 6. Xóa các phân loại nếu có danh sách ID cần xóa
      if (updateDto.classificationsToDelete && updateDto.classificationsToDelete.length > 0) {
        for (const classificationId of updateDto.classificationsToDelete) {
          await this.classificationService.delete(classificationId, userId);
        }
      }

      // 7. Lấy danh sách phân loại hiện có nếu có productId
      if (updateDto.productId && classificationResponses.length === 0) {
        const classifications = await this.classificationService.getByProductId(updateDto.productId);
        classificationResponses.push(...classifications);
      }

      return {
        product: productResponse,
        groupForm: groupFormResponse,
        customFields: customFieldResponses,
        classifications: classificationResponses,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật tích hợp business: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.INTEGRATION_UPDATE_FAILED,
        `Lỗi khi cập nhật tích hợp business: ${error.message}`,
      );
    }
  }

  /**
   * Xóa tích hợp business
   * @param deleteDto DTO chứa thông tin xóa
   * @param userId ID người dùng hiện tại
   */
  @Transactional()
  async delete(
    deleteDto: DeleteIntegratedBusinessDto,
    userId: number,
  ): Promise<void> {
    try {
      this.logger.log(`Xóa tích hợp business cho userId=${userId}, productId=${deleteDto.productId}`);

      // 1. Tìm tất cả nhóm trường liên quan đến sản phẩm
      const queryDto = {
        productId: deleteDto.productId,
        userId,
        page: 1,
        limit: 100,
      };
      const groupForms = await this.customGroupFormService.findAll(queryDto);

      // 2. Xóa từng nhóm trường và các trường tùy chỉnh liên quan
      for (const groupForm of groupForms.items) {
        await this.customGroupFormService.delete(groupForm.id, userId);
      }

      // 3. Xóa các phân loại sản phẩm liên quan
      const classifications = await this.classificationService.getByProductId(deleteDto.productId);
      for (const classification of classifications) {
        await this.classificationService.delete(classification.id, userId);
      }

      // 3. Xóa sản phẩm
      await this.userProductService.deleteProduct(deleteDto.productId, userId);

    } catch (error) {
      this.logger.error(`Lỗi khi xóa tích hợp business: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.INTEGRATION_DELETION_FAILED,
        `Lỗi khi xóa tích hợp business: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết tích hợp business
   * @param productId ID sản phẩm
   * @param userId ID người dùng hiện tại
   * @returns Thông tin chi tiết tích hợp
   */
  async getDetail(
    productId: number,
    userId: number,
  ): Promise<IntegratedBusinessResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết tích hợp business cho userId=${userId}, productId=${productId}`);

      // 1. Lấy thông tin sản phẩm
      const product = await this.userProductService.getProductDetail(productId);

      // 2. Tìm nhóm trường liên quan đến sản phẩm
      const queryDto = {
        productId,
        userId,
        page: 1,
        limit: 100,
      };
      const groupForms = await this.customGroupFormService.findAll(queryDto);

      let groupForm: CustomGroupFormResponseDto | null = null;
      const customFields: CustomFieldResponseDto[] = [];

      if (groupForms.items.length > 0) {
        // Lấy nhóm trường đầu tiên
        groupForm = await this.customGroupFormService.findById(groupForms.items[0].id, userId);

        // 3. Lấy các trường tùy chỉnh trong nhóm
        if (groupForm && groupForm.fields) {
          for (const field of groupForm.fields) {
            const customField = await this.customFieldService.findById(field.fieldId);
            if (customField) {
              customFields.push(customField);
            }
          }
        }
      }

      // 4. Lấy các phân loại sản phẩm
      const classifications = await this.classificationService.getByProductId(productId);

      return {
        product,
        groupForm,
        customFields,
        classifications,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết tích hợp business: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.INTEGRATION_FIND_FAILED,
        `Lỗi khi lấy chi tiết tích hợp business: ${error.message}`,
      );
    }
  }

  /**
   * Tạo tích hợp business mới bao gồm sản phẩm, nhóm trường và các trường tùy chỉnh (không bao gồm phân loại sản phẩm)
   * @param createDto DTO chứa thông tin tích hợp không bao gồm phân loại
   * @param userId ID người dùng hiện tại
   * @returns Thông tin tích hợp đã tạo không bao gồm phân loại
   */
  @Transactional()
  async createWithoutClassification(
    createDto: CreateIntegratedBusinessWithoutClassificationDto,
    userId: number,
  ): Promise<IntegratedBusinessWithoutClassificationResponseDto> {
    try {
      this.logger.log(`Tạo tích hợp business mới không bao gồm phân loại cho userId=${userId}`);

      // 1. Tạo sản phẩm trước
      const productResponse = await this.userProductService.createProduct(
        createDto.product,
        userId,
      );

      // 2. Tạo nhóm trường với productId
      const groupFormDto = {
        ...createDto.groupForm,
        userId,
        productId: productResponse.id,
      };
      const groupFormResponse = await this.customGroupFormService.create(groupFormDto);

      // 3. Tạo các trường tùy chỉnh và liên kết với nhóm
      const customFieldResponses: CustomFieldResponseDto[] = [];

      if (createDto.customFields && createDto.customFields.length > 0) {
        for (const fieldDto of createDto.customFields) {
          // Thêm các trường bắt buộc nếu chưa có
          const customFieldDto: CreateCustomFieldDto = {
            // Đảm bảo có các trường bắt buộc
            component: fieldDto.component || 'Text Input',
            configId: fieldDto.configId, // Sẽ được tự động tạo nếu không được cung cấp
            label: fieldDto.label || 'Trường tùy chỉnh',
            type: fieldDto.type || 'text',
            required: fieldDto.required ?? false,
            configJson: fieldDto.configJson || {},

            // Thêm trường liên kết
            userId,
            formGroupId: groupFormResponse.id,

            // Thêm các trường tùy chọn nếu có
            grid: fieldDto.grid,
            value: fieldDto.value,
          };

          const customField = await this.customFieldService.create(customFieldDto);
          customFieldResponses.push(customField);
        }
      }

      return {
        product: productResponse,
        groupForm: groupFormResponse,
        customFields: customFieldResponses,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tạo tích hợp business không bao gồm phân loại: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.INTEGRATION_CREATION_FAILED,
        `Lỗi khi tạo tích hợp business không bao gồm phân loại: ${error.message}`,
      );
    }
  }
}