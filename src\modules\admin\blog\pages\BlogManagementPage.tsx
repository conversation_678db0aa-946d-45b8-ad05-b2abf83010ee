import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography } from '@/shared/components/common';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import { useGetAdminBlogs } from '../hooks/useBlogAdmin';
import { useGetAdminBlogPurchases } from '../hooks/useBlogPurchase.admin';

interface BlogListPageProps {
  initialTag?: string;
}
/**
 * Trang tổng quan quản lý Blog đã mua
 */
const BlogManagementPage: React.FC<BlogListPageProps> = ({ initialTag }) => {
  const { t } = useTranslation(['blogAdmin', 'common']);
  const [tag, setTag] = useState<string | undefined>(initialTag);

  // Sử dụng hook để lấy tổng số blog
  const { data: blogsData, isLoading: isLoadingBlogs } = useGetAdminBlogs({
    page: 1,
    limit: 1,
    tags: tag
  });

  // Sử dụng hook để lấy tổng số giao dịch mua bài viết
  const { data: purchasesData, isLoading: isLoadingPurchases } = useGetAdminBlogPurchases({
    page: 1,
    limit: 1
  });

  // Update tag when initialTag changes
  useEffect(() => {
    setTag(initialTag);
  }, [initialTag]);

  return (
    <div>
      <Typography variant="h1" className="mb-6">
        {t('purchaseManagement.title')}
      </Typography>
      <Typography variant="body1" className="mb-8">
        {t('purchaseManagement.description')}
      </Typography>

      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Purchased Blogs Card */}
        <ModuleCard
          title={t('purchaseManagement.purchasedBlogs.title')}
          description={t('purchaseManagement.purchasedBlogs.description')}
          icon="book-open"
          count={isLoadingBlogs ? '...' : blogsData?.result?.totalItems.toString() || '0'}
          countLabel={t('purchaseManagement.purchasedBlogs.totalBlogs')}
          linkTo="/admin/blog/list"
          linkText={t('purchaseManagement.purchasedBlogs.manage')}
        />
        <ModuleCard
          title={t('purchaseManagement.transactions.title')}
          description={t('purchaseManagement.transactions.description')}
          icon="credit-card"
          count={isLoadingPurchases ? '...' : purchasesData?.result.meta?.totalItems.toString() || '0'}
          countLabel={t('purchaseManagement.transactions.totalTransactions')}
          linkTo="/admin/blog/purchases"
          linkText={t('purchaseManagement.transactions.manage')}
        />
      </ResponsiveGrid>
    </div>
  );
};

export default BlogManagementPage;

