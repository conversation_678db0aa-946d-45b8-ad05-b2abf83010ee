import React, { useState } from 'react';
import { Button, Input, Typography } from '@/shared/components/common';
import { EmailTemplate } from '../types';
import {
  Type,
  Heading1,
  Image,
  Square,
  Minus,
  ArrowUpDown,
  List,
  Link,
  Share2,
  PanelTop,
  PanelLeft,
  Layout,
  Columns,
  Video,
  Code,
  Search,
  Grid,
  Table,
  AlignLeft,
  Clock,
  MapPin,
  Phone,
  Mail,
} from 'lucide-react';

// Kích thước icon
const iconSize = 16;

// Định nghĩa các nhóm blocks
const blockCategories = [
  { id: 'basic', name: '<PERSON><PERSON> bản' },
  { id: 'layout', name: '<PERSON><PERSON> cục' },
  { id: 'media', name: 'Media' },
  { id: 'advanced', name: '<PERSON>âng cao' },
  { id: 'templates', name: 'Templates' }
];

// Phân loại các blocks vào các nhóm
const categorizedBlocks = {
  basic: [
    { type: 'text', label: 'Văn bản', icon: Type, description: 'Thêm đoạn văn bản' },
    { type: 'heading', label: 'Tiêu đề', icon: Heading1, description: 'Thêm tiêu đề' },
    { type: 'button', label: 'Nút nhấn', icon: Square, description: 'Thêm nút nhấn' },
    { type: 'divider', label: 'Đường kẻ', icon: Minus, description: 'Thêm đường kẻ ngang' },
    { type: 'spacer', label: 'Khoảng cách', icon: ArrowUpDown, description: 'Thêm khoảng cách' }
  ],
  layout: [
    { type: '1column', label: 'Cột đơn', icon: Layout, description: 'Bố cục một cột' },
    { type: '2columns', label: 'Hai cột', icon: Columns, description: 'Bố cục hai cột' },
    { type: 'header', label: 'Header', icon: PanelTop, description: 'Thêm header' },
    { type: 'footer', label: 'Footer', icon: PanelLeft, description: 'Thêm footer' },
    { type: 'grid', label: 'Lưới', icon: Grid, description: 'Bố cục lưới' },
    { type: 'table', label: 'Bảng', icon: Table, description: 'Thêm bảng' }
  ],
  media: [
    { type: 'image', label: 'Hình ảnh', icon: Image, description: 'Thêm hình ảnh' },
    { type: 'video', label: 'Video', icon: Video, description: 'Thêm video' },
    { type: 'social', label: 'Mạng xã hội', icon: Share2, description: 'Thêm nút mạng xã hội' }
  ],
  advanced: [
    { type: 'html', label: 'HTML', icon: Code, description: 'Thêm mã HTML tùy chỉnh' },
    { type: 'list', label: 'Danh sách', icon: List, description: 'Thêm danh sách' },
    { type: 'link', label: 'Liên kết', icon: Link, description: 'Thêm liên kết' },
    { type: 'quote', label: 'Trích dẫn', icon: AlignLeft, description: 'Thêm trích dẫn' },
    { type: 'countdown', label: 'Đếm ngược', icon: Clock, description: 'Thêm bộ đếm ngược' },
    { type: 'map', label: 'Bản đồ', icon: MapPin, description: 'Thêm bản đồ' },
    { type: 'contact', label: 'Liên hệ', icon: Phone, description: 'Thêm thông tin liên hệ' },
    { type: 'email', label: 'Email', icon: Mail, description: 'Thêm địa chỉ email' }
  ]
};


interface BlocksPanelProps {
  onAddElement: (type: string) => void;
  onApplyTemplate: (template: EmailTemplate) => void;
}

const BlocksPanel: React.FC<BlocksPanelProps> = ({
  onAddElement,
  // onApplyTemplate không được sử dụng nhưng giữ lại để tương thích với interface
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('basic');
  const [hoveredBlock, setHoveredBlock] = useState<string | null>(null);

  // Xử lý khi kéo phần tử
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, elementType: string) => {
    e.dataTransfer.setData('text/plain', elementType);
    e.dataTransfer.effectAllowed = 'copy';
  };

  // Lọc blocks theo từ khóa tìm kiếm
  const getFilteredBlocks = () => {
    if (!searchQuery) {
      return categorizedBlocks[activeCategory as keyof typeof categorizedBlocks] || [];
    }

    const query = searchQuery.toLowerCase();
    const allBlocks = Object.values(categorizedBlocks).flat();
    return allBlocks.filter(block =>
      block.label.toLowerCase().includes(query) ||
      block.description.toLowerCase().includes(query)
    );
  };

  // Lấy icon tương ứng với loại phần tử
  const getBlockIcon = (block: { icon: React.ComponentType<{ size?: number | string }> }) => {
    const Icon = block.icon;
    return <Icon size={iconSize} />;
  };

  const filteredBlocks = getFilteredBlocks();

  return (
    <div className="h-full overflow-y-auto p-4">
      <div className="mb-4">
        <div className="relative">
          <span className="absolute left-2 top-2.5 text-gray-400">
            <Search size={iconSize} />
          </span>
          <Input
            placeholder="Tìm kiếm blocks..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {!searchQuery && (
        <div className="flex overflow-x-auto mb-4 pb-2 scrollbar-thin">
          {blockCategories.map(category => (
            <Button
              key={category.id}
              variant={activeCategory === category.id ? 'primary' : 'outline'}
              onClick={() => setActiveCategory(category.id)}
              className="text-xs whitespace-nowrap mr-2"
            >
              {category.name}
            </Button>
          ))}
        </div>
      )}

      <div className="grid grid-cols-2 gap-2 mb-6">
        {filteredBlocks.map((block) => (
          <div
            key={block.type}
            className="cursor-pointer border rounded-md p-3 hover:bg-gray-50 flex flex-col items-center justify-center relative"
            draggable
            onDragStart={(e) => handleDragStart(e, block.type)}
            onClick={() => onAddElement(block.type)}
            onMouseEnter={() => setHoveredBlock(block.type)}
            onMouseLeave={() => setHoveredBlock(null)}
          >
            <div className="mb-1">
              {getBlockIcon(block)}
            </div>
            <Typography variant="body2" className="text-xs text-center">{block.label}</Typography>

            {/* Tooltip khi hover */}
            {hoveredBlock === block.type && (
              <div className="absolute bottom-full left-0 mb-2 bg-gray-800 text-white text-xs rounded py-1 px-2 w-full z-10">
                {block.description}
              </div>
            )}
          </div>
        ))}
      </div>

      {activeCategory === 'templates' && (
        <div className="space-y-2">
          <Typography variant="h3" className="text-lg font-medium mb-2">Templates</Typography>
          <div className="grid grid-cols-1 gap-2">
            {/* Render templates here */}
          </div>
        </div>
      )}
    </div>
  );
};

export default BlocksPanel;
