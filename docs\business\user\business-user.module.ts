import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServicesModule } from '@shared/services/services.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import {
  UserProduct,
  UserClassification,
  CustomFieldClassification,
  CustomField,
  CustomGroupForm,
  CustomGroupFormField,
  PhysicalWarehouse,
  UserOrder,
  VirtualWarehouse,
  Warehouse,
  WarehouseCustomField,
  Inventory,
  UserConvert,
  UserConvertCustomer,
  File,
  Folder
} from '../entities';
import { Product } from '@modules/marketplace/entities';
import { Agent } from '@modules/agent/entities/agent.entity';

import {
  UserProductRepository,
  UserOrderRepository,
  WarehouseRepository,
  PhysicalWarehouseRepository,
  VirtualWarehouseRepository,
  InventoryRepository,
  UserClassificationRepository,
  CustomFieldClassificationRepository,
  CustomFieldRepository,
  CustomGroupFormRepository,
  CustomGroupFormFieldRepository,
  UserConvertRepository,
  UserConvertCustomerRepository,
  WarehouseCustomFieldRepository,
  FileRepository,
  FolderRepository
} from '../repositories';
import { ProductRepository } from '@modules/marketplace/repositories';

import {
  UserProductController,
  UserOrderController,
  UserWarehouseController,
  UserWarehouseCustomFieldController,
  UserInventoryController,
  UserPhysicalWarehouseController,
  CustomGroupFormController,
  CustomFieldController,
  BusinessIntegrationController,
  ClassificationController,
  UserConvertController,
  UserConvertCustomerController,
  UserFileController,
  UserFolderController, UserVirtualWarehouseController,
} from './controllers';

import {
  UserProductService,
  UserOrderService,
  UserWarehouseService,
  UserWarehouseCustomFieldService,
  UserInventoryService,
  UserPhysicalWarehouseService,
  CustomGroupFormService,
  CustomFieldService,
  BusinessIntegrationService,
  ClassificationService,
  UserConvertService,
  UserConvertCustomerService,
  UserFileService,
  UserFolderService, UserVirtualWarehouseService,
} from './services';

import { ValidationHelper, UserProductHelper } from './helpers';
import { BusinessIntegrationInterceptor } from './interceptors/business-integration.interceptor';

/**
 * Module quản lý chức năng business cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserProduct,
      UserClassification,
      CustomFieldClassification,
      CustomField,
      CustomGroupForm,
      CustomGroupFormField,
      PhysicalWarehouse,
      UserOrder,
      VirtualWarehouse,
      Warehouse,
      WarehouseCustomField,
      Inventory,
      UserConvert,
      UserConvertCustomer,
      Product,
      Agent,
      File,
      Folder
    ]),
    ServicesModule
  ],
  controllers: [
    UserProductController,
    UserOrderController,
    UserWarehouseController,
    UserWarehouseCustomFieldController,
    UserInventoryController,
    UserPhysicalWarehouseController,
    CustomGroupFormController,
    CustomFieldController,
    BusinessIntegrationController,
    ClassificationController,
    UserConvertController,
    UserConvertCustomerController,
    UserFileController,
    UserFolderController,
    UserVirtualWarehouseController
  ],
  providers: [
    // Repositories
    UserProductRepository,
    UserOrderRepository,
    WarehouseRepository,
    PhysicalWarehouseRepository,
    VirtualWarehouseRepository,
    InventoryRepository,
    UserClassificationRepository,
    CustomFieldClassificationRepository,
    CustomFieldRepository,
    CustomGroupFormRepository,
    CustomGroupFormFieldRepository,
    UserConvertRepository,
    UserConvertCustomerRepository,
    ProductRepository,
    WarehouseCustomFieldRepository,
    FileRepository,
    FolderRepository,

    // Helpers
    ValidationHelper,
    UserProductHelper,

    // Services
    UserProductService,
    UserOrderService,
    UserWarehouseService,
    UserWarehouseCustomFieldService,
    UserInventoryService,
    UserPhysicalWarehouseService,
    CustomGroupFormService,
    CustomFieldService,
    ClassificationService,
    BusinessIntegrationService,
    UserConvertService,
    UserConvertCustomerService,
    UserFileService,
    UserFolderService,
    UserVirtualWarehouseService,

    // Interceptors
    {
      provide: APP_INTERCEPTOR,
      useClass: BusinessIntegrationInterceptor
    }
  ],
  exports: [
    TypeOrmModule,
    UserProductService,
    UserOrderService,
    UserWarehouseService,
    UserWarehouseCustomFieldService,
    UserInventoryService,
    UserPhysicalWarehouseService,
    CustomGroupFormService,
    CustomFieldService,
    ClassificationService,
    BusinessIntegrationService,
    UserConvertService,
    UserConvertCustomerService,
    UserFileService,
    UserFolderService,
    UserVirtualWarehouseService,
    UserProductRepository,
  ],
})
export class BusinessUserModule {}
