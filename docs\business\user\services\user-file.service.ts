import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { PaginatedResult } from '@common/response';
import { File } from '@modules/business/entities';
import { FileRepository } from '@modules/business/repositories';
import { ValidationHelper } from '../helpers/validation.helper';
import {
  CreateFileDto,
  UpdateFileDto,
  FileResponseDto,
  QueryFileDto,
} from '../dto/file';
import { Transactional } from 'typeorm-transactional';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { CategoryFolderEnum, FileSizeEnum, TimeIntervalEnum } from '@shared/utils';
import { generateS3Key } from '@shared/utils/generators';
import { FileTypeEnum } from '@shared/utils/file/file-media-type.util';
import { MediaType } from '@shared/utils/file';
import { extname } from 'path';

/**
 * Service xử lý nghiệp vụ liên quan đến file
 */
@Injectable()
export class UserFileService {
  private readonly logger = new Logger(UserFileService.name);

  constructor(
    private readonly fileRepository: FileRepository,
    private readonly validationHelper: ValidationHelper,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Tạo mới file
   * @param createDto DTO chứa thông tin tạo file mới
   * @returns Thông tin file đã tạo và URL để upload
   */
  @Transactional()
  async createFile(createDto: CreateFileDto): Promise<FileResponseDto> {
    try {
      // Kiểm tra dữ liệu đầu vào
      await this.validationHelper.validateCreateFile(createDto);

      // Xác định loại file dựa vào phần mở rộng
      const extension = extname(createDto.name).toLowerCase();
      let fileType: MediaType;

      // Xác định loại file dựa vào phần mở rộng
      switch (extension) {
        case '.pdf':
          fileType = FileTypeEnum.PDF;
          break;
        case '.doc':
          fileType = FileTypeEnum.DOC;
          break;
        case '.docx':
          fileType = FileTypeEnum.DOCX;
          break;
        case '.pptx':
          fileType = FileTypeEnum.PPTX;
          break;
        case '.json':
          fileType = FileTypeEnum.JSON;
          break;
        case '.html':
          fileType = FileTypeEnum.HTML;
          break;
        default:
          fileType = FileTypeEnum.TXT;
          break;
      }

      // Tạo S3 key cho file
      const s3Key = generateS3Key({
        baseFolder: 'files',
        categoryFolder: CategoryFolderEnum.DOCUMENT,
        fileName: createDto.name,
        useTimeFolder: true,
      });

      // Tạo file mới
      const file = new File();
      file.name = createDto.name;
      // Lưu warehouseId vào biến tạm thời để sử dụng trong response
      // Nếu không có warehouseId, sử dụng giá trị mặc định hoặc lấy từ folder
      const warehouseId = createDto.warehouseId || null;
      // Đảm bảo folderId không null vì cột folder_id trong database là NOT NULL
      if (!createDto.folderId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'ID thư mục không được để trống',
        );
      }
      file.folderId = createDto.folderId;
      file.storageKey = s3Key;
      file.size = createDto.size || 0;

      // Lưu file vào cơ sở dữ liệu
      const savedFile = await this.fileRepository.createFile(file);

      // Tạo presigned URL để client có thể upload file
      const uploadUrl = await this.s3Service.createPresignedWithID(
        s3Key,
        TimeIntervalEnum.TEN_MINUTES,
        fileType,
        FileSizeEnum.TWENTY_MB,
      );

      // Tạo URL xem file
      const viewUrl = this.cdnService.generateUrlView(s3Key, TimeIntervalEnum.ONE_DAY) || '';

      // Chuyển đổi sang DTO và trả về
      const fileResponse = {
        ...savedFile,
        warehouseId, // Thêm warehouseId vào response mặc dù không có trong database
        viewUrl,
        uploadUrl,
      };

      return plainToInstance(FileResponseDto, fileResponse, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tạo file: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.FILE_CREATION_FAILED,
        `Lỗi khi tạo file: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin file
   * @param id ID của file
   * @param updateDto DTO chứa thông tin cập nhật file
   * @param userId ID của người dùng
   * @returns Thông tin file đã cập nhật
   */
  @Transactional()
  async updateFile(id: number, updateDto: UpdateFileDto, userId: number): Promise<FileResponseDto> {
    try {
      // Kiểm tra dữ liệu đầu vào
      await this.validationHelper.validateUpdateFile(id, updateDto);

      // Lấy thông tin file hiện tại
      const existingFile = await this.fileRepository.findByIdUser(id);
      if (!existingFile) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FILE_NOT_FOUND,
          `Không tìm thấy file với ID ${id}`,
        );
      }

      // Kiểm tra quyền truy cập
      const folder = await this.validationHelper.validateFolderExists(existingFile.folderId);
      if (folder.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FILE_ACCESS_DENIED,
          `Bạn không có quyền cập nhật file này`,
        );
      }

      // Chuẩn bị dữ liệu cập nhật
      const updateData: Partial<File> = {};

      if (updateDto.name !== undefined) {
        updateData.name = updateDto.name;
      }

      if (updateDto.folderId !== undefined) {
        updateData.folderId = updateDto.folderId;
      }

      if (updateDto.size !== undefined) {
        updateData.size = updateDto.size;
      }

      // Cập nhật thời gian
      updateData.updatedAt = Date.now();

      // Cập nhật file
      const updatedFile = await this.fileRepository.updateFile(id, updateData);

      // Tạo URL xem file
      const viewUrl = this.cdnService.generateUrlView(updatedFile.storageKey, TimeIntervalEnum.ONE_DAY) || '';

      // File entity không có thuộc tính warehouseId
      const warehouseId = null;

      // Chuẩn bị response
      const fileResponse: any = {
        ...updatedFile,
        viewUrl,
      };

      // Nếu cần tạo URL để upload file mới
      if (updateDto.generateUploadUrl) {
        // Xác định loại file dựa vào phần mở rộng
        const extension = extname(updatedFile.name).toLowerCase();
        let fileType: MediaType;

        // Xác định loại file dựa vào phần mở rộng
        switch (extension) {
          case '.pdf':
            fileType = FileTypeEnum.PDF;
            break;
          case '.doc':
            fileType = FileTypeEnum.DOC;
            break;
          case '.docx':
            fileType = FileTypeEnum.DOCX;
            break;
          case '.pptx':
            fileType = FileTypeEnum.PPTX;
            break;
          case '.json':
            fileType = FileTypeEnum.JSON;
            break;
          case '.html':
            fileType = FileTypeEnum.HTML;
            break;
          default:
            fileType = FileTypeEnum.TXT;
            break;
        }

        // Tạo presigned URL để client có thể upload file mới
        const uploadUrl = await this.s3Service.createPresignedWithID(
          updatedFile.storageKey,
          TimeIntervalEnum.TEN_MINUTES,
          fileType,
          FileSizeEnum.TWENTY_MB,
        );

        // Thêm uploadUrl vào response
        fileResponse.uploadUrl = uploadUrl;
      }

      return plainToInstance(FileResponseDto, fileResponse, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật file: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.FILE_UPDATE_FAILED,
        `Lỗi khi cập nhật file: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin file theo ID
   * @param id ID của file
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của file
   */
  async getFileById(id: number, userId: number): Promise<FileResponseDto> {
    try {
      // Lấy thông tin file
      const file = await this.validationHelper.validateFileExists(id);

      // Kiểm tra quyền truy cập
      const folder = await this.validationHelper.validateFolderExists(file.folderId);
      if (folder.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FILE_ACCESS_DENIED,
          `Bạn không có quyền truy cập file này`,
        );
      }

      // Tạo URL xem file
      const viewUrl = this.cdnService.generateUrlView(file.storageKey, TimeIntervalEnum.ONE_DAY) || '';

      // Lấy warehouseId từ file (nếu có)
      // File entity doesn't have warehouseId property
      const warehouseId = null;

      // Chuyển đổi sang DTO và trả về
      const fileResponse = {
        ...file,
        warehouseId, // Thêm warehouseId vào response mặc dù không có trong database
        viewUrl,
      };

      return plainToInstance(FileResponseDto, fileResponse, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin file: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.FILE_FETCH_FAILED,
        `Lỗi khi lấy thông tin file: ${error.message}`,
      );
    }
  }

  /**
   * Xóa file
   * @param id ID của file
   * @param userId ID của người dùng
   * @returns Kết quả xóa
   */
  @Transactional()
  async deleteFile(id: number, userId: number): Promise<void> {
    try {
      // Kiểm tra file tồn tại
      const file = await this.validationHelper.validateFileExists(id);

      // Kiểm tra quyền truy cập
      const folder = await this.validationHelper.validateFolderExists(file.folderId);
      if (folder.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FILE_ACCESS_DENIED,
          `Bạn không có quyền xóa file này`,
        );
      }

      // Xóa file trên S3 nếu có storage_key
      if (file.storageKey) {
        try {
          await this.s3Service.deleteFile(file.storageKey);
          this.logger.log(`Đã xóa file trên S3: ${file.storageKey}`);
        } catch (s3Error) {
          this.logger.warn(`Không thể xóa file trên S3: ${s3Error.message}`, s3Error.stack);
          // Tiếp tục xóa bản ghi trong database ngay cả khi xóa trên S3 thất bại
        }
      }

      // Xóa file trong database
      const result = await this.fileRepository.deleteFile(id);

      if (!result) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FILE_DELETE_FAILED,
          `Không thể xóa file với ID ${id}`,
        );
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xóa file: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.FILE_DELETE_FAILED,
        `Lỗi khi xóa file: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách file với phân trang và lọc
   * @param queryDto Tham số truy vấn
   * @returns Danh sách file với phân trang
   */
  async getFiles(queryDto: QueryFileDto): Promise<PaginatedResult<FileResponseDto>> {
    try {
      // Kiểm tra và xử lý trường sắp xếp
      const validSortFields = ['id', 'name', 'size', 'createdAt', 'updatedAt'];
      if (queryDto.sortBy && !validSortFields.includes(queryDto.sortBy)) {
        // Nếu trường sắp xếp không hợp lệ, sử dụng trường mặc định
        queryDto.sortBy = 'id';
      }

      // Đảm bảo chỉ lấy file của người dùng hiện tại
      if (!queryDto.userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FILE_ACCESS_DENIED,
          `Không thể lấy danh sách file: Thiếu thông tin người dùng`,
        );
      }

      // Lấy danh sách file từ repository
      const result = await this.fileRepository.findAllUser(
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.folderId,
        queryDto.sortBy,
        queryDto.sortDirection,
        queryDto.userId
      );

      // Chuyển đổi các item sang DTO và thêm URL xem file
      const items = await Promise.all(
        result.items.map(async (file) => {
          // Tạo URL xem file
          const viewUrl = this.cdnService.generateUrlView(file.storageKey, TimeIntervalEnum.ONE_DAY) || '';

          // Lấy warehouseId từ query hoặc gán giá trị mặc định
          const warehouseId = queryDto.warehouseId || 0;

          // Kết hợp dữ liệu file và URL
          const fileResponse = {
            ...file,
            warehouseId, // Thêm warehouseId vào response mặc dù không có trong database
            viewUrl,
          };

          return plainToInstance(FileResponseDto, fileResponse, {
            excludeExtraneousValues: true,
          });
        })
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách file: ${error.message}`, error.stack);

      throw new AppException(
        BUSINESS_ERROR_CODES.FILE_FETCH_FAILED,
        `Lỗi khi lấy danh sách file: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách file theo ID thư mục
   * @param folderId ID của thư mục
   * @param userId ID của người dùng
   * @returns Danh sách file trong thư mục
   */
  async getFilesByFolderId(folderId: number, userId: number): Promise<FileResponseDto[]> {
    try {
      // Kiểm tra thư mục tồn tại
      const folder = await this.validationHelper.validateFolderExists(folderId);

      // Kiểm tra quyền truy cập
      if (folder.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FILE_ACCESS_DENIED,
          `Bạn không có quyền truy cập thư mục này`,
        );
      }

      // Lấy danh sách file theo ID thư mục
      const files = await this.fileRepository.findByFolderId(folderId);

      // Chuyển đổi các item sang DTO và thêm URL xem file
      return await Promise.all(
        files.map(async (file) => {
          // Tạo URL xem file
          const viewUrl = this.cdnService.generateUrlView(file.storageKey, TimeIntervalEnum.ONE_DAY) || '';

          // Gán warehouseId mặc định vì không có trong database
          const warehouseId = 0; // Hoặc có thể lấy từ một nguồn khác nếu cần

          // Kết hợp dữ liệu file và URL
          const fileResponse = {
            ...file,
            warehouseId, // Thêm warehouseId vào response mặc dù không có trong database
            viewUrl,
          };

          return plainToInstance(FileResponseDto, fileResponse, {
            excludeExtraneousValues: true,
          });
        })
      );
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách file theo ID thư mục: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.FILE_FETCH_FAILED,
        `Lỗi khi lấy danh sách file theo ID thư mục: ${error.message}`,
      );
    }
  }
}
