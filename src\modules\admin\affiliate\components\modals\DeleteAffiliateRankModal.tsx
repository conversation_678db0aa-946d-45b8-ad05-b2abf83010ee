import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAffiliateRankData } from '../../hooks/useAffiliateData';
import { AffiliateRankDto } from '../../types/api.types';
import DeleteConfirmModal from '@/shared/components/common/DeleteConfirmModal';

interface DeleteAffiliateRankModalProps {
  /**
   * Trạng thái hiển thị của modal
   */
  isOpen: boolean;

  /**
   * Callback khi đóng modal
   */
  onClose: () => void;

  /**
   * Callback khi xóa thành công
   */
  onSuccess?: () => void;

  /**
   * Thông tin rank cần xóa
   */
  rank: AffiliateRankDto;
}

/**
 * Modal xác nhận xóa rank affiliate
 */
const DeleteAffiliateRankModal: React.FC<DeleteAffiliateRankModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  rank,
}) => {
  const { t } = useTranslation(['affiliate', 'common']);
  const { useDeleteRank } = useAffiliateRankData();
  const { mutate: deleteRank, isPending } = useDeleteRank();
  const [error, setError] = useState<string | null>(null);

  // Xử lý khi xác nhận xóa
  const handleConfirm = () => {
    setError(null);
    deleteRank(rank.id, {
      onSuccess: () => {
        onClose();
        if (onSuccess) {
          onSuccess();
        }
      },
      onError: (error: Error) => {
        setError(error?.message || t('affiliate:rank.delete.error', 'Lỗi khi xóa cấp bậc'));
      },
    });
  };

  return (
    <DeleteConfirmModal
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={handleConfirm}
      title={t('affiliate:rank.delete.title', 'Xác nhận xóa cấp bậc')}
      itemName={rank.rankName}
      description={
        error ||
        t(
          'affiliate:rank.delete.description',
          'Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến cấp bậc này sẽ bị xóa vĩnh viễn.'
        )
      }
      isLoading={isPending}
      size="md"
    />
  );
};

export default DeleteAffiliateRankModal;
