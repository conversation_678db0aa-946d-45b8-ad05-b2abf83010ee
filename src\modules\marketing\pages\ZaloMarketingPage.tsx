import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Icon } from '@/shared/components/common';

/**
 * Trang tích hợp & quản lý Zalo OA/ZNS – skeleton
 */
const ZaloMarketingPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  return (
    <Card className="p-6">
      <div className="flex items-center mb-4">
        <Icon name="zap" size="lg" className="text-primary mr-2" />
        <Typography variant="h4">
          {t('marketing:zalo.title', 'Zalo OA/ZNS')}
        </Typography>
      </div>
      <Typography variant="body1" className="mb-2">
        {t(
          'marketing:zalo.description',
          'Tích hợp Zalo Official Account, gửi tin ZNS chăm sóc khách hàng'
        )}
      </Typography>
      <Typography variant="body2">
        {t(
          'marketing:zalo.comingSoon',
          'Chức năng đang được phát triển. Vui lòng quay lại sau!'
        )}
      </Typography>
    </Card>
  );
};

export default ZaloMarketingPage; 