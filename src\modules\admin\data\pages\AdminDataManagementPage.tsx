import React from 'react';
import { useTranslation } from 'react-i18next';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import { useAdminDataCounts } from '../hooks';

/**
 * Trang tổng quan quản lý dữ liệu cho Admin
 */
const AdminDataManagementPage: React.FC = () => {
  const { t } = useTranslation(['data', 'common', 'admin']);
  const { mediaCount, knowledgeFilesCount, urlCount, vectorStoreCount, isLoading } =
    useAdminDataCounts();

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Media Card */}
        <ModuleCard
          title={t('admin:data.media.title', 'Quản lý Media')}
          description={t(
            'admin:data.media.description',
            'Quản lý tập trung các tệp tin media như hình ảnh, video, âm thanh và tài liệu trong hệ thống.'
          )}
          icon="file-media"
          count={isLoading ? '...' : mediaCount.toString()}
          countLabel={t('admin:data.media.totalFiles', 'Tổng số tệp tin')}
          linkTo="/admin/data/media"
          linkText={t('admin:data.media.manage', 'Quản lý Media')}
        />

        {/* URL Card */}
        <ModuleCard
          title={t('admin:data.url.title', 'Quản lý URL')}
          description={t(
            'admin:data.url.description',
            'Quản lý các URL và nội dung được crawl từ các trang web để sử dụng trong hệ thống.'
          )}
          icon=""
          count={isLoading ? '...' : urlCount.toString()}
          countLabel={t('admin:data.url.totalUrls', 'Tổng số URL')}
          linkTo="/admin/data/url"
          linkText={t('admin:data.url.manage', 'Quản lý URL')}
        />

        {/* Knowledge Files Card */}
        <ModuleCard
          title={t('admin:data.knowledgeFiles.title', 'Quản lý File Tri Thức')}
          description={t(
            'admin:data.knowledgeFiles.description',
            'Quản lý các tệp tin tri thức được sử dụng cho AI và vector store.'
          )}
          icon="file-text"
          count={isLoading ? '...' : knowledgeFilesCount.toString()}
          countLabel={t('admin:data.knowledgeFiles.totalFiles', 'Tổng số tệp tin')}
          linkTo="/admin/data/knowledge-files"
          linkText={t('admin:data.knowledgeFiles.manage', 'Quản lý File Tri Thức')}
        />

        {/* Vector Store Card */}
        <ModuleCard
          title={t('admin:data.vectorStore.title', 'Quản lý Vector Store')}
          description={t(
            'admin:data.vectorStore.description',
            'Quản lý các vector store và embedding cho các ứng dụng AI và tìm kiếm ngữ nghĩa.'
          )}
          icon="database"
          count={isLoading ? '...' : vectorStoreCount.toString()}
          countLabel={t('admin:data.vectorStore.totalStores', 'Tổng số vector store')}
          linkTo="/admin/data/vector-store"
          linkText={t('admin:data.vectorStore.manage', 'Quản lý Vector Store')}
        />
      </ResponsiveGrid>
    </div>
  );
};

export default AdminDataManagementPage;
