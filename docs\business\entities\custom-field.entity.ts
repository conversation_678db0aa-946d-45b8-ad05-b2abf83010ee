import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { EntityStatusEnum } from '@modules/business/enums';

/**
 * Enum cho trạng thái của trường tùy chỉnh
 */
export enum CustomFieldStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DELETED = 'DELETED'
}

/**
 * Entity đại diện cho bảng custom_fields trong cơ sở dữ liệu
 * Bảng quản lý trường tùy chỉnh
 */
@Entity('custom_fields')
export class CustomField {
  /**
   * ID của trường tùy chỉnh
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Thành phần UI
   */
  @Column({
    name: 'component',
    length: 255,
    nullable: false,
    comment: 'Thành phần UI',
  })
  component: string;

  /**
   * ID cấu hình
   */
  @Column({
    name: 'config_id',
    length: 255,
    nullable: false,
    unique: true,
    comment: 'ID cấu hình',
  })
  configId: string;

  /**
   * Nhãn hiển thị
   */
  @Column({
    name: 'label',
    length: 255,
    nullable: false,
    comment: 'Nhãn hiển thị',
  })
  label: string;

  /**
   * Loại trường
   */
  @Column({ name: 'type', length: 50, nullable: false, comment: 'Loại trường' })
  type: string;

  /**
   * Trường bắt buộc hay không
   */
  @Column({
    name: 'required',
    type: 'boolean',
    nullable: false,
    comment: 'Trường bắt buộc hay không',
  })
  required: boolean;

  /**
   * Cấu hình JSON
   */
  @Column({
    name: 'config_json',
    type: 'jsonb',
    nullable: false,
    comment: 'Cấu hình JSON',
  })
  configJson: any;

  /**
   * ID nhân viên tạo
   */
  @Column({ name: 'employee_id', type: 'integer', nullable: true, comment: 'ID nhân viên tạo' })
  employeeId: number | null;

  /**
   * ID người dùng tạo
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true, comment: 'ID người dùng tạo' })
  userId: number | null;


  /**
   * Thời gian tạo (millis)
   */
  @Column({
    name: 'create_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian tạo (millis)',
  })
  createAt: number;

  /**
   * Trạng thái của trường tùy chỉnh
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: CustomFieldStatus,
    default: CustomFieldStatus.PENDING,
    nullable: false,
    comment: 'Trạng thái của trường tùy chỉnh',
  })
  status: CustomFieldStatus;
}
