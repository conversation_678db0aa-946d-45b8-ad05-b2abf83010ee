import { Injectable, Logger } from '@nestjs/common';
import { UserOrderRepository, UserConvertCustomerRepository } from '@modules/business/repositories';
import {
  QueryUserOrderDto,
  UserOrderResponseDto,
  UserOrderDetailResponseDto,
  UserConvertCustomerResponseDto
} from '../dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions';
import { BUSINESS_ADMIN_ERROR_CODES } from '@modules/business/admin/exceptions';
import { UserOrder, UserConvertCustomer } from '@modules/business/entities';
import { ValidationHelper } from '../helpers/validation.helper';

/**
 * Service xử lý nghiệp vụ liên quan đến đơn hàng của người dùng cho admin
 */
@Injectable()
export class UserOrderAdminService {
  private readonly logger = new Logger(UserOrderAdminService.name);

  constructor(
    private readonly userOrderRepository: UserOrderRepository,
    private readonly userConvertCustomerRepository: UserConvertCustomerRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Lấy danh sách đơn hàng với phân trang và lọc
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách đơn hàng phân trang
   */
  async getUserOrders(
    employeeId: number,
    queryDto: QueryUserOrderDto,
  ): Promise<PaginatedResult<UserOrderResponseDto>> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy danh sách đơn hàng với query: ${JSON.stringify(queryDto)}`,
      `method: ${this.getUserOrders.name}`
    );

    try {
      // Gọi repository để lấy dữ liệu
      const result = await this.userOrderRepository.findUserOrders(queryDto);

      // Chuyển đổi từ entity sang DTO
      const orderDtos = result.items.map(order => this.mapToUserOrderResponseDto(order));

      this.logger.log(`Đã tìm thấy ${result.items.length} đơn hàng`);

      return {
        items: orderDtos,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách đơn hàng: ${error.message}`,
        error.stack,
        `method: ${this.getUserOrders.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_FETCH_ERROR,
        'Lỗi khi lấy danh sách đơn hàng',
      );
    }
  }

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param orderId ID của đơn hàng
   * @returns Chi tiết đơn hàng
   */
  async getUserOrderById(
    employeeId: number,
    orderId: number,
  ): Promise<UserOrderDetailResponseDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy chi tiết đơn hàng với ID: ${orderId}`,
      `method: ${this.getUserOrderById.name}`
    );

    try {
      // Lấy thông tin đơn hàng với thông tin khách hàng
      const orderData = await this.userOrderRepository.findUserOrderByIdWithCustomer(orderId);

      // Kiểm tra đơn hàng có tồn tại không
      this.validationHelper.validateUserOrderExists(orderData);

      this.logger.log(`Đã tìm thấy đơn hàng với ID: ${orderId}`);

      // Chuyển đổi từ raw data sang entity
      const order = new UserOrder();
      let customer: UserConvertCustomer | null = null;

      // Log raw data để debug
      this.logger.log(`Raw order data: ${JSON.stringify(orderData)}`);

      // Đảm bảo orderData không null sau khi validate
      if (orderData) {
        // Map các trường của đơn hàng
        order.id = Number(orderData.id);
        order.userConvertCustomerId = orderData.user_convert_customer_id ? Number(orderData.user_convert_customer_id) : 0;
        order.userId = orderData.user_id ? Number(orderData.user_id) : 0;
        order.productInfo = orderData.product_info;
        order.billInfo = orderData.bill_info;
        order.hasShipping = Boolean(orderData.has_shipping);
        order.shippingStatus = orderData.shipping_status || null;
        order.logisticInfo = orderData.logistic_info;
        order.createdAt = Number(orderData.created_at);
        order.updatedAt = Number(orderData.updated_at);
        order.source = orderData.source || null;

        // Map các trường của khách hàng
        if (orderData.customer_id) {
          customer = new UserConvertCustomer();
          customer.id = Number(orderData.customer_id);
          customer.avatar = orderData.customer_avatar || null;
          customer.name = orderData.customer_name || null;
          customer.email = orderData.customer_email || null;
          customer.phone = orderData.customer_phone || null;
          customer.platform = orderData.customer_platform || null;
          customer.timezone = orderData.customer_timezone || null;
          customer.metadata = orderData.customer_metadata || null;
          customer.createdAt = Number(orderData.customer_created_at);
          customer.updatedAt = Number(orderData.customer_updated_at);
          customer.userId = orderData.customer_user_id ? Number(orderData.customer_user_id) : 0;
          customer.agentId = orderData.customer_agent_id || null;
        }
      }

      // Chuyển đổi từ entity sang DTO
      const orderDto = this.mapToUserOrderResponseDto(order);
      const customerDto = customer ? this.mapToUserConvertCustomerResponseDto(customer) : null;

      // Log DTO để debug
      this.logger.log(`Order DTO: ${JSON.stringify(orderDto)}`);
      if (customerDto) {
        this.logger.log(`Customer DTO: ${JSON.stringify(customerDto)}`);
      }

      return {
        ...orderDto,
        customer: customerDto
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy chi tiết đơn hàng: ${error.message}`,
        error.stack,
        `method: ${this.getUserOrderById.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_FETCH_ERROR,
        'Lỗi khi lấy chi tiết đơn hàng',
      );
    }
  }

  /**
   * Chuyển đổi từ entity UserOrder sang DTO UserOrderResponseDto
   * @param order Entity UserOrder
   * @returns DTO UserOrderResponseDto
   */
  private mapToUserOrderResponseDto(order: UserOrder): UserOrderResponseDto {
    return {
      id: order.id,
      userConvertCustomerId: order.userConvertCustomerId,
      userId: order.userId,
      productInfo: Array.isArray(order.productInfo) ? order.productInfo.map(item => ({
        productId: item.productId || 0,
        name: item.name || '',
        quantity: item.quantity || 0,
        price: item.price || 0,
        ...item
      })) : (order.productInfo ? [{
        productId: (order.productInfo as any).productId || 0,
        name: (order.productInfo as any).name || '',
        quantity: (order.productInfo as any).quantity || 0,
        price: (order.productInfo as any).price || 0,
        ...(order.productInfo as any)
      }] : []),
      billInfo: order.billInfo ? {
        subtotal: typeof order.billInfo.subtotal === 'number' ? order.billInfo.subtotal : 0,
        total: typeof order.billInfo.total === 'number' ? order.billInfo.total : 0,
        tax: typeof order.billInfo.tax === 'number' ? order.billInfo.tax : undefined,
        shipping: typeof order.billInfo.shipping === 'number' ? order.billInfo.shipping : undefined,
        paymentMethod: typeof order.billInfo.paymentMethod === 'string' ? order.billInfo.paymentMethod : undefined
      } : null,
      hasShipping: order.hasShipping,
      shippingStatus: order.shippingStatus,
      logisticInfo: order.logisticInfo ? {
        address: typeof order.logisticInfo.address === 'string' ? order.logisticInfo.address : '',
        carrier: typeof order.logisticInfo.carrier === 'string' ? order.logisticInfo.carrier : undefined,
        trackingNumber: typeof order.logisticInfo.trackingNumber === 'string' ? order.logisticInfo.trackingNumber : undefined,
        recipient: order.logisticInfo.recipient ? {
          name: typeof (order.logisticInfo.recipient as any).name === 'string' ? (order.logisticInfo.recipient as any).name : '',
          phone: typeof (order.logisticInfo.recipient as any).phone === 'string' ? (order.logisticInfo.recipient as any).phone : ''
        } : undefined
      } : null,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      source: order.source,
    };
  }

  /**
   * Chuyển đổi từ entity UserConvertCustomer sang DTO UserConvertCustomerResponseDto
   * @param customer Entity UserConvertCustomer
   * @returns DTO UserConvertCustomerResponseDto
   */
  private mapToUserConvertCustomerResponseDto(customer: UserConvertCustomer): UserConvertCustomerResponseDto {
    return {
      id: Number(customer.id), // Đảm bảo id được chuyển đổi sang kiểu number
      avatar: customer.avatar,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      platform: customer.platform,
      timezone: customer.timezone,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      userId: customer.userId,
      agentId: customer.agentId,
      metadata: customer.metadata ? Object.entries(customer.metadata).map(([fieldName, fieldValue]) => ({
        fieldName,
        fieldValue: fieldValue as string | number | boolean | string[] | number[]
      })) : [],
    };
  }
}
