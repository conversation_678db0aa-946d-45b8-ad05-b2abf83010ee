/**
 * Interface cho item tìm kiếm
 */
export interface SearchItem {
  /**
   * ID của item
   */
  id: string | number;

  /**
   * Tên hiển thị
   */
  name: string;

  /**
   * <PERSON><PERSON>ả (tùy chọn)
   */
  description?: string;

  /**
   * URL hình ảnh (tùy chọn)
   */
  image?: string;

  /**
   * Tr<PERSON>ng thái disabled
   */
  disabled?: boolean;

  /**
   * Dữ liệu bổ sung
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?: any;
}

/**
 * Interface cho tham số phân trang
 */
export interface PaginationParams {
  page: number;
  limit: number;
}

/**
 * Interface cho kết quả phân trang
 */
export interface PaginatedResult<T> {
  items: T[];
  meta?: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
