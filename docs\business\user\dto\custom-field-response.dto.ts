import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho response khi tạo trường tùy chỉnh
 */
export class CustomFieldResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 2,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Thành phần UI',
    example: 'Text Input',
  })
  component: string;

  @Expose()
  @ApiProperty({
    description: 'ID cấu hình',
    example: 'custom-text-001',
  })
  configId: string;

  @Expose()
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Số điện thoại',
  })
  label: string;

  @Expose()
  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  type: string;

  @Expose()
  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  required: boolean;

  @Expose()
  @ApiProperty({
    description: 'Cấu hình JSON',
    example: {
      validation: { pattern: '^[0-9]{10}$' },
      placeholder: 'Nhập số điện thoại',
      variant: 'outlined',
      size: 'small',
    },
  })
  configJson: any;

  @Expose()
  @ApiProperty({
    description: 'ID người dùng tạo',
    example: 1001,
    nullable: true,
  })
  userId: number | null;

  @Expose()
  @ApiProperty({
    description: 'ID nhân viên tạo',
    example: null,
    nullable: true,
  })
  employeeId: number | null;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái của trường',
    example: 'PENDING',
  })
  status: string;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createAt: number;
}
