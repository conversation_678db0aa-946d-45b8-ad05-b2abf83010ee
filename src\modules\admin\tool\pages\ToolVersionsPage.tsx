/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Button,
  Table,
  Tooltip,
  Modal,
  IconCard,
  Chip,
  Select,
  FormItem,
} from '@/shared/components/common';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import ToolVersionForm from '../components/ToolVersionForm';

// Import hooks từ module tool
import { useAdminTools, useAdminToolDetail, useSetDefaultVersion } from '../hooks/useTool';
import {
  useAdminToolVersionDetail,
  useCreateAdminToolVersion,
  useUpdateAdminToolVersion,
  useDeleteAdminToolVersion,
} from '../hooks/useToolVersion';

// Import types từ module tool
import {
  ToolVersion,
  ToolStatus,
  CreateToolVersionParams,
  UpdateToolVersionParams,
  ToolQueryParams,
} from '../types/tool.types';

/**
 * Trang quản lý phiên bản tool
 */
const ToolVersionsPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const [toolVersions, setToolVersions] = useState<ToolVersion[]>([]);
  const [selectedTool, setSelectedTool] = useState<string>('');
  const [versionToDelete, setVersionToDelete] = useState<ToolVersion | null>(null);
  const [versionToView, setVersionToView] = useState<ToolVersion | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showSetDefaultConfirm, setShowSetDefaultConfirm] = useState(false);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Sử dụng hook animation cho form tạo mới
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form xem/chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Tạo query params cho API lấy danh sách tools
  const toolsQueryParams = useMemo<ToolQueryParams>(() => {
    return {
      page: 1,
      limit: 100, // Lấy tối đa 100 tools
    };
  }, []);

  // Hooks để gọi API
  const { data: toolsData, isLoading: isLoadingTools } = useAdminTools(toolsQueryParams);

  // Hook useAdminToolDetail đã có enabled: !!id trong định nghĩa
  const { data: toolDetail, isLoading: isLoadingToolDetail } = useAdminToolDetail(selectedTool);

  // Hook useAdminToolVersionDetail đã có enabled: !!toolId && !!versionId trong định nghĩa
  const { data: versionDetail, isLoading: isLoadingVersionDetail } = useAdminToolVersionDetail(
    selectedTool,
    versionToView?.id || ''
  );

  const { mutateAsync: createVersion, isPending: isCreating } = useCreateAdminToolVersion();
  const { mutateAsync: updateVersion, isPending: isUpdating } = useUpdateAdminToolVersion();
  const { mutateAsync: deleteVersion, isPending: isDeleting } = useDeleteAdminToolVersion();
  const { mutateAsync: setDefaultVersion, isPending: isSettingDefault } = useSetDefaultVersion();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (toolDetail?.versions) {
      setToolVersions(toolDetail.versions);
      console.log('Versions loaded from toolDetail:', toolDetail.versions);
    } else {
      setToolVersions([]);
    }
  }, [toolDetail]);

  // Cập nhật versionToView khi có dữ liệu chi tiết
  useEffect(() => {
    if (versionDetail && versionToView) {
      setVersionToView(versionDetail);
    }
  }, [versionDetail]);

  // Xử lý thay đổi tool được chọn
  const handleToolChange = useCallback((value: string | number | string[] | number[]) => {
    const toolId = value as string;
    console.log('Selected tool changed to:', toolId);
    setSelectedTool(toolId);

    // Reset các state liên quan khi thay đổi tool
    setVersionToView(null);
    setVersionToDelete(null);
    setShowDeleteConfirm(false);
    setShowSetDefaultConfirm(false);
  }, []);

  // Xử lý submit form tạo phiên bản
  const handleSubmitCreateVersion = useCallback(
    async (values: CreateToolVersionParams | UpdateToolVersionParams) => {
      if (!selectedTool) return;

      try {
        await createVersion({ toolId: selectedTool, data: values as CreateToolVersionParams });
        hideCreateForm();
      } catch (error) {
        console.error('Error creating tool version:', error);
      }
    },
    [createVersion, hideCreateForm, selectedTool]
  );

  // Xử lý submit form chỉnh sửa phiên bản
  const handleSubmitEditVersion = useCallback(
    async (values: CreateToolVersionParams | UpdateToolVersionParams) => {
      if (!selectedTool || !versionToView) return;

      try {
        await updateVersion({
          toolId: selectedTool,
          versionId: versionToView.id,
          data: values as UpdateToolVersionParams,
        });
        hideEditForm();
        setVersionToView(null);
      } catch (error) {
        console.error('Error updating tool version:', error);
      }
    },
    [updateVersion, hideEditForm, selectedTool, versionToView]
  );

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((version: ToolVersion) => {
    setVersionToDelete(version);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setVersionToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!selectedTool || !versionToDelete) return;

    try {
      await deleteVersion({ toolId: selectedTool, versionId: versionToDelete.id });
      setShowDeleteConfirm(false);
      setVersionToDelete(null);
    } catch (error) {
      console.error('Error deleting tool version:', error);
    }
  }, [versionToDelete, deleteVersion, selectedTool]);

  // Xử lý hiển thị popup xác nhận đặt làm mặc định
  const handleShowSetDefaultConfirm = useCallback((version: ToolVersion) => {
    setVersionToView(version);
    setShowSetDefaultConfirm(true);
  }, []);

  // Xử lý hủy đặt làm mặc định
  const handleCancelSetDefault = useCallback(() => {
    setShowSetDefaultConfirm(false);
    setVersionToView(null);
  }, []);

  // Xử lý xác nhận đặt làm mặc định
  const handleConfirmSetDefault = useCallback(async () => {
    if (!selectedTool || !versionToView) return;

    try {
      await setDefaultVersion({ toolId: selectedTool, versionId: versionToView.id });
      setShowSetDefaultConfirm(false);
      setVersionToView(null);
    } catch (error) {
      console.error('Error setting default version:', error);
    }
  }, [versionToView, setDefaultVersion, selectedTool]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý hiển thị form xem chi tiết phiên bản
  const handleShowViewForm = useCallback(
    (version: ToolVersion) => {
      setVersionToView(version);
      showEditForm();
    },
    [showEditForm]
  );

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'versionNumber',
        title: t('admin.tool.table.versionNumber', 'Phiên bản'),
        dataIndex: 'versionNumber',
        width: '10%',
        render: (value: unknown) => {
          return <span>v{String(value)}</span>;
        },
      },
      {
        key: 'toolName',
        title: t('admin.tool.table.toolName', 'Tên hiển thị'),
        dataIndex: 'toolName',
        width: '20%',
      },
      {
        key: 'status',
        title: t('admin.tool.table.status', 'Trạng thái'),
        dataIndex: 'status',
        width: '15%',
        render: (value: unknown) => {
          const status = value as ToolStatus;
          let variant: 'default' | 'primary' | 'success' | 'warning' | 'danger' = 'default';

          switch (status) {
            case ToolStatus.APPROVED:
              variant = 'success';
              break;
            case ToolStatus.DRAFT:
              variant = 'warning';
              break;
            case ToolStatus.DEPRECATED:
              variant = 'danger';
              break;
          }

          return (
            <Chip size="sm" variant={variant}>
              {t(`admin.tool.status.${status}`, status)}
            </Chip>
          );
        },
      },
      {
        key: 'isDefault',
        title: t('admin.tool.table.isDefault', 'Mặc định'),
        width: '10%',
        render: (_: unknown, record: ToolVersion) => {
          const isDefault = toolDetail?.defaultVersion?.id === record.id;
          return isDefault ? (
            <Chip size="sm" variant="primary">
              {t('admin.tool.default', 'Mặc định')}
            </Chip>
          ) : null;
        },
      },
      {
        key: 'createdAt',
        title: t('admin.tool.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        render: (value: unknown) => formatDate(value as number),
      },
      {
        key: 'createdBy',
        title: t('admin.tool.table.createdBy', 'Người tạo'),
        dataIndex: 'createdBy',
        width: '15%',
        render: (value: unknown) => {
          const creator = value as { name: string; email?: string } | undefined;
          return (
            <div className="flex flex-col">
              <span>{creator?.name || t('admin.tool.unknownUser', 'Không xác định')}</span>
              {creator?.email && <span className="text-xs text-gray-500">{creator.email}</span>}
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('common.actions', 'Thao tác'),
        render: (_: unknown, record: ToolVersion) => (
          <div className="flex space-x-2">
            <Tooltip content={t('common.view', 'Xem')}>
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => handleShowViewForm(record)}
              />
            </Tooltip>

            {toolDetail?.defaultVersion?.id !== record.id && (
              <Tooltip content={t('admin.tool.setDefault', 'Đặt làm mặc định')}>
                <IconCard
                  icon="star"
                  variant="primary"
                  size="sm"
                  onClick={() => handleShowSetDefaultConfirm(record)}
                />
              </Tooltip>
            )}

            <Tooltip content={t('common.delete', 'Xóa')}>
              <IconCard
                icon="trash"
                variant="primary"
                size="sm"
                onClick={() => handleShowDeleteConfirm(record)}
              />
            </Tooltip>
          </div>
        ),
      },
    ];

    return allColumns;
  }, [t, handleShowDeleteConfirm, handleShowViewForm, handleShowSetDefaultConfirm, toolDetail]);

  return (
    <div>
      <div className="space-y-4">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
          <div className="w-full md:w-1/3">
            <FormItem>
              <Select
                value={selectedTool}
                onChange={handleToolChange}
                options={
                  toolsData?.items?.map(tool => ({
                    value: tool.id,
                    label: tool.name,
                  })) || []
                }
                placeholder={t('admin.tool.selectToolPlaceholder', 'Chọn tool để xem phiên bản')}
                loading={isLoadingTools}
              />
            </FormItem>
          </div>

          <div className="flex-grow">
            <MenuIconBar
              onAdd={() => {
                if (!selectedTool) {
                  alert(
                    t('admin.tool.selectToolFirst', 'Vui lòng chọn tool trước khi thêm phiên bản')
                  );
                  return;
                }
                showCreateForm();
              }}
              onColumnVisibilityChange={handleColumnVisibilityChange}
              columns={visibleColumns}
              showDateFilter={false}
              showColumnFilter={true}
              onSearch={() => {}}
              items={[]}
            />
          </div>
        </div>

        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <ToolVersionForm
            toolId={selectedTool}
            onSubmit={handleSubmitCreateVersion}
            onCancel={hideCreateForm}
            isLoading={isCreating}
          />
        </SlideInForm>

        {/* SlideInForm cho form xem/chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {versionToView && (
            <ToolVersionForm
              initialValues={versionToView}
              toolId={selectedTool}
              onSubmit={handleSubmitEditVersion}
              onCancel={hideEditForm}
              isLoading={isUpdating || isLoadingVersionDetail}
              isEdit={true}
            />
          )}
        </SlideInForm>

        <Card className="overflow-hidden">
          {selectedTool ? (
            <>
              {isLoadingToolDetail ? (
                <div className="p-8 text-center">
                  <p className="text-gray-500">{t('common.loading', 'Đang tải...')}</p>
                </div>
              ) : toolVersions && toolVersions.length > 0 ? (
                <Table<ToolVersion>
                  columns={columns}
                  data={toolVersions}
                  rowKey="id"
                  loading={isLoadingToolDetail}
                  sortable={false}
                  pagination={false}
                />
              ) : (
                <div className="p-8 text-center">
                  <p className="text-gray-500">
                    {t('admin.tool.noVersions', 'Không có phiên bản nào')}
                  </p>
                </div>
              )}
            </>
          ) : (
            <div className="p-8 text-center">
              <p className="text-gray-500">
                {t('admin.tool.selectToolFirst', 'Vui lòng chọn tool để xem phiên bản')}
              </p>
            </div>
          )}
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        footer={
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleCancelDelete} disabled={isDeleting}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="danger" onClick={handleConfirmDelete} disabled={isDeleting}>
              {isDeleting ? t('common.processing', 'Đang xử lý...') : t('common.delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <p>
          {t(
            'admin.tool.confirmDeleteVersionMessage',
            'Bạn có chắc chắn muốn xóa phiên bản tool này? Hành động này không thể hoàn tác.'
          )}
        </p>
        {versionToDelete && (
          <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded">
            <p className="font-medium">
              {versionToDelete.toolName} (v{versionToDelete.versionNumber})
            </p>
          </div>
        )}
      </Modal>

      {/* Modal xác nhận đặt làm mặc định */}
      <Modal
        isOpen={showSetDefaultConfirm}
        onClose={handleCancelSetDefault}
        title={t('admin.tool.confirmSetDefault', 'Xác nhận đặt làm mặc định')}
        footer={
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleCancelSetDefault} disabled={isSettingDefault}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="primary" onClick={handleConfirmSetDefault} disabled={isSettingDefault}>
              {isSettingDefault
                ? t('common.processing', 'Đang xử lý...')
                : t('common.confirm', 'Xác nhận')}
            </Button>
          </div>
        }
      >
        <p>
          {t(
            'admin.tool.confirmSetDefaultMessage',
            'Bạn có chắc chắn muốn đặt phiên bản này làm mặc định cho tool?'
          )}
        </p>
        {versionToView && (
          <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded">
            <p className="font-medium">
              {versionToView.toolName} (v{versionToView.versionNumber})
            </p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ToolVersionsPage;
