/**
 * Trang quản lý vai trò
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON>, <PERSON>ton, Card, Container, Typography } from '@/shared/components/common';
import { RoleStatus, RoleDto } from '../types/role.types';
import { useAllRoles } from '../hooks/useRoleQuery';

/**
 * Trang quản lý vai trò
 */
const RolePage: React.FC = () => {
  const { t } = useTranslation(['employee', 'common']);

  // State for search term
  const [searchTerm, setSearchTerm] = useState('');

  // Queries
  const rolesQuery = useAllRoles();

  // State lưu trữ dữ liệu vai trò
  const [roles, setRoles] = useState<RoleDto[]>([]);

  // Effect to update state when API data is available
  useEffect(() => {
    if (rolesQuery.data) {
      setRoles(rolesQuery.data);
    }
  }, [rolesQuery.data]);

  // Effect to filter roles based on search term
  useEffect(() => {
    if (rolesQuery.data) {
      if (searchTerm.trim() === '') {
        // If search term is empty, show all roles
        setRoles(rolesQuery.data);
      } else {
        // Filter roles based on search term
        const filteredRoles = rolesQuery.data.filter(
          role =>
            role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (role.description && role.description.toLowerCase().includes(searchTerm.toLowerCase()))
        );
        setRoles(filteredRoles);
      }
    }
  }, [searchTerm, rolesQuery.data]);

  // Render badge trạng thái
  const renderStatusBadge = (status: RoleStatus) => {
    switch (status) {
      case RoleStatus.ACTIVE:
        return <Badge variant="success">{t('common.active')}</Badge>;
      case RoleStatus.INACTIVE:
        return <Badge variant="danger">{t('common.inactive')}</Badge>;
      default:
        return null;
    }
  };

  return (
    <Container>
      <div className="py-6">
        <Typography variant="h4" className="font-bold mb-2">
          {t('employee:role.title')}
        </Typography>
        <Typography variant="body1" className="text-muted mb-6">
          {t('employee:role.description')}
        </Typography>

        <Card className="mb-6">
          <div className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <input
                  type="text"
                  placeholder={t('common.search')}
                  className="px-4 py-2 border border-gray-300 rounded-md"
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                />
              </div>
              <div>
                <Button variant="primary">{t('common.add')}</Button>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            {rolesQuery.isLoading ? (
              <div className="text-center py-8">{t('common.loading')}</div>
            ) : roles.length === 0 ? (
              <div className="text-center py-8">{t('common.noData')}</div>
            ) : (
              <div className="space-y-4">
                {roles.map((role: RoleDto) => (
                  <div key={role.id} className="border p-4 rounded-md">
                    <div className="flex justify-between">
                      <div>
                        <Typography variant="h6">{role.name}</Typography>
                        <Typography variant="body2" className="text-muted">
                          {role.description}
                        </Typography>
                      </div>
                      <div>
                        {renderStatusBadge(role.status)}
                        {role.isSystem ? (
                          <Badge variant="info" className="ml-2">
                            {t('employee:role.system')}
                          </Badge>
                        ) : (
                          <Badge variant="primary" className="ml-2">
                            {t('employee:role.custom')}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </Card>
      </div>
    </Container>
  );
};

export default RolePage;
