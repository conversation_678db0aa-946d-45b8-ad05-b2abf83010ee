import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AdminToolVersionService } from '../services/tool-version.service';
import {
  ToolVersion,
  CreateToolVersionParams,
  UpdateToolVersionParams,
} from '../types/tool.types';
import { ADMIN_TOOL_QUERY_KEYS } from './useTool';

// Khởi tạo service
const toolVersionService = new AdminToolVersionService();

// Các key cho React Query
export const ADMIN_TOOL_VERSION_QUERY_KEYS = {
  all: ['admin', 'tool-versions'] as const,
  lists: () => [...ADMIN_TOOL_VERSION_QUERY_KEYS.all, 'list'] as const,
  list: (toolId: string) => [...ADMIN_TOOL_VERSION_QUERY_KEYS.lists(), toolId] as const,
  details: () => [...ADMIN_TOOL_VERSION_QUERY_KEYS.all, 'detail'] as const,
  detail: (toolId: string, versionId: string) =>
    [...ADMIN_TOOL_VERSION_QUERY_KEYS.details(), toolId, versionId] as const,
};



/**
 * Hook để lấy thông tin chi tiết phiên bản
 * @param toolId ID của tool
 * @param versionId ID của phiên bản
 * @returns Query object
 */
export const useAdminToolVersionDetail = (toolId: string, versionId: string) => {
  return useQuery<ToolVersion>({
    queryKey: ADMIN_TOOL_VERSION_QUERY_KEYS.detail(toolId, versionId),
    queryFn: () => toolVersionService.getVersionById(toolId, versionId),
    enabled: !!toolId && !!versionId,
  });
};

/**
 * Hook để tạo phiên bản mới cho tool
 * @returns Mutation object
 */
export const useCreateAdminToolVersion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ toolId, data }: { toolId: string; data: CreateToolVersionParams }) =>
      toolVersionService.createVersion(toolId, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết tool
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_QUERY_KEYS.detail(variables.toolId),
      });
    },
  });
};

/**
 * Hook để cập nhật thông tin phiên bản
 * @returns Mutation object
 */
export const useUpdateAdminToolVersion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      toolId,
      versionId,
      data,
    }: {
      toolId: string;
      versionId: string;
      data: UpdateToolVersionParams;
    }) => toolVersionService.updateVersion(toolId, versionId, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết phiên bản và chi tiết tool
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_VERSION_QUERY_KEYS.detail(variables.toolId, variables.versionId),
      });
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_QUERY_KEYS.detail(variables.toolId),
      });
    },
  });
};

/**
 * Hook để xóa phiên bản
 * @returns Mutation object
 */
export const useDeleteAdminToolVersion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ toolId, versionId }: { toolId: string; versionId: string }) =>
      toolVersionService.deleteVersion(toolId, versionId),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết tool
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_QUERY_KEYS.detail(variables.toolId),
      });
    },
  });
};
