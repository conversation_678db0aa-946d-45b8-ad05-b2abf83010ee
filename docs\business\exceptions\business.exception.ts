import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi cho module Business
 */
export const BUSINESS_ERROR_CODES = {
  INVALID_INPUT: new ErrorCode(
    30000,
    'Lỗi khóa nhập dữ liệu',
    HttpStatus.BAD_REQUEST,
  ),

  WAREHOUSE_VALIDATION_FAILED: new ErrorCode(
    30000,
    'Lỗi kiểm tra dữ liệu kho',
    HttpStatus.BAD_REQUEST,
  ),

  WAREHOUSE_FETCH_FAILED: new ErrorCode(
    30001,
    'Lỗi khi lấy thông tin kho',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  WAREHOUSE_DELETE_FAILED: new ErrorCode(
    30002,
    'Lỗi khi xóa kho',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Inventory exceptions (30201-30220)
  INVENTORY_NOT_FOUND: new ErrorCode(
    30201,
    'Không tìm thấy tồn kho',
    HttpStatus.NOT_FOUND,
  ),
  INVENTORY_CREATION_FAILED: new ErrorCode(
    30202,
    'Lỗi khi tạo tồn kho',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVENTORY_UPDATE_FAILED: new ErrorCode(
    30203,
    'Lỗi khi cập nhật tồn kho',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVENTORY_DELETE_FAILED: new ErrorCode(
    30204,
    'Lỗi khi xóa tồn kho',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVENTORY_FETCH_FAILED: new ErrorCode(
    30205,
    'Lỗi khi lấy thông tin tồn kho',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVENTORY_VALIDATION_FAILED: new ErrorCode(
    30206,
    'Lỗi kiểm tra dữ liệu tồn kho',
    HttpStatus.BAD_REQUEST,
  ),
  INVENTORY_ACCESS_DENIED: new ErrorCode(
    30207,
    'Bạn không có quyền truy cập tồn kho này',
    HttpStatus.FORBIDDEN,
  ),

  // Product exceptions (30001-30020)
  CLASSIFICATION_VALIDATION_FAILED: new ErrorCode(
    30001,
    'Classification validation failed',
    HttpStatus.BAD_REQUEST,
  ),

  INTEGRATION_CREATION_FAILED: new ErrorCode(
    30000,
    'Failed to create integration',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  INTEGRATION_UPDATE_FAILED: new ErrorCode(
    30000,
    'Failed to update integration',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  INTEGRATION_DELETION_FAILED: new ErrorCode(
    30000,
    'Failed to delete integration',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  INTEGRATION_FIND_FAILED: new ErrorCode(
    30000,
    'Failed to find integrations',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  CUSTOM_FIELD_DELETE_FAILED: new ErrorCode(
    30000,
    'Failed to delete custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  PRODUCT_MAPPING_FAILED: new ErrorCode(
    30000,
    'Failed to map product to DTO',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  PRODUCT_NOT_FOUND: new ErrorCode(
    30001,
    'Product not found',
    HttpStatus.NOT_FOUND,
  ),
  PRODUCT_CREATION_FAILED: new ErrorCode(
    30002,
    'Failed to create product',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_UPDATE_FAILED: new ErrorCode(
    30003,
    'Failed to update product',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_DELETION_FAILED: new ErrorCode(
    30004,
    'Failed to delete product',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_FIND_FAILED: new ErrorCode(
    30005,
    'Failed to find products',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_UNAUTHORIZED: new ErrorCode(
    30006,
    'Unauthorized to access this product',
    HttpStatus.FORBIDDEN,
  ),

  // Custom Field exceptions (30101-30120)
  CUSTOM_FIELD_NOT_FOUND: new ErrorCode(
    30101,
    'Custom field not found',
    HttpStatus.NOT_FOUND,
  ),
  CUSTOM_FIELD_CREATION_FAILED: new ErrorCode(
    30102,
    'Failed to create custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CUSTOM_FIELD_UPDATE_FAILED: new ErrorCode(
    30103,
    'Failed to update custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CUSTOM_FIELD_DELETION_FAILED: new ErrorCode(
    30104,
    'Failed to delete custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CUSTOM_FIELD_FIND_FAILED: new ErrorCode(
    30105,
    'Failed to find custom fields',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CUSTOM_FIELD_REQUIRED: new ErrorCode(
    30106,
    'Custom field is required',
    HttpStatus.BAD_REQUEST,
  ),
  CUSTOM_FIELD_VALIDATION_FAILED: new ErrorCode(
    30107,
    'Custom field validation failed',
    HttpStatus.BAD_REQUEST,
  ),
  CUSTOM_FIELD_NOT_IN_GROUP: new ErrorCode(
    30108,
    'Custom field not in group',
    HttpStatus.BAD_REQUEST,
  ),

  // Group Form exceptions (30121-30140)
  GROUP_FORM_NOT_FOUND: new ErrorCode(
    30121,
    'Group form not found',
    HttpStatus.NOT_FOUND,
  ),
  GROUP_FORM_CREATION_FAILED: new ErrorCode(
    30122,
    'Failed to create group form',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GROUP_FORM_UPDATE_FAILED: new ErrorCode(
    30123,
    'Failed to update group form',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GROUP_FORM_DELETION_FAILED: new ErrorCode(
    30124,
    'Failed to delete group form',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GROUP_FORM_FIND_FAILED: new ErrorCode(
    30125,
    'Failed to find group forms',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  GROUP_FORM_INVALID_SORT_FIELD: new ErrorCode(
    30126,
    'Invalid sort field for group form',
    HttpStatus.BAD_REQUEST,
  ),

  // Classification exceptions (30141-30160)
  CLASSIFICATION_NOT_FOUND: new ErrorCode(
    30141,
    'Classification not found',
    HttpStatus.NOT_FOUND,
  ),
  CLASSIFICATION_CREATION_FAILED: new ErrorCode(
    30142,
    'Failed to create classification',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CLASSIFICATION_UPDATE_FAILED: new ErrorCode(
    30143,
    'Failed to update classification',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CLASSIFICATION_DELETION_FAILED: new ErrorCode(
    30144,
    'Failed to delete classification',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CLASSIFICATION_FIND_FAILED: new ErrorCode(
    30145,
    'Failed to find classifications',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Order exceptions (30021-30040)
  ORDER_NOT_FOUND: new ErrorCode(
    30021,
    'Order not found',
    HttpStatus.NOT_FOUND,
  ),
  ORDER_CREATION_FAILED: new ErrorCode(
    30022,
    'Failed to create order',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ORDER_UPDATE_FAILED: new ErrorCode(
    30023,
    'Failed to update order',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ORDER_DELETION_FAILED: new ErrorCode(
    30024,
    'Failed to delete order',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ORDER_FIND_FAILED: new ErrorCode(
    30025,
    'Failed to find orders',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ORDER_UNAUTHORIZED: new ErrorCode(
    30026,
    'Unauthorized to access this order',
    HttpStatus.FORBIDDEN,
  ),

  // Warehouse exceptions (30041-30060)
  WAREHOUSE_NOT_FOUND: new ErrorCode(
    30041,
    'Warehouse not found',
    HttpStatus.NOT_FOUND,
  ),
  WAREHOUSE_CREATION_FAILED: new ErrorCode(
    30042,
    'Failed to create warehouse',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_UPDATE_FAILED: new ErrorCode(
    30043,
    'Failed to update warehouse',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_DELETION_FAILED: new ErrorCode(
    30044,
    'Failed to delete warehouse',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_FIND_FAILED: new ErrorCode(
    30045,
    'Failed to find warehouses',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_NAME_ALREADY_EXISTS: new ErrorCode(
    30046,
    'Warehouse name already exists',
    HttpStatus.CONFLICT,
  ),
  WAREHOUSE_VALIDATION_ERROR: new ErrorCode(
    30047,
    'Warehouse validation error',
    HttpStatus.BAD_REQUEST,
  ),
  WAREHOUSE_ACCESS_DENIED: new ErrorCode(
    30048,
    'Bạn không có quyền truy cập kho này',
    HttpStatus.FORBIDDEN,
  ),
  WAREHOUSE_CUSTOM_FIELD_NOT_FOUND: new ErrorCode(
    30048,
    'Warehouse custom field not found',
    HttpStatus.NOT_FOUND,
  ),
  WAREHOUSE_CUSTOM_FIELD_ALREADY_EXISTS: new ErrorCode(
    30049,
    'Warehouse custom field already exists',
    HttpStatus.CONFLICT,
  ),
  WAREHOUSE_CUSTOM_FIELD_CREATION_FAILED: new ErrorCode(
    30050,
    'Failed to create warehouse custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_CUSTOM_FIELD_UPDATE_FAILED: new ErrorCode(
    30051,
    'Failed to update warehouse custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_CUSTOM_FIELD_DELETION_FAILED: new ErrorCode(
    30052,
    'Failed to delete warehouse custom field',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_CUSTOM_FIELD_FIND_FAILED: new ErrorCode(
    30053,
    'Failed to find warehouse custom fields',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WAREHOUSE_CUSTOM_FIELD_VALIDATION_ERROR: new ErrorCode(
    30054,
    'Warehouse custom field validation error',
    HttpStatus.BAD_REQUEST,
  ),

  // Physical Warehouse exceptions (30221-30240)
  WAREHOUSE_TYPE_MISMATCH: new ErrorCode(
    30221,
    'Loại kho không phù hợp',
    HttpStatus.BAD_REQUEST,
  ),
  WAREHOUSE_ALREADY_EXISTS: new ErrorCode(
    30222,
    'Kho vật lý đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  // File exceptions (30241-30260)
  FILE_NOT_FOUND: new ErrorCode(
    30241,
    'Không tìm thấy file',
    HttpStatus.NOT_FOUND,
  ),
  FILE_CREATION_FAILED: new ErrorCode(
    30242,
    'Lỗi khi tạo file',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FILE_UPDATE_FAILED: new ErrorCode(
    30243,
    'Lỗi khi cập nhật file',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FILE_DELETE_FAILED: new ErrorCode(
    30244,
    'Lỗi khi xóa file',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FILE_FETCH_FAILED: new ErrorCode(
    30245,
    'Lỗi khi lấy thông tin file',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FILE_ACCESS_DENIED: new ErrorCode(
    30246,
    'Bạn không có quyền truy cập file này',
    HttpStatus.FORBIDDEN,
  ),

  // Folder exceptions (30261-30280)
  FOLDER_NOT_FOUND: new ErrorCode(
    30261,
    'Không tìm thấy thư mục',
    HttpStatus.NOT_FOUND,
  ),
  FOLDER_CREATION_FAILED: new ErrorCode(
    30262,
    'Lỗi khi tạo thư mục',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FOLDER_UPDATE_FAILED: new ErrorCode(
    30263,
    'Lỗi khi cập nhật thư mục',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FOLDER_DELETE_FAILED: new ErrorCode(
    30264,
    'Lỗi khi xóa thư mục',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FOLDER_FETCH_FAILED: new ErrorCode(
    30265,
    'Lỗi khi lấy thông tin thư mục',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Supplier exceptions (30061-30080)
  SUPPLIER_NOT_FOUND: new ErrorCode(
    30061,
    'Supplier not found',
    HttpStatus.NOT_FOUND,
  ),
  SUPPLIER_CREATION_FAILED: new ErrorCode(
    30062,
    'Failed to create supplier',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  SUPPLIER_UPDATE_FAILED: new ErrorCode(
    30063,
    'Failed to update supplier',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  SUPPLIER_DELETION_FAILED: new ErrorCode(
    30064,
    'Failed to delete supplier',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  SUPPLIER_FIND_FAILED: new ErrorCode(
    30065,
    'Failed to find suppliers',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // User Convert exceptions (30161-30180)
  CONVERT_NOT_FOUND: new ErrorCode(
    30161,
    'Convert record not found',
    HttpStatus.NOT_FOUND,
  ),
  CONVERT_CREATION_FAILED: new ErrorCode(
    30162,
    'Failed to create convert record',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_UPDATE_FAILED: new ErrorCode(
    30163,
    'Failed to update convert record',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_DELETION_FAILED: new ErrorCode(
    30164,
    'Failed to delete convert record',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_FIND_FAILED: new ErrorCode(
    30165,
    'Failed to find convert records',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_ACCESS_DENIED: new ErrorCode(
    30166,
    'Access denied to this convert record',
    HttpStatus.FORBIDDEN,
  ),

  // User Convert Customer exceptions (30181-30200)
  CONVERT_CUSTOMER_NOT_FOUND: new ErrorCode(
    30181,
    'Convert customer not found',
    HttpStatus.NOT_FOUND,
  ),
  CONVERT_CUSTOMER_CREATION_FAILED: new ErrorCode(
    30182,
    'Failed to create convert customer',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_UPDATE_FAILED: new ErrorCode(
    30183,
    'Failed to update convert customer',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_DELETION_FAILED: new ErrorCode(
    30184,
    'Failed to delete convert customer',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_FIND_FAILED: new ErrorCode(
    30185,
    'Failed to find convert customers',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERT_CUSTOMER_ACCESS_DENIED: new ErrorCode(
    30186,
    'Access denied to this convert customer',
    HttpStatus.FORBIDDEN,
  ),

  // Order specific exceptions
  ORDER_ACCESS_DENIED: new ErrorCode(
    30027,
    'Access denied to this order',
    HttpStatus.FORBIDDEN,
  ),
};
