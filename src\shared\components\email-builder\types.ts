// <PERSON><PERSON>nh nghĩa kiểu dữ liệu cho asset (hình ảnh)
export interface Asset {
  id: string;
  type: string;
  src: string;
  alt?: string;
  width?: number;
  height?: number;
  name?: string;
  size?: number;
  category?: string;
  selected?: boolean;
}

// Định nghĩa kiểu dữ liệu cho các phần tử email
export interface EmailElement {
  id: string;
  type: string;
  content?: string;
  src?: string;
  url?: string;
  text?: string;
  alt?: string;
  children?: EmailElement[];
  parent?: string;
  draggable?: boolean;
  removable?: boolean;
  editable?: boolean;
  selectable?: boolean;
  hoverable?: boolean;
  copyable?: boolean;
  resizable?: boolean;
  style?: {
    // Typography
    color?: string;
    backgroundColor?: string;
    fontSize?: number;
    fontFamily?: string;
    fontWeight?: string | number;
    fontStyle?: string;
    textAlign?: string;
    lineHeight?: number | string;
    letterSpacing?: number | string;
    textDecoration?: string;
    textTransform?: string;

    // Spacing
    paddingTop?: number;
    paddingBottom?: number;
    paddingLeft?: number;
    paddingRight?: number;
    padding?: number | string;
    marginTop?: number;
    marginBottom?: number;
    marginLeft?: number;
    marginRight?: number;
    margin?: string | number;

    // Borders
    borderWidth?: number;
    borderStyle?: string;
    borderColor?: string;
    borderTop?: string;
    borderBottom?: string;
    borderLeft?: string;
    borderRight?: string;
    borderRadius?: number | string;
    borderTopWidth?: number;
    borderRightWidth?: number;
    borderBottomWidth?: number;
    borderLeftWidth?: number;

    // Box Shadow
    boxShadow?: string;
    boxShadowX?: number;
    boxShadowY?: number;
    boxShadowBlur?: number;
    boxShadowColor?: string;

    // Dimensions
    width?: string | number;
    height?: string | number;
    minWidth?: string | number;
    maxWidth?: string | number;
    minHeight?: string | number;
    maxHeight?: string | number;

    // Display & Position
    display?: string;
    position?: string;
    top?: number | string;
    right?: number | string;
    bottom?: number | string;
    left?: number | string;
    zIndex?: number;
    float?: string;
    overflow?: string;

    // Flexbox
    flexDirection?: string;
    justifyContent?: string;
    alignItems?: string;
    flexWrap?: string;
    flex?: string | number;
    gap?: number | string;
  };
  attributes?: {
    [key: string]: string;
  };
  // Thuộc tính cho heading
  headingType?: string;

  // Thuộc tính cho columns
  isContainer?: boolean;
  columnCount?: number;
  columnGap?: number;
  columnPadding?: number;
  columnBgColor?: string;
  columnPosition?: 'left' | 'right' | 'center';
  selectedChildId?: string; // ID của phần tử con đang được chọn trong column

  [key: string]: unknown; // Cho phép các thuộc tính động
}

// Dữ liệu email
export interface EmailData {
  name: string;
  subject: string;
  preheader: string;
  [key: string]: unknown;
}

// Props cho component EmailBuilder
export interface EmailBuilderProps {
  initialValue?: string;
  onContentChange?: (htmlContent: string) => void;
  compactMode?: boolean;
}

// History management for undo/redo
export interface HistoryState {
  emailElements: EmailElement[];
  selectedIndex: number | null;
}

// Email template
export interface EmailTemplate {
  id: string;
  name: string;
  category: string;
  description: string;
  elements: EmailElement[];
}

// Asset category
export interface AssetCategory {
  id: string;
  name: string;
  count: number;
}

// Asset upload response
export interface AssetUploadResponse {
  id: string;
  url: string;
  name: string;
  type: string;
  size: number;
  width?: number;
  height?: number;
}

// Viewport size for responsive preview
export type ViewportSize = 'desktop' | 'tablet' | 'mobile';

// Theme settings
export interface ThemeSettings {
  primaryColor: string;
  secondaryColor: string;
  textColor: string;
  backgroundColor: string;
  fontFamily: string;
  borderRadius: number;
}

// Email export format
export type ExportFormat = 'html' | 'json' | 'image' | 'pdf';
