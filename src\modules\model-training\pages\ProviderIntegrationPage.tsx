import React, { useState } from 'react';
import {
  Container,
  Typography,
  Card,
  ConfirmDeleteModal,
  Button,
} from '@/shared/components/common';
import { Plus } from 'lucide-react';
import { ProviderSelector, ProviderForm, ProviderTable, ProviderModelForm } from '../components';
import { useProviderModels, useCreateProviderModel, useDeleteProviderModel } from '../hooks/useProviderModelQuery';
import { ProviderType } from '../types/provider.types';
import { ProviderModelQueryParams, CreateProviderModelDto, ProviderModel } from '../types/provider-model.types';

/**
 * Trang tích hợp nhà cung cấp AI
 */
const ProviderIntegrationPage: React.FC = () => {
  const [selectedProvider, setSelectedProvider] = useState<ProviderType | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentProvider, setCurrentProvider] = useState<ProviderModel | null>(null);
  const [providerToDelete, setProviderToDelete] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // State cho tham số truy vấn
  const [queryParams, setQueryParams] = useState<ProviderModelQueryParams>({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortDirection: 'DESC'
  });

  // Queries và mutations
  const { data: providersData, isLoading } = useProviderModels(queryParams);
  const createProviderMutation = useCreateProviderModel();
  const deleteProviderMutation = useDeleteProviderModel();

  // Xử lý thay đổi trang và sắp xếp
  const handlePageChange = (page: number, pageSize: number) => {
    setQueryParams(prev => ({
      ...prev,
      page,
      limit: pageSize
    }));
  };

  const handleSortChange = (column: string | null, order: 'asc' | 'desc' | null) => {
    if (column && order) {
      setQueryParams(prev => ({
        ...prev,
        sortBy: column,
        sortDirection: order === 'asc' ? 'ASC' : 'DESC'
      }));
    }
  };

  // Handlers
  const handleSelectProvider = (provider: ProviderType) => {
    setSelectedProvider(provider);
  };

  const handleEditProvider = (provider: ProviderModel) => {
    console.log('Editing provider:', provider);

    // Đảm bảo provider có đầy đủ thông tin cần thiết
    const providerToEdit: ProviderModel = {
      id: provider.id,
      name: provider.name || '',
      type: provider.type || ProviderType.OPENAI,
      apiKey: provider.apiKey || '',
      createdAt: provider.createdAt
    };

    console.log('Provider to edit:', providerToEdit);
    setCurrentProvider(providerToEdit);
    setIsEditMode(true);
  };

  const handleDeleteProvider = (id: string) => {
    setProviderToDelete(id);
    setIsDeleteModalOpen(true);
  };

  const handleSubmitProvider = (data: Partial<CreateProviderModelDto>) => {
    // Đảm bảo trường type luôn được gửi đi
    const payload: CreateProviderModelDto = {
      name: data.name || '',
      apiKey: data.apiKey || '',
      type: data.type || selectedProvider || ProviderType.OPENAI
    };

    createProviderMutation.mutate(payload, {
      onSuccess: () => {
        setCurrentProvider(null);
        setIsEditMode(false);
      },
    });
  };

  const confirmDeleteProvider = () => {
    if (providerToDelete) {
      console.log('Deleting provider model:', providerToDelete);

      deleteProviderMutation.mutate(providerToDelete, {
        onSuccess: () => {
          setIsDeleteModalOpen(false);
          setProviderToDelete(null);
        },
      });
    }
  };

  return (
    <Container>
      <div className="space-y-8">
        {/* Phần chọn nhà cung cấp */}
        <Card className="p-6">
          <ProviderSelector
            selectedProvider={selectedProvider}
            onSelectProvider={handleSelectProvider}
          />

          {/* Form thêm API key */}
          <div className="mt-8 border-t pt-6">
            <div className="flex justify-between items-center mb-4">
              <Typography variant="h6">
                {isEditMode ? 'Cập nhật Provider Model' : 'Thêm API Key'}
              </Typography>
              {isEditMode && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setIsEditMode(false);
                    setCurrentProvider(null);
                  }}
                >
                  Quay lại
                </Button>
              )}
            </div>

            {isEditMode ? (
              <ProviderModelForm
                initialValues={currentProvider || { type: selectedProvider || ProviderType.OPENAI }}
                onSubmit={handleSubmitProvider}
                isLoading={createProviderMutation.isPending}
                isUpdate={true}
              />
            ) : (
              <ProviderForm
                initialValues={{ type: selectedProvider || ProviderType.OPENAI }}
                onSubmit={handleSubmitProvider}
                isLoading={createProviderMutation.isPending}
              />
            )}
          </div>
        </Card>

        {/* Danh sách nhà cung cấp đã tích hợp */}
        <Card className="p-6">
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h5">
              Danh sách nhà cung cấp đã tích hợp
            </Typography>
            {isEditMode && (
              <Button
                onClick={() => {
                  setCurrentProvider(null);
                  setIsEditMode(false);
                }}
                leftIcon={<Plus size={16} />}
              >
                Thêm nhà cung cấp
              </Button>
            )}
          </div>

          <ProviderTable
            providers={providersData?.items || []}
            isLoading={isLoading}
            onEdit={(provider) => {
              console.log('Provider from table:', provider);
              // Không cần kiểm tra apiKey, chỉ cần đảm bảo provider là object hợp lệ
              if (provider && typeof provider === 'object' && 'id' in provider) {
                handleEditProvider(provider as ProviderModel);
              } else {
                console.error('Invalid provider object:', provider);
              }
            }}
            onDelete={handleDeleteProvider}
            onPageChange={handlePageChange}
            onSortChange={handleSortChange}
            pagination={{
              current: queryParams.page || 1,
              pageSize: queryParams.limit || 10,
              total: providersData?.meta?.totalItems || 0
            }}
          />
        </Card>
      </div>



      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={confirmDeleteProvider}
        title="Xóa nhà cung cấp"
        message="Bạn có chắc chắn muốn xóa nhà cung cấp này? Hành động này không thể hoàn tác."
        isSubmitting={deleteProviderMutation.isPending}
      />
    </Container>
  );
};

export default ProviderIntegrationPage;

