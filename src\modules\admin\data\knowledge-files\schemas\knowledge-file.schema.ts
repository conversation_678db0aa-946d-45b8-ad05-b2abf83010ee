import { z } from 'zod';
import { KnowledgeFileStatus } from '../types';

/**
 * Schema cho file tri thức
 */
export const KnowledgeFileSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Tên file không được để trống'),
  extension: z.string().optional(),
  storage: z.number().min(0, 'Dung lượng file phải lớn hơn hoặc bằng 0'),
  vectorStoreId: z.string().optional(),
  vectorStoreName: z.string().optional(),
  downloadURL: z.string().url('URL tải xuống không hợp lệ').optional(),
  createdAt: z.number(),
  updatedAt: z.number().optional(),
  status: z.nativeEnum(KnowledgeFileStatus).optional(),
});

/**
 * Schema cho tham số truy vấn danh sách file tri thức
 */
export const KnowledgeFileQueryParamsSchema = z.object({
  vectorStoreId: z.string().optional(),
  extensions: z.string().optional(),
  search: z.string().optional(),
  page: z.number().int().positive().optional(),
  limit: z.number().int().positive().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['asc', 'desc']).optional(),
});

/**
 * Schema cho dữ liệu tạo file tri thức mới
 */
export const CreateKnowledgeFileSchema = z.object({
  name: z.string().min(1, 'Tên file không được để trống'),
  mime: z.string().min(1, 'Loại MIME không được để trống'),
  storage: z.number().min(0, 'Dung lượng file phải lớn hơn hoặc bằng 0'),
});

/**
 * Schema cho phản hồi danh sách file tri thức
 */
export const KnowledgeFileListResponseSchema = z.object({
  items: z.array(KnowledgeFileSchema),
  meta: z.object({
    totalItems: z.number().int().nonnegative(),
    currentPage: z.number().int().positive(),
    itemsPerPage: z.number().int().positive(),
    totalPages: z.number().int().nonnegative(),
  }),
});

/**
 * Schema cho phản hồi API
 */
export const ApiResponseSchema = z.object({
  code: z.number().int(),
  message: z.string(),
  data: z.any().optional(),
});
