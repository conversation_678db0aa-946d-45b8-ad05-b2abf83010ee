
import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
  Checkbox,
} from '@/shared/components/common';
// Tạm thời import trực tiếp từ file
import SearchInputWithLazyLoading from '@/shared/components/common/SearchInputWithLazyLoading/SearchInputWithLazyLoading';
import FileIcon from '@/shared/components/common/FileIcon/FileIcon';
import { SearchItem } from '@/shared/components/common/SearchInputWithLazyLoading/types';
import { useGetUnassignedFiles } from '@/modules/data/url/hooks/useUrlQuery';
import { formatDate } from '@/shared/utils/format';
import { NotificationUtil } from '@/shared/utils/notification';

// Hàm định dạng kích thước file
const formatBytes = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

// Định nghĩa kiểu dữ liệu cho file từ API
interface FileData {
  id: string;
  name: string;
  createdAt: number;
  description?: string;
  thumbnailUrl?: string;
  size: number;
}

interface AssignFilesToVectorStoreFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  isLoading?: boolean;
  vectorStoreId: string;
}

/**
 * Form gán file vào Vector Store
 */
const AssignFilesToVectorStoreForm: React.FC<AssignFilesToVectorStoreFormProps> = ({
  onSubmit,
  onCancel,
  isLoading = false,
  vectorStoreId,
}) => {
  const { t } = useTranslation();
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [files, setFiles] = useState<FileData[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Hook để lấy danh sách file chưa được gán vào vector store
  const { getUnassignedFiles } = useGetUnassignedFiles();

  // Hàm load options cho SearchInputWithLazyLoading
  const loadOptions = useCallback(
    async (inputValue: string, pagination?: { page: number; limit: number }) => {
      try {
        const response = await getUnassignedFiles({
          vectorStoreId,
          search: inputValue,
          page: pagination?.page || 1,
          limit: pagination?.limit || 10,
        });

        // Chuyển đổi dữ liệu từ API sang FileData
        const extendedFiles: FileData[] = response.items.map(file => {
          // Sử dụng type assertion để truy cập các thuộc tính không có trong kiểu
          const fileWithExtras = file as unknown as Record<string, unknown>;

          return {
            id: file.id,
            name: file.name,
            createdAt: file.createdAt,
            description: (fileWithExtras.description as string) || '',
            thumbnailUrl: (fileWithExtras.thumbnailUrl as string) || '',
            size: (fileWithExtras.size as number) || 0 // Giả định kích thước mặc định
          };
        });

        // Cập nhật danh sách files
        if (pagination?.page === 1) {
          setFiles(extendedFiles);
        } else {
          setFiles((prev) => [...prev, ...extendedFiles]);
        }

        // Chuyển đổi dữ liệu sang định dạng SearchItem
        const searchItems: SearchItem[] = extendedFiles.map((file) => ({
          id: file.id,
          name: file.name,
          description: file.description || '',
          image: file.thumbnailUrl || '',
          disabled: false,
          data: file,
        }));

        return {
          items: searchItems,
          meta: {
            ...response.meta,
            itemsPerPage: response.meta.itemCount || 10
          },
        };
      } catch (error) {
        console.error('Error loading unassigned files:', error);
        NotificationUtil.error({
          message: t('data:vectorStore.errorLoadingFiles', 'Lỗi khi tải danh sách file'),
        });
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: 10,
            totalPages: 0,
            currentPage: 1
          }
        };
      }
    },
    [getUnassignedFiles, vectorStoreId, t]
  );

  // Xử lý khi chọn/bỏ chọn tất cả
  const handleSelectAll = useCallback(() => {
    if (selectAll) {
      setSelectedFileIds([]);
    } else {
      setSelectedFileIds(files.map((file) => file.id));
    }
    setSelectAll(!selectAll);
  }, [selectAll, files]);

  // Cập nhật selectAll khi selectedFileIds thay đổi
  useEffect(() => {
    if (files.length > 0 && selectedFileIds.length === files.length) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  }, [selectedFileIds, files]);

  // Xử lý khi chọn/bỏ chọn một file
  const handleSelectFile = useCallback((fileId: string) => {
    setSelectedFileIds((prev) => {
      if (prev.includes(fileId)) {
        return prev.filter((id) => id !== fileId);
      } else {
        return [...prev, fileId];
      }
    });
  }, []);

  // Xử lý khi submit form
  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();

      if (selectedFileIds.length === 0) {
        NotificationUtil.warning({
          message: t('data:vectorStore.selectFilesWarning', 'Vui lòng chọn ít nhất một file'),
        });
        return;
      }

      onSubmit({ fileIds: selectedFileIds, vectorStoreId });
    },
    [onSubmit, selectedFileIds, vectorStoreId, t]
  );

  return (
    <div className="p-6">
      <Typography variant="h6" className="mb-4">
        {t('data:vectorStore.assignFiles', 'Gán file vào Vector Store')}
      </Typography>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Phần tìm kiếm */}
        <div className="mb-4">
          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg">
            <SearchInputWithLazyLoading
              placeholder={t('data:vectorStore.searchFiles', 'Tìm kiếm file...')}
              loadOptions={loadOptions}
              onInputChange={setSearchTerm}
              fullWidth
              showSearchIcon
              loadInitialOptions
              pageSize={10}
              className="bg-gray-50 dark:bg-gray-900"
            />
          </div>
        </div>

        {/* Danh sách file */}
        <Card className="overflow-hidden bg-gray-50 dark:bg-gray-900 border-0">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center">
            <Checkbox
              checked={selectAll}
              onChange={handleSelectAll}
              color="danger"
              className="mr-2"
            />
            <Typography variant="subtitle2">
              {t('data:vectorStore.selectAll', 'Chọn tất cả')}
            </Typography>
            <Typography variant="caption" className="ml-auto text-gray-500">
              {selectedFileIds.length > 0
                ? t('data:vectorStore.selectedFiles', '{{count}} file đã chọn', {
                    count: selectedFileIds.length,
                  })
                : ''}
            </Typography>
          </div>

          <div className="max-h-60 overflow-y-auto p-2">
            {files.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                {searchTerm
                  ? t('data:vectorStore.noFilesFound', 'Không tìm thấy file nào phù hợp')
                  : t('data:vectorStore.noFilesAvailable', 'Không có file nào chưa được gán')}
              </div>
            ) : (
              <div className="space-y-2">
                {files.map((file) => (
                  <div
                    key={file.id}
                    className="flex items-center p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
                  >
                    <Checkbox
                      checked={selectedFileIds.includes(file.id)}
                      onChange={() => handleSelectFile(file.id)}
                      color="danger"
                      className="mr-2"
                    />
                    <div className="mr-3">
                      <FileIcon fileName={file.name} size="md" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <Typography variant="subtitle2" className="truncate">
                        {file.name}
                      </Typography>
                      <div className="flex items-center text-xs text-gray-500">
                        <span className="mr-2">{formatBytes(file.size)}</span>
                        <span>{formatDate(file.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </Card>

        {/* Buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button
            variant="primary"
            type="submit"
            disabled={isLoading || selectedFileIds.length === 0}
            isLoading={isLoading}
          >
            {t('data:vectorStore.assignButton', 'Gán file')}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default AssignFilesToVectorStoreForm;
