import React from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import {
  Button,
  Card,
  ConditionalField,
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
} from '@/shared/components/common';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { useChatPanel } from '@/shared/contexts/chat-panel';
import { createCouponSchema } from '../../schemas';
import { CouponDto, CouponStatus, DiscountType } from '../../types';

// Định nghĩa kiểu dữ liệu cho form
export type CouponFormValues = z.infer<typeof createCouponSchema>;

interface CouponFormProps {
  /**
   * Dữ liệu ban đầu cho form (khi chỉnh sửa)
   */
  initialData?: CouponDto | null;

  /**
   * Hàm xử lý khi submit form
   */
  onSubmit: (values: Record<string, unknown>) => void;

  /**
   * Hàm xử lý khi hủy form
   */
  onCancel: () => void;

  /**
   * Trạng thái đang submit form
   */
  isSubmitting?: boolean;
}

/**
 * Form tạo mới/chỉnh sửa mã khuyến mãi
 */
const CouponForm: React.FC<CouponFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['rpointAdmin', 'common']);
  const { isChatPanelOpen } = useChatPanel();
  const isEditMode = !!initialData;

  // Hàm chuyển đổi timestamp thành chuỗi datetime-local
  const formatDateTimeLocal = (timestamp: number): string => {
    try {
      // Kiểm tra xem timestamp có hợp lệ không
      if (isNaN(timestamp)) {
        console.warn(`Invalid timestamp: ${timestamp}, using current time instead`);
        return new Date().toISOString().slice(0, 16); // Format: YYYY-MM-DDTHH:MM
      }

      const date = new Date(timestamp);

      // Kiểm tra xem date có hợp lệ không
      if (isNaN(date.getTime())) {
        console.warn(`Invalid date from timestamp: ${timestamp}, using current time instead`);
        return new Date().toISOString().slice(0, 16);
      }

      return date.toISOString().slice(0, 16); // Format: YYYY-MM-DDTHH:MM
    } catch (error) {
      console.error(`Error formatting timestamp: ${timestamp}`, error);
      return new Date().toISOString().slice(0, 16); // Fallback to current time
    }
  };

  // Chuẩn bị giá trị mặc định cho form
  const defaultValues = initialData
    ? {
        ...initialData,
        // Chuyển đổi timestamp thành chuỗi datetime-local
        startDate: formatDateTimeLocal(initialData.startDate),
        endDate: formatDateTimeLocal(initialData.endDate),
      }
    : {
        code: '',
        description: '',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 0,
        minOrderValue: 0,
        maxDiscountAmount: null,
        startDate: formatDateTimeLocal(Date.now()),
        endDate: formatDateTimeLocal(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        usageLimit: null,
        perUserLimit: 1,
        status: CouponStatus.ACTIVE,
      };

  // Xử lý submit form
  const handleFormSubmit = (values: Record<string, unknown>) => {
    onSubmit(values);
  };

  return (
    <Card
      title={isEditMode ? t('rpointAdmin:coupons.form.update') : t('rpointAdmin:coupons.form.create')}
      className={`w-full mx-auto ${isChatPanelOpen ? 'max-w-2xl' : 'max-w-4xl'}`}
    >
      <Form
        schema={createCouponSchema}
        onSubmit={handleFormSubmit}
        className="space-y-6"
        defaultValues={defaultValues}
      >
        <FormItem
          name="code"
          label={t('rpointAdmin:coupons.form.code')}
          required
        >
          <Input
            placeholder={t('rpointAdmin:coupons.form.code')}
            fullWidth
          />
        </FormItem>

        <FormItem
          name="description"
          label={t('rpointAdmin:coupons.form.description')}
        >
          <Textarea
            placeholder={t('rpointAdmin:coupons.form.description')}
            rows={3}
            fullWidth
          />
        </FormItem>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem
            name="discountType"
            label={t('rpointAdmin:coupons.form.discountType')}
            required
          >
            <Select
              options={[
                { value: DiscountType.PERCENTAGE, label: t('rpointAdmin:coupons.discountType.PERCENTAGE') },
                { value: DiscountType.FIXED_AMOUNT, label: t('rpointAdmin:coupons.discountType.FIXED_AMOUNT') },
              ]}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="discountValue"
            label={t('rpointAdmin:coupons.form.discountValue')}
            required
          >
            <Input
              type="number"
              placeholder={t('rpointAdmin:coupons.form.discountValue')}
              fullWidth
            />
          </FormItem>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem
            name="minOrderValue"
            label={t('rpointAdmin:coupons.form.minOrderValue')}
            required
          >
            <Input
              type="number"
              placeholder={t('rpointAdmin:coupons.form.minOrderValue')}
              fullWidth
            />
          </FormItem>

          {/* Hiển thị maxDiscountAmount chỉ khi discountType là PERCENTAGE */}
          <ConditionalField
            condition={{
              field: 'discountType',
              type: ConditionType.EQUALS,
              value: DiscountType.PERCENTAGE,
            }}
          >
            <FormItem
              name="maxDiscountAmount"
              label={t('rpointAdmin:coupons.form.maxDiscountAmount')}
            >
              <Input
                type="number"
                placeholder={t('rpointAdmin:coupons.form.maxDiscountAmount')}
                fullWidth
              />
            </FormItem>
          </ConditionalField>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem
            name="startDate"
            label={t('rpointAdmin:coupons.form.startDate')}
            required
          >
            <Input
              type="datetime-local"
              fullWidth
            />
          </FormItem>

          <FormItem
            name="endDate"
            label={t('rpointAdmin:coupons.form.endDate')}
            required
          >
            <Input
              type="datetime-local"
              fullWidth
            />
          </FormItem>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem
            name="usageLimit"
            label={t('rpointAdmin:coupons.form.usageLimit')}
          >
            <Input
              type="number"
              placeholder={t('rpointAdmin:coupons.form.usageLimit')}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="perUserLimit"
            label={t('rpointAdmin:coupons.form.perUserLimit')}
            required
          >
            <Input
              type="number"
              placeholder={t('rpointAdmin:coupons.form.perUserLimit')}
              fullWidth
            />
          </FormItem>
        </div>

        <FormItem
          name="status"
          label={t('rpointAdmin:coupons.form.status')}
          required
        >
          <Select
            options={[
              { value: CouponStatus.ACTIVE, label: t('rpointAdmin:coupons.status.ACTIVE') },
              { value: CouponStatus.INACTIVE, label: t('rpointAdmin:coupons.status.INACTIVE') },
              { value: CouponStatus.EXPIRED, label: t('rpointAdmin:coupons.status.EXPIRED') },
            ]}
            fullWidth
          />
        </FormItem>

        <div className="flex justify-end space-x-4 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('rpointAdmin:coupons.form.cancel')}
          </Button>
          <Button type="submit" variant="primary" isLoading={isSubmitting}>
            {t('rpointAdmin:coupons.form.submit')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default CouponForm;
