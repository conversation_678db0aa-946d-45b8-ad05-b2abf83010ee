import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Icon } from '@/shared/components/common';

/**
 * Trang tích hợp & quản lý Facebook Ads – skeleton
 */
const FacebookAdsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  return (
    <Card className="p-6">
      <div className="flex items-center mb-4">
        <Icon name="facebook" size="lg" className="text-primary mr-2" />
        <Typography variant="h4">
          {t('marketing:facebookAds.title', 'Facebook Ads')}
        </Typography>
      </div>
      <Typography variant="body1" className="mb-2">
        {t(
          'marketing:facebookAds.description',
          'Tích hợp và quản lý chiến dịch Facebook Ads từ hệ thống'
        )}
      </Typography>
      <Typography variant="body2">
        {t(
          'marketing:facebookAds.comingSoon',
          '<PERSON>ức năng đang được phát triển. Vui lòng quay lại sau!'
        )}
      </Typography>
    </Card>
  );
};

export default FacebookAdsPage; 