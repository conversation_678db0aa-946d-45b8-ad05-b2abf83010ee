/**
 * Enum định nghĩa các trạng thái của tool
 */
export enum ToolStatus {
  DRAFT = 'DRAFT',
  APPROVED = 'APPROVED',
  DEPRECATED = 'DEPRECATED',
}

/**
 * Enum định nghĩa các loại truy cập của tool
 */
export enum AccessType {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE',
  RESTRICTED = 'RESTRICTED',
}

/**
 * Enum định nghĩa các loại sắp xếp của tool
 */
export enum ToolSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum định nghĩa các loại xác thực
 */
export enum AuthType {
  NONE = 'none',
  API_KEY = 'apiKey',
  OAUTH = 'oauth',
}

/**
 * Enum định nghĩa vị trí của API key
 */
export enum ApiKeyLocation {
  HEADER = 'header',
  QUERY = 'query',
}

/**
 * Enum định nghĩa nguồn của token
 */
export enum TokenSource {
  JWT = 'jwt',
  OAUTH = 'oauth',
}

/**
 * Interface định nghĩa thông tin người tạo/cập nhật
 */
export interface UserInfo {
  id: number;
  name: string;
  email?: string;
}

/**
 * Interface định nghĩa tham số truy vấn cơ bản
 */
export interface BaseQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface định nghĩa kết quả phân trang
 */
export interface PaginatedResult<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
