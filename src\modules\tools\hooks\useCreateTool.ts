import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ToolService } from '../services/user-tool.service';
import { CreateToolParams } from '../types/tool.types';

/**
 * Hook để tạo tool mới
 * @returns Mutation để tạo tool mới
 */
export const useCreateTool = () => {
  const queryClient = useQueryClient();
  const toolService = new ToolService();

  return useMutation({
    mutationFn: (params: CreateToolParams) => toolService.createTool(params),
    onSuccess: () => {
      // Invalidate queries để cập nhật danh sách tools
      queryClient.invalidateQueries({ queryKey: ['userTools'] });
    },
  });
};

export default useCreateTool;
