import { z } from 'zod';
import { ProviderType } from '../types/provider.types';

// Sử dụng ApiResponseSchema từ provider.schema.ts

/**
 * Schema cho thông tin provider model
 */
export const ProviderModelSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Tên provider model không được để trống'),
  type: z.nativeEnum(ProviderType),
  apiKey: z.string().min(1, 'API key không được để trống').optional(),
  createdAt: z.number().optional(),
});

/**
 * Schema cho tạo provider model mới
 */
export const CreateProviderModelSchema = z.object({
  name: z.string().min(1, 'Tên provider model không được để trống'),
  type: z.nativeEnum(ProviderType),
  apiKey: z.string().min(1, 'API key không được để trống'),
});

/**
 * Schema cho cập nhật provider model
 */
export const UpdateProviderModelSchema = z.object({
  name: z.string().min(1, 'Tên provider model không được để trống').optional(),
  apiKey: z.string().min(1, 'API key không được để trống').optional(),
});

/**
 * Schema cho danh sách provider model
 */
export const ProviderModelListSchema = z.array(ProviderModelSchema);

/**
 * Schema cho kết quả phân trang
 */
export const PaginatedProviderModelSchema = z.object({
  items: z.array(ProviderModelSchema),
  meta: z.object({
    totalItems: z.number(),
    itemCount: z.number(),
    itemsPerPage: z.number(),
    totalPages: z.number(),
    currentPage: z.number(),
  }),
});
