import React from 'react';
import { useTranslation } from 'react-i18next';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import { usePointConversionData } from '../hooks/usePointConversionData';

/**
 * Trang tổng quan quản lý Affiliate
 */
const AffiliateManagementPage: React.FC = () => {
  const { t } = useTranslation(['affiliate', 'common']);

  // Sử dụng hook để lấy tổng số lịch sử chuyển đổi điểm
  const { useTotalPointConversions } = usePointConversionData();
  const { data: totalConversions, isLoading: isLoadingConversions } = useTotalPointConversions();

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Publisher Card */}
        <ModuleCard
          title={t('affiliate:publisher.title')}
          description={t('affiliate:publisher.description')}
          icon="users"
          count="1,250"
          countLabel={t('affiliate:publisher.totalPublishers')}
          linkTo="/admin/affiliate/publishers"
          linkText={t('affiliate:publisher.manage')}
        />

        {/* Rank Card */}
        <ModuleCard
          title={t('affiliate:rank.title')}
          description={t('affiliate:rank.description')}
          icon="award"
          count="5"
          countLabel={t('affiliate:rank.totalRanks')}
          linkTo="/admin/affiliate/ranks"
          linkText={t('affiliate:rank.manage')}
        />
        {/* Orders Card */}
        <ModuleCard
          title={t('affiliate:order.title')}
          description={t('affiliate:order.description')}
          icon="shopping-cart"
          count="328"
          countLabel={t('affiliate:order.totalOrders')}
          linkTo="/admin/affiliate/orders"
          linkText={t('affiliate:order.manage')}
        />

        {/* Point Conversion Card */}
        <ModuleCard
          title={t('affiliate:pointConversion.title')}
          description={t('affiliate:pointConversion.description')}
          icon="refresh-cw"
          count={isLoadingConversions ? '...' : totalConversions?.toString() || '0'}
          countLabel={t('affiliate:pointConversion.totalConversions')}
          linkTo="/admin/affiliate/point-conversions"
          linkText={t('affiliate:pointConversion.manage')}
        />
      </ResponsiveGrid>
    </div>
  );
};

export default AffiliateManagementPage;
