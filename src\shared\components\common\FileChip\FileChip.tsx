import React from 'react';
import Icon from '@/shared/components/common/Icon/Icon';
import type { IconName } from '@/shared/components/common/Icon/Icon';
import { useTheme } from '@/shared/contexts/theme';

export interface FileChipProps {
  /**
   * Tên file
   */
  fileName: string;

  /**
   * Kích thước file (bytes)
   */
  fileSize?: number;

  /**
   * Sự kiện khi click vào chip
   */
  onClick?: () => void;

  /**
   * Sự kiện khi click vào nút xóa
   */
  onDelete?: () => void;

  /**
   * Có hiển thị nút xóa không
   */
  showDeleteButton?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị file dạng chip
 */
const FileChip: React.FC<FileChipProps> = ({
  fileName,
  fileSize,
  onClick,
  onDelete,
  showDeleteButton = false,
  className = '',
}) => {
  const { currentTheme } = useTheme();

  // Lấy phần mở rộng từ tên file
  const getFileExtension = (): string => {
    return fileName.split('.').pop()?.toLowerCase() || '';
  };

  // Lấy icon dựa trên phần mở rộng của file
  const getFileIcon = (): IconName => {
    const extension = getFileExtension();

    if (['pdf'].includes(extension)) return 'file-pdf';
    if (['doc', 'docx'].includes(extension)) return 'file-text';
    if (['xls', 'xlsx', 'csv'].includes(extension)) return 'file-spreadsheet';
    if (['ppt', 'pptx'].includes(extension)) return 'presentation';
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension)) return 'image';
    if (['mp4', 'avi', 'mov', 'wmv'].includes(extension)) return 'video';
    if (['mp3', 'wav', 'ogg'].includes(extension)) return 'music';
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) return 'archive';
    if (['html', 'htm'].includes(extension)) return 'code';
    if (['txt', 'md'].includes(extension)) return 'file-text';
    if (['json', 'xml'].includes(extension)) return 'code';

    return 'file';
  };

  // Lấy màu cho icon dựa trên phần mở rộng
  const getFileIconColor = (): string => {
    const extension = getFileExtension();

    if (['pdf'].includes(extension)) return currentTheme.semanticColors.destructive;
    if (['doc', 'docx'].includes(extension)) return currentTheme.semanticColors.primary;
    if (['xls', 'xlsx', 'csv'].includes(extension)) return currentTheme.semanticColors.success;
    if (['ppt', 'pptx'].includes(extension)) return currentTheme.semanticColors.warning;
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension)) return '#9333ea'; // purple
    if (['mp4', 'avi', 'mov', 'wmv'].includes(extension)) return '#ec4899'; // pink
    if (['mp3', 'wav', 'ogg'].includes(extension)) return '#eab308'; // yellow
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) return currentTheme.semanticColors.muted;

    return currentTheme.semanticColors.muted;
  };

  // Định dạng kích thước file
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '';
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  return (
    <div
      className={`
        flex items-center gap-2 px-3 py-2 rounded-md bg-gray-100 dark:bg-gray-800
        ${onClick ? 'cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700' : ''}
        ${className}
      `}
      onClick={onClick}
    >
      <Icon
        name={getFileIcon()}
        size="md"
        className={`flex-shrink-0`}
        color={getFileIconColor()}
      />

      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium truncate">
          {fileName}
        </div>
        {fileSize !== undefined && (
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {formatFileSize(fileSize)}
          </div>
        )}
      </div>

      {showDeleteButton && onDelete && (
        <button
          type="button"
          className="flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }}
        >
          <Icon name="x" size="sm" />
        </button>
      )}
    </div>
  );
};

export default FileChip;
