import React from 'react';
import { Input, Typography, Button } from '@/shared/components/common';
import { EmailElement } from '../types';
import {
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Bold,
  Italic,
  Underline
} from 'lucide-react';

interface StyleEditorProps {
  selectedElement: EmailElement;
  updateSelectedElement: (property: string, value: unknown) => void;
}

const StyleEditor: React.FC<StyleEditorProps> = ({
  selectedElement,
  updateSelectedElement
}) => {
  // Kích thước icon
  const iconSize = 16;

  // Xử lý khi thay đổi căn chỉnh văn bản
  const handleTextAlignChange = (align: string) => {
    updateSelectedElement('style.textAlign', align);
  };

  // Xử lý khi thay đổi font style
  const handleFontStyleChange = (style: string) => {
    const currentStyle = selectedElement.style?.fontStyle || 'normal';
    const newStyle = currentStyle === style ? 'normal' : style;
    updateSelectedElement('style.fontStyle', newStyle);
  };

  // Xử lý khi thay đổi font weight
  const handleFontWeightChange = () => {
    const currentWeight = selectedElement.style?.fontWeight || 'normal';
    const newWeight = currentWeight === 'bold' ? 'normal' : 'bold';
    updateSelectedElement('style.fontWeight', newWeight);
  };

  // Xử lý khi thay đổi text decoration
  const handleTextDecorationChange = () => {
    const currentDecoration = selectedElement.style?.textDecoration || 'none';
    const newDecoration = currentDecoration === 'underline' ? 'none' : 'underline';
    updateSelectedElement('style.textDecoration', newDecoration);
  };

  return (
    <div className="space-y-4">
      {/* Typography Section */}
      <div className="p-4 rounded-md">
        <Typography variant="body1" className="font-medium mb-2">Typography</Typography>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <Typography variant="body2" className="text-xs mb-1">Font Family</Typography>
            <select
              value={selectedElement.style?.fontFamily || 'Arial, sans-serif'}
              onChange={(e) => updateSelectedElement('style.fontFamily', e.target.value)}
              className="w-full p-2 border rounded"
            >
              <option value="Arial, sans-serif">Arial</option>
              <option value="'Times New Roman', serif">Times New Roman</option>
              <option value="'Courier New', monospace">Courier New</option>
              <option value="Georgia, serif">Georgia</option>
              <option value="Verdana, sans-serif">Verdana</option>
              <option value="'Open Sans', sans-serif">Open Sans</option>
              <option value="'Roboto', sans-serif">Roboto</option>
            </select>
          </div>

          <div>
            <Typography variant="body2" className="text-xs mb-1">Font Size</Typography>
            <div className="flex items-center">
              <Input
                type="number"
                value={selectedElement.style?.fontSize || 16}
                onChange={(e) => updateSelectedElement('style.fontSize', parseInt(e.target.value) || 16)}
                className="w-full"
              />
              <span className="ml-2">px</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <Typography variant="body2" className="text-xs mb-1">Line Height</Typography>
            <div className="flex items-center">
              <Input
                type="number"
                step="0.1"
                value={selectedElement.style?.lineHeight || 1.5}
                onChange={(e) => updateSelectedElement('style.lineHeight', parseFloat(e.target.value) || 1.5)}
                className="w-full"
              />
            </div>
          </div>

          <div>
            <Typography variant="body2" className="text-xs mb-1">Letter Spacing</Typography>
            <div className="flex items-center">
              <Input
                type="number"
                step="0.1"
                value={selectedElement.style?.letterSpacing || 0}
                onChange={(e) => updateSelectedElement('style.letterSpacing', parseFloat(e.target.value) || 0)}
                className="w-full"
              />
              <span className="ml-2">px</span>
            </div>
          </div>
        </div>

        <div className="mb-4">
          <Typography variant="body2" className="text-xs mb-1">Text Align</Typography>
          <div className="flex space-x-1">
            <Button
              variant={selectedElement.style?.textAlign === 'left' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => handleTextAlignChange('left')}
              className="flex-1"
            >
              <AlignLeft size={iconSize} />
            </Button>
            <Button
              variant={selectedElement.style?.textAlign === 'center' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => handleTextAlignChange('center')}
              className="flex-1"
            >
              <AlignCenter size={iconSize} />
            </Button>
            <Button
              variant={selectedElement.style?.textAlign === 'right' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => handleTextAlignChange('right')}
              className="flex-1"
            >
              <AlignRight size={iconSize} />
            </Button>
            <Button
              variant={selectedElement.style?.textAlign === 'justify' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => handleTextAlignChange('justify')}
              className="flex-1"
            >
              <AlignJustify size={iconSize} />
            </Button>
          </div>
        </div>

        <div>
          <Typography variant="body2" className="text-xs mb-1">Style</Typography>
          <div className="flex space-x-1">
            <Button
              variant={selectedElement.style?.fontWeight === 'bold' ? 'primary' : 'outline'}
              size="sm"
              onClick={handleFontWeightChange}
              className="flex-1"
            >
              <Bold size={iconSize} />
            </Button>
            <Button
              variant={selectedElement.style?.fontStyle === 'italic' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => handleFontStyleChange('italic')}
              className="flex-1"
            >
              <Italic size={iconSize} />
            </Button>
            <Button
              variant={selectedElement.style?.textDecoration === 'underline' ? 'primary' : 'outline'}
              size="sm"
              onClick={handleTextDecorationChange}
              className="flex-1"
            >
              <Underline size={iconSize} />
            </Button>
          </div>
        </div>
      </div>

      {/* Colors Section */}
      <div className="p-4 rounded-md">
        <Typography variant="body1" className="font-medium mb-2">Colors</Typography>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Typography variant="body2" className="text-xs mb-1">Text Color</Typography>
            <div className="flex items-center gap-2">
              <input
                type="color"
                value={selectedElement.style?.color || '#333333'}
                onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                className="w-8 h-8 rounded"
              />
              <Input
                value={selectedElement.style?.color || '#333333'}
                onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                className="flex-1"
              />
            </div>
          </div>

          <div>
            <Typography variant="body2" className="text-xs mb-1">Background Color</Typography>
            <div className="flex items-center gap-2">
              <input
                type="color"
                value={selectedElement.style?.backgroundColor || '#ffffff'}
                onChange={(e) => updateSelectedElement('style.backgroundColor', e.target.value)}
                className="w-8 h-8 rounded"
              />
              <Input
                value={selectedElement.style?.backgroundColor || '#ffffff'}
                onChange={(e) => updateSelectedElement('style.backgroundColor', e.target.value)}
                className="flex-1"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StyleEditor;
