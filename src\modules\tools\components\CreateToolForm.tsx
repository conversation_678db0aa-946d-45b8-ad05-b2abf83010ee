import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Textarea, Select, FormItem } from '@/shared/components/common';
import { ToolStatus, AccessType } from '../types/common.types';
import { CreateToolParams } from '../types/tool.types';

interface CreateToolFormProps {
  onSubmit: (values: CreateToolParams) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

/**
 * Form tạo tool mới
 */
const CreateToolForm: React.FC<CreateToolFormProps> = ({
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [status, setStatus] = useState<ToolStatus>(ToolStatus.DRAFT);
  const [accessType, setAccessType] = useState<AccessType>(AccessType.PUBLIC);
  const [toolName, setToolName] = useState('');
  const [toolDescription, setToolDescription] = useState('');
  const [parameters, setParameters] = useState<Record<string, unknown>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = t('tools.validation.nameRequired', 'Tên tool là bắt buộc');
    }

    if (!toolName.trim()) {
      newErrors.toolName = t(
        'tools.validation.toolNameRequired',
        'Tên hiển thị của tool là bắt buộc'
      );
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Tạo tool mới
    const createData: CreateToolParams = {
      name,
      description: description || undefined,
      toolName,
      toolDescription: toolDescription || undefined,
      parameters,
      status,
      accessType,
    };
    onSubmit(createData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-4">
      <div className="flex justify-between items-center">
        <Typography variant="h6">{t('tools.createTool', 'Tạo công cụ mới')}</Typography>
      </div>

      <div className="space-y-4">
        {/* Thông tin cơ bản */}
        <div className="space-y-4">
          <FormItem label={t('tools.name', 'Tên công cụ')} helpText={errors.name} required>
            <Input
              value={name}
              onChange={e => {
                setName(e.target.value);
                if (errors.name) setErrors(prev => ({ ...prev, name: '' }));
              }}
              placeholder={t('tools.namePlaceholder', 'Nhập tên công cụ')}
              disabled={isLoading}
            />
          </FormItem>

          <FormItem label={t('tools.description', 'Mô tả')}>
            <Textarea
              value={description}
              onChange={e => setDescription(e.target.value)}
              placeholder={t('tools.descriptionPlaceholder', 'Nhập mô tả công cụ')}
              disabled={isLoading}
              rows={3}
            />
          </FormItem>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('tools.status', 'Trạng thái')}>
              <Select
                value={status}
                onChange={val => setStatus(val as ToolStatus)}
                options={[
                  { value: ToolStatus.DRAFT, label: t('tools.status.draft', 'Bản nháp') },
                  { value: ToolStatus.APPROVED, label: t('tools.status.approved', 'Đã duyệt') },
                  {
                    value: ToolStatus.DEPRECATED,
                    label: t('tools.status.deprecated', 'Không dùng'),
                  },
                ]}
                disabled={isLoading}
              />
            </FormItem>

            <FormItem label={t('tools.accessType', 'Loại truy cập')}>
              <Select
                value={accessType}
                onChange={val => setAccessType(val as AccessType)}
                options={[
                  { value: AccessType.PUBLIC, label: t('tools.access.public', 'Công khai') },
                  { value: AccessType.PRIVATE, label: t('tools.access.private', 'Riêng tư') },
                  { value: AccessType.RESTRICTED, label: t('tools.access.restricted', 'Hạn chế') },
                ]}
                disabled={isLoading}
              />
            </FormItem>
          </div>
        </div>

        {/* Thông tin phiên bản */}
        <div className="space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <Typography variant="subtitle1" className="font-medium">
            {t('tools.initialVersion', 'Phiên bản ban đầu')}
          </Typography>

          <FormItem
            label={t('tools.toolName', 'Tên hiển thị công cụ')}
            helpText={errors.toolName}
            required
          >
            <Input
              value={toolName}
              onChange={e => {
                setToolName(e.target.value);
                if (errors.toolName) setErrors(prev => ({ ...prev, toolName: '' }));
              }}
              placeholder={t('tools.toolNamePlaceholder', 'Nhập tên hiển thị công cụ')}
              disabled={isLoading}
            />
          </FormItem>

          <FormItem label={t('tools.toolDescription', 'Mô tả hiển thị công cụ')}>
            <Textarea
              value={toolDescription}
              onChange={e => setToolDescription(e.target.value)}
              placeholder={t('tools.toolDescriptionPlaceholder', 'Nhập mô tả hiển thị công cụ')}
              disabled={isLoading}
              rows={3}
            />
          </FormItem>

          <FormItem label={t('tools.parameters', 'Tham số')}>
            <Textarea
              value={JSON.stringify(parameters, null, 2)}
              onChange={e => {
                try {
                  const parsed = JSON.parse(e.target.value);
                  setParameters(parsed);
                } catch (error) {
                  // Ignore parse errors while typing
                  console.log(error);
                }
              }}
              placeholder={t('tools.parametersPlaceholder', 'Nhập tham số công cụ dạng JSON')}
              disabled={isLoading}
              rows={5}
              className="font-mono text-sm"
            />
          </FormItem>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <Button variant="outline" onClick={onCancel} disabled={isLoading}>
          {t('common.cancel', 'Hủy')}
        </Button>
        <Button type="submit" variant="primary" disabled={isLoading}>
          {isLoading ? t('tools.creating', 'Đang tạo...') : t('tools.create', 'Tạo')}
        </Button>
      </div>
    </form>
  );
};

export default CreateToolForm;
