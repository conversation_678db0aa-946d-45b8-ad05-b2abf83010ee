/**
 * Interface cho message trong dataset
 */
export interface DatasetMessage {
  /**
   * Vai trò của message (system, user, assistant)
   */
  role: 'system' | 'user' | 'assistant';

  /**
   * Nội dung của message
   */
  content: string;
}

/**
 * Interface cho một conversation trong dataset
 */
export interface DatasetConversation {
  /**
   * Danh sách các message trong conversation
   */
  messages: DatasetMessage[];
}

/**
 * DTO để tạo dataset
 */
export interface CreateDatasetDto {
  /**
   * Tên của dataset
   */
  name: string;

  /**
   * Mô tả của dataset
   */
  description: string;

  /**
   * Dữ liệu huấn luyện
   */
  trainData: DatasetConversation[];

  /**
   * Dữ liệu kiểm tra
   */
  validationData: DatasetConversation[];
}

/**
 * Response khi tạo dataset thành công
 */
export interface DatasetResponseDto {
  /**
   * ID của dataset
   */
  id: string;

  /**
   * Tên của dataset
   */
  name: string;

  /**
   * <PERSON>ô tả của dataset
   */
  description: string;

  /**
   * Trạng thái của dataset
   */
  status: 'DRAFT' | 'PROCESSING' | 'COMPLETED' | 'FAILED';

  /**
   * Tác giả của dataset
   */
  author: string;

  /**
   * Avatar của tác giả
   */
  avatar: string;
}

/**
 * DTO để query danh sách dataset
 */
export interface QueryDatasetDto {
  /**
   * Trang hiện tại
   */
  page?: number;

  /**
   * Số lượng item trên mỗi trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm
   */
  search?: string;

  /**
   * Sắp xếp theo trường
   */
  sortBy?: string;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC';

  /**
   * Lọc theo trạng thái
   */
  status?: 'DRAFT' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
}
