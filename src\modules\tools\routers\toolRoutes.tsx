import { RouteObject } from 'react-router-dom';
import { Suspense } from 'react';

import { Loading } from '@/shared';
import { ToolsPage, ToolManagementPage, ToolVersionsPage, ToolGroupsPage } from '../pages';
import MainLayout from '@/shared/layouts/MainLayout';

/**
 * Routes cho module tools của user
 */
const toolRoutes: RouteObject[] = [
  // Trang tổng quan quản lý tools
  {
    path: '/tools',
    element: (
      <MainLayout title="Tool Management">
        <Suspense fallback={<Loading />}>
          <ToolManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang danh sách tools
  {
    path: '/tools/list',
    element: (
      <MainLayout title="Tools List">
        <Suspense fallback={<Loading />}>
          <ToolsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang danh sách phiên bản của tool
  {
    path: '/tools/versions',
    element: (
      <MainLayout title="Tool Versions">
        <Suspense fallback={<Loading />}>
          <ToolVersionsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang danh sách phiên bản của tool cụ thể
  {
    path: '/tools/:id/versions',
    element: (
      <MainLayout title="Tool Versions">
        <Suspense fallback={<Loading />}>
          <ToolVersionsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang quản lý nhóm công cụ
  {
    path: '/tools/groups',
    element: (
      <MainLayout title="Tool Groups">
        <Suspense fallback={<Loading />}>
          <ToolGroupsPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default toolRoutes;
