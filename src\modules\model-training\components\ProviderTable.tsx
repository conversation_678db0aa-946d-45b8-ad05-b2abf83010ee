import React, { useMemo } from 'react';
import { Table, ActionMenu } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import { Provider } from '../types/provider.types';
import { ProviderModel } from '../types/provider-model.types';
import { formatTimestamp } from '@/shared/utils/date';

interface ProviderTableProps {
  providers: (Provider | ProviderModel)[];
  isLoading?: boolean;
  onEdit: (provider: Provider | ProviderModel) => void;
  onDelete: (id: string) => void;
  onPageChange?: (page: number, pageSize: number) => void;
  onSortChange?: (column: string | null, order: 'asc' | 'desc' | null) => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
  };
}

/**
 * Component hiển thị danh sách nhà cung cấp dưới dạng bảng
 */
const ProviderTable: React.FC<ProviderTableProps> = ({
  providers,
  isLoading = false,
  onEdit,
  onDelete,
  onPageChange,
  onSortChange,
  pagination,
}) => {
  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<Provider | ProviderModel>[]>(
    () => [
      {
        key: 'id',
        title: 'ID',
        dataIndex: 'id',
        width: '10%',
        sortable: true,
      },
      {
        key: 'name',
        title: 'Tên nhà cung cấp',
        dataIndex: 'name',
        width: '25%',
        sortable: true,
      },
      {
        key: 'type',
        title: 'Loại nhà cung cấp',
        dataIndex: 'type',
        width: '20%',
        sortable: true,
      },
      {
        key: 'createdAt',
        title: 'Ngày tạo',
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          if (!value) return '-';

          // Sử dụng hàm formatTimestamp để xử lý timestamp
          return formatTimestamp(value);
        },
      },
      {
        key: 'actions',
        title: 'Thao tác',
        width: '10%',
        render: (_: unknown, record: Provider | ProviderModel) => {
          // Tạo danh sách các action items
          const actionItems = [
            {
              id: 'edit',
              label: 'Chỉnh sửa',
              icon: 'edit',
              onClick: () => onEdit(record),
            },
            {
              id: 'delete',
              label: 'Xóa',
              icon: 'trash',
              onClick: () => onDelete(record.id || ''),
            },
          ];

          return (
            <div className="flex justify-center">
              <ActionMenu
                items={actionItems}
                menuTooltip="Thêm hành động"
                iconSize="sm"
                iconVariant="default"
                placement="bottom"
                menuWidth="180px"
                menuIcon="more-horizontal"
                showAllInMenu={true}
                preferRight={true}
                preferTop={true}
              />
            </div>
          );
        },
      },
    ],
    [onEdit, onDelete]
  );

  return (
    <Table
      columns={columns}
      data={providers}
      rowKey="id"
      loading={isLoading}
      sortable={true}
      onSortChange={onSortChange}
      pagination={{
        total: pagination?.total || providers.length,
        current: pagination?.current || 1,
        pageSize: pagination?.pageSize || 10,
        onChange: onPageChange || (() => {}),
        showSizeChanger: true,
        pageSizeOptions: [10, 20, 50, 100],
        showFirstLastButtons: true,
        showPageInfo: true,
      }}
      bordered
      hoverable
    />
  );
};

export default ProviderTable;
