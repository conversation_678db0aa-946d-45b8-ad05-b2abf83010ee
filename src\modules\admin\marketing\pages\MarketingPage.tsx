import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, ResponsiveGrid, Typography } from '@/shared/components/common';
import ModuleCard from '@/modules/components/card/ModuleCard';
import { useCustomFields } from '../hooks';

/**
 * Marketing Dashboard Page
 * Displays an overview of marketing module features
 */
const MarketingPage: React.FC = () => {
  const { t } = useTranslation(['marketingAdmin', 'common']);

  // State to store counts
  const [customFieldCount, setCustomFieldCount] = useState<number>(0);

  // Call API to get custom fields list
  const { data: fieldData } = useCustomFields({
    page: 1,
    limit: 1,
  });

  // Update counts when data is available
  useEffect(() => {
    if (fieldData?.meta) {
      setCustomFieldCount(fieldData.meta.totalItems);
    }
  }, [fieldData]);

  return (
    <div className="p-6">
      <Typography variant="h1" className="mb-6">
        {t('marketingAdmin:marketing.title')}
      </Typography>

      <Card className="mb-6 p-6">
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 3 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
          gap={{ xs: 4, md: 6 }}
        >
          {/* Custom Fields Card */}
          <ModuleCard
            title={t('marketingAdmin:customFields.title')}
            description={t('marketingAdmin:customFields.description')}
            icon="database"
            count={customFieldCount}
            countLabel={t('marketingAdmin:customFields.totalFields')}
            linkTo="/admin/marketing/custom-fields"
            linkText={t('marketingAdmin:customFields.manage')}
          />
        </ResponsiveGrid>
      </Card>
    </div>
  );
};

export default MarketingPage;
