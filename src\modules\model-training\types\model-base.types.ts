/**
 * Enum định nghĩa trạng thái của model base
 */
export enum ModelBaseStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED',
}

/**
 * Interface cho cấu hình của model base
 */
export interface ModelBaseConfig {
  /**
   * Có hỗ trợ TopP không
   */
  hasTopP: boolean;

  /**
   * Có hỗ trợ TopK không
   */
  hasTopK: boolean;

  /**
   * Có hỗ trợ Function không
   */
  hasFunction: boolean;

  /**
   * Có hỗ trợ Temperature không
   */
  hasTemperature: boolean;

  /**
   * Có hỗ trợ Text không
   */
  hasText: boolean;

  /**
   * Có hỗ trợ Image không
   */
  hasImage: boolean;

  /**
   * Có hỗ trợ Audio không
   */
  hasAudio: boolean;

  /**
   * Có hỗ trợ Video không
   */
  hasVideo: boolean;

  /**
   * <PERSON><PERSON> hỗ trợ Parallel Tool Call không
   */
  hasParallelToolCall: boolean;

  /**
   * Có hỗ trợ Reasoning Effort không
   */
  hasReasoningEffort?: string[];
}

/**
 * Interface cho thông tin model base
 */
export interface ModelBase {
  /**
   * ID của model base
   */
  id?: string;

  /**
   * Tên của model base
   */
  name: string;

  /**
   * Mô tả của model base
   */
  description?: string;

  /**
   * ID của nhà cung cấp
   */
  providerId: string;

  /**
   * Giá input cho base
   */
  baseInputRate: number;

  /**
   * Giá output cho base
   */
  baseOutputRate: number;

  /**
   * Giá train cho base
   */
  baseTrainRate: number;

  /**
   * Giá input cho fine-tuning
   */
  fineTuningInputRate: number;

  /**
   * Giá output cho fine-tuning
   */
  fineTuningOutputRate: number;

  /**
   * Giá train cho fine-tuning
   */
  fineTuningTrainRate: number;

  /**
   * Số lượng token
   */
  tokenCount?: number;

  /**
   * Trạng thái của model base
   */
  status?: ModelBaseStatus;

  /**
   * Cấu hình của model base
   */
  config: ModelBaseConfig;

  /**
   * Thời gian tạo (unix timestamp)
   */
  createdAt?: number;

  /**
   * Thời gian cập nhật (unix timestamp)
   */
  updatedAt?: number;
}

/**
 * Interface cho request tạo model base mới
 */
export interface CreateModelBaseDto {
  /**
   * Tên của model base
   */
  name: string;

  /**
   * Mô tả của model base
   */
  description?: string;

  /**
   * ID của nhà cung cấp
   */
  providerId: string;

  /**
   * Giá input cho base
   */
  baseInputRate: number;

  /**
   * Giá output cho base
   */
  baseOutputRate: number;

  /**
   * Giá train cho base
   */
  baseTrainRate: number;

  /**
   * Giá input cho fine-tuning
   */
  fineTuningInputRate: number;

  /**
   * Giá output cho fine-tuning
   */
  fineTuningOutputRate: number;

  /**
   * Giá train cho fine-tuning
   */
  fineTuningTrainRate: number;

  /**
   * Cấu hình của model base
   */
  config: ModelBaseConfig;
}

/**
 * Interface cho tham số truy vấn danh sách model base
 */
export interface ModelBaseQueryParams {
  /**
   * Trang hiện tại
   */
  page?: number;

  /**
   * Số lượng kết quả trên một trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm
   */
  search?: string;

  /**
   * Trường sắp xếp
   */
  sortBy?: string;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC';

  /**
   * Lọc theo ID nhà cung cấp
   */
  providerId?: string;

  /**
   * Lọc theo trạng thái
   */
  status?: ModelBaseStatus;
}
