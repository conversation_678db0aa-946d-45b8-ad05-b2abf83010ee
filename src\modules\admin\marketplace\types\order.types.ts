/**
 * <PERSON><PERSON><PERSON> nghĩa các types cho Order trong Admin Marketplace
 */
import { Product } from './product.types';

/**
 * Enum cho trạng thái đơn hàng
 */
export enum OrderStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  FAILED = 'FAILED',
}

/**
 * Enum cho loại đơn hàng
 */
export enum OrderType {
  PURCHASE = 'PURCHASE',
  SUBSCRIPTION = 'SUBSCRIPTION',
}

/**
 * Interface cho item trong đơn hàng
 */
export interface OrderItem {
  id: string;
  productId: string;
  quantity: number;
  price: number;
  product: Product;
}

/**
 * Interface cho thông tin thanh toán
 */
export interface PaymentInfo {
  method: string;
  transactionId?: string;
  status: string;
  amount: number;
  currency: string;
  paidAt?: string;
}

/**
 * Interface cho đơn hàng
 */
export interface Order {
  id: string;
  userId: number;
  orderNumber: string;
  items: OrderItem[];
  totalItems: number;
  totalPrice: number;
  status: OrderStatus;
  type: OrderType;
  paymentInfo?: PaymentInfo;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho tham số filter của đơn hàng
 */
export interface OrderFilterParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: OrderStatus | string;
  type?: OrderType | string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho phản hồi API danh sách đơn hàng
 */
export interface OrderListData {
  items: Order[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Alias cho kiểu dữ liệu phản hồi API danh sách đơn hàng
 */
export type OrderListResponse = OrderListData;

/**
 * Alias cho kiểu dữ liệu phản hồi API chi tiết đơn hàng
 */
export type OrderDetailResponse = Order;
