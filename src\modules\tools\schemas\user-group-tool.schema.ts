import * as z from 'zod';

/**
 * Schema cho tham số truy vấn danh sách nhóm tool
 */
export const userGroupToolQuerySchema = z.object({
  page: z.coerce.number().optional().default(1),
  limit: z.coerce.number().optional().default(10),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
  typeAgentId: z.coerce.number().optional(),
});

/**
 * Schema cho tham số tạo mới nhóm tool
 */
export const createUserGroupToolSchema = z.object({
  name: z.string().min(1, 'Tên nhóm tool là bắt buộc'),
  description: z.string().optional(),
  toolIds: z.array(z.string()).optional(),
  typeAgentId: z.number().optional(),
});

/**
 * Schema cho tham số cập nhật nhóm tool
 */
export const updateUserGroupToolSchema = z.object({
  name: z.string().min(1, 'Tên nhóm tool là bắt buộc').optional(),
  description: z.string().optional(),
  typeAgentId: z.number().optional(),
});

/**
 * Schema cho tham số cập nhật danh sách tool của nhóm
 */
export const updateGroupToolToolsSchema = z.object({
  toolIds: z.array(z.string()),
});
