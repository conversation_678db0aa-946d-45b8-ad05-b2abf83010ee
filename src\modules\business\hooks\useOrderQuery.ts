import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { OrderService, OrderQueryParams, CreateOrderData, UpdateOrderData } from '../services/order.service';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';
import { useTranslation } from 'react-i18next';

/**
 * Query keys cho order API
 */
export const ORDER_QUERY_KEYS = {
  all: ['business', 'orders'] as const,
  list: (params: OrderQueryParams) => [...ORDER_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...ORDER_QUERY_KEYS.all, 'detail', id] as const,
};

/**
 * Hook lấy danh sách đơn hàng
 */
export const useOrders = (params: OrderQueryParams = {}) => {
  // t được sử dụng trong các thông báo lỗi ở các hook khác
  useTranslation(['business', 'common']);

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.list(params),
    queryFn: () => OrderService.getOrders(params),
    select: (data) => data.result,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook lấy chi tiết đơn hàng theo ID
 */
export const useOrder = (id: number) => {
  // t được sử dụng trong các thông báo lỗi ở các hook khác
  useTranslation(['business', 'common']);

  return useQuery({
    queryKey: ORDER_QUERY_KEYS.detail(id),
    queryFn: () => OrderService.getOrderById(id),
    select: (data) => data.result,
    enabled: !!id,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook tạo đơn hàng mới
 */
export const useCreateOrder = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (data: CreateOrderData) => OrderService.createOrder(data),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:order.createSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:order.createError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook cập nhật đơn hàng
 */
export const useUpdateOrder = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateOrderData }) =>
      OrderService.updateOrder(id, data),
    onSuccess: (_, variables) => {
      NotificationUtil.success({
        message: t('business:order.updateSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.detail(variables.id),
      });

      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:order.updateError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook xóa đơn hàng
 */
export const useDeleteOrder = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: (id: number) => OrderService.deleteOrder(id),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:order.deleteSuccess'),
        duration: 3000,
      });

      queryClient.invalidateQueries({
        queryKey: ORDER_QUERY_KEYS.all,
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      NotificationUtil.error({
        message: error.response?.data?.message || t('business:order.deleteError'),
        duration: 3000,
      });
    },
  });
};
