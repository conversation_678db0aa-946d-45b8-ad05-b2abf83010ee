import React, { ComponentType, createElement } from 'react';
import { RouteGuardType } from './types';
import RouteGuard from './RouteGuard';

/**
 * HOC bảo vệ route
 * @param Component Component cần bảo vệ
 * @param type Loại bảo vệ route
 * @param redirectTo Đường dẫn chuyển hướng khi không có quyền truy cập
 * @returns Component đã được bảo vệ
 */
export const withRouteGuard = <P extends object>(
  Component: ComponentType<P>,
  type: RouteGuardType,
  redirectTo?: string
): React.FC<P> => {
  // Tạo một component mới bọc component gốc
  const WrappedComponent: React.FC<P> = (props: P) => {
    // Tạo một hàm render component gốc với props
    const renderComponent = () => createElement(Component, props);

    // Tạo RouteGuard component với các props cần thiết
    return createElement(RouteGuard, {
      component: renderComponent,
      type,
      redirectTo
    });
  };

  return WrappedComponent;
};
