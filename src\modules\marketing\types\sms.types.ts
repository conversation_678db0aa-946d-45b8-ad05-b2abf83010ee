/**
 * Types cho SMS Marketing
 */

/**
 * Trạng thái brandname SMS
 */
export enum SmsBrandnameStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  REJECTED = 'rejected',
}

/**
 * Loại brandname SMS
 */
export enum SmsBrandnameType {
  ADVERTISEMENT = 'advertisement',
  CUSTOMER_CARE = 'customer_care',
}

/**
 * Trạng thái template SMS
 */
export enum SmsTemplateStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  REJECTED = 'rejected',
}

/**
 * Trạng thái tin nhắn SMS
 */
export enum SmsMessageStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  PENDING = 'pending',
}

/**
 * Trạng thái chiến dịch SMS
 */
export enum SmsCampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  RUNNING = 'running',
  COMPLETED = 'completed',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
}

/**
 * DTO brandname SMS
 */
export interface SmsBrandnameDto {
  id: number;
  userId: number;
  name: string;
  brandname: string;
  type: SmsBrandnameType;
  status: SmsBrandnameStatus;
  provider: string;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO template SMS
 */
export interface SmsTemplateDto {
  id: number;
  userId: number;
  brandnameId: number;
  name: string;
  content: string;
  params: string[];
  status: SmsTemplateStatus;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO tin nhắn SMS
 */
export interface SmsMessageDto {
  id: number;
  userId: number;
  brandnameId: number;
  templateId?: number;
  campaignId?: number;
  phone: string;
  content: string;
  status: SmsMessageStatus;
  errorCode?: string;
  errorMessage?: string;
  sentAt?: number;
  deliveredAt?: number;
  createdAt: number;
}

/**
 * DTO chiến dịch SMS
 */
export interface SmsCampaignDto {
  id: number;
  userId: number;
  brandnameId: number;
  templateId?: number;
  name: string;
  content: string;
  status: SmsCampaignStatus;
  totalMessages: number;
  deliveredCount: number;
  failedCount: number;
  scheduleTime?: number;
  startedAt?: number;
  completedAt?: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO tham số truy vấn brandname SMS
 */
export interface SmsBrandnameQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: SmsBrandnameStatus;
  type?: SmsBrandnameType;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tham số truy vấn template SMS
 */
export interface SmsTemplateQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  brandnameId?: number;
  status?: SmsTemplateStatus;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tham số truy vấn tin nhắn SMS
 */
export interface SmsMessageQueryDto {
  page?: number;
  limit?: number;
  brandnameId?: number;
  campaignId?: number;
  phone?: string;
  status?: SmsMessageStatus;
  startDate?: number;
  endDate?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tham số truy vấn chiến dịch SMS
 */
export interface SmsCampaignQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  brandnameId?: number;
  status?: SmsCampaignStatus;
  startDate?: number;
  endDate?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tạo brandname SMS
 */
export interface CreateSmsBrandnameDto {
  name: string;
  brandname: string;
  type: SmsBrandnameType;
  provider: string;
}

/**
 * DTO cập nhật brandname SMS
 */
export interface UpdateSmsBrandnameDto {
  name?: string;
  status?: SmsBrandnameStatus;
}

/**
 * DTO tạo template SMS
 */
export interface CreateSmsTemplateDto {
  brandnameId: number;
  name: string;
  content: string;
  params?: string[];
}

/**
 * DTO cập nhật template SMS
 */
export interface UpdateSmsTemplateDto {
  name?: string;
  content?: string;
  params?: string[];
  status?: SmsTemplateStatus;
}

/**
 * DTO gửi tin nhắn SMS
 */
export interface SendSmsMessageDto {
  brandnameId: number;
  templateId?: number;
  phone: string;
  content?: string;
  params?: Record<string, string>;
}

/**
 * DTO gửi nhiều tin nhắn SMS
 */
export interface SendBulkSmsMessageDto {
  brandnameId: number;
  templateId?: number;
  phones: string[];
  content?: string;
  params?: Record<string, string>[];
}

/**
 * DTO tạo chiến dịch SMS
 */
export interface CreateSmsCampaignDto {
  brandnameId: number;
  templateId?: number;
  name: string;
  content?: string;
  phones: string[];
  params?: Record<string, string>[];
  scheduleTime?: number;
}

/**
 * DTO cập nhật chiến dịch SMS
 */
export interface UpdateSmsCampaignDto {
  name?: string;
  status?: SmsCampaignStatus;
  scheduleTime?: number;
}

/**
 * DTO phản hồi paging
 */
export interface PagingResponseDto<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
} 