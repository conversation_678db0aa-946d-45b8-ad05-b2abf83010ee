import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AgentDetail } from '../types/agent.types';
import { Gender, EducationLevel } from '../types/agent.types';

// Mock data service - sẽ được thay thế bằng API thực tế
const AgentService = {
  getAgentById: async (id: string): Promise<{ result: AgentDetail }> => {
    // Giả lập API call
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          result: {
            profile: {
              id,
              name: 'AI Assistant',
              birthDate: '2000-01-01',
              gender: Gender.MALE,
              language: 'vi',
              educationLevel: EducationLevel.BACHELOR,
              country: 'Vietnam',
              position: 'Sales Assistant',
              skills: ['Sales', 'Customer Support', 'Product Knowledge'],
              personality: ['Friendly', 'Professional', 'Helpful'],
              avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
            },
            facebookPages: [
              {
                id: '1',
                name: 'Business Page',
                imageUrl: 'https://randomuser.me/api/portraits/men/2.jpg',
                isConnected: true,
              },
            ],
            websites: [
              {
                id: '1',
                url: 'https://example.com',
                name: 'Company Website',
                favicon: 'https://example.com/favicon.ico',
                isConnected: true,
              },
            ],
            strategy: {
              id: '1',
              name: 'Sales Strategy',
              description: 'Optimize for sales conversion',
              parameters: [
                {
                  id: '1',
                  name: 'aggressiveness',
                  type: 'number',
                  value: 7,
                  min: 1,
                  max: 10,
                  step: 1,
                },
                {
                  id: '2',
                  name: 'followUpFrequency',
                  type: 'select',
                  value: 'medium',
                  options: [
                    { label: 'Low', value: 'low' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'High', value: 'high' },
                  ],
                },
              ],
            },
            conversionFields: [
              {
                id: '1',
                name: 'email',
                type: 'email',
                isRequired: true,
              },
              {
                id: '2',
                name: 'phone',
                type: 'phone',
                isRequired: false,
              },
            ],
            vector: {
              id: '1',
              name: 'Default Vector',
              description: 'Standard embedding vector',
              configuration: {
                dimensions: 1536,
                model: 'text-embedding-3-small',
              },
            },
            resources: [
              {
                id: '1',
                name: 'Product Catalog',
                type: 'document',
                url: 'https://example.com/catalog.pdf',
                size: 1024 * 1024 * 2, // 2MB
                isSelected: true,
              },
            ],
            modules: [
              {
                id: '1',
                name: 'GPT-4',
                description: 'Advanced language model',
                isEnabled: true,
                configuration: {
                  maxTokens: 2048,
                  topP: 0.9,
                  topK: 40,
                  temperature: 0.7,
                },
              },
            ],
          },
        });
      }, 500);
    });
  },
  updateAgent: async (id: string, data: Partial<AgentDetail>): Promise<{ result: AgentDetail }> => {
    // Giả lập API call
    return new Promise(resolve => {
      setTimeout(() => {
        // Tạo một đối tượng AgentDetail hoàn chỉnh từ dữ liệu cục bộ
        const profileWithId = {
          id,
          name: 'Default Name',
          skills: [],
          personality: [],
          ...(data.profile || {}),
        };
        
        const result: AgentDetail = {
          profile: profileWithId,
          facebookPages: data.facebookPages || [],
          websites: data.websites || [],
          strategy: data.strategy,
          conversionFields: data.conversionFields || [],
          vector: data.vector,
          resources: data.resources || [],
          modules: data.modules || [],
        };
        
        resolve({
          result,
        });
      }, 500);
    });
  },
};

// Query keys
export const AGENT_QUERY_KEYS = {
  all: ['agents'],
  lists: () => [...AGENT_QUERY_KEYS.all, 'list'],
  list: (filters: Record<string, unknown>) => [...AGENT_QUERY_KEYS.lists(), filters],
  details: () => [...AGENT_QUERY_KEYS.all, 'detail'],
  detail: (id: string) => [...AGENT_QUERY_KEYS.details(), id],
};

/**
 * Hook để lấy thông tin chi tiết của Agent
 * @param id ID của Agent
 */
export const useAgentDetail = (id: string) => {
  return useQuery({
    queryKey: AGENT_QUERY_KEYS.detail(id),
    queryFn: () => AgentService.getAgentById(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để cập nhật thông tin Agent
 */
export const useUpdateAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<AgentDetail> }) =>
      AgentService.updateAgent(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch
      queryClient.invalidateQueries({ queryKey: AGENT_QUERY_KEYS.detail(variables.id) });
    },
  });
};
