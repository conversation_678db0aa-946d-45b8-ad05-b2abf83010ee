import { apiClient } from '@/shared/api/axios';
import {
  ToolDetail,
  ToolListItem,
  ToolQueryParams,
  PaginatedResult,
  CreateToolParams,
  UpdateToolParams,
} from '../types/tool.types';

/**
 * Service để tương tác với API tools của admin
 */
export class AdminToolService {
  private baseUrl = `/admin/tools`;

  /**
   * Lấy danh sách tool
   * @param params Tham số truy vấn
   * @returns Danh sách tool với phân trang
   */
  async getTools(params?: ToolQueryParams): Promise<PaginatedResult<ToolListItem>> {
    try {
      const response = await apiClient.get<PaginatedResult<ToolListItem>>(this.baseUrl, {
        params,
        tokenType: 'admin'
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching admin tools:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết tool
   * @param id ID của tool
   * @returns Thông tin chi tiết tool
   */
  async getToolById(id: string): Promise<ToolDetail> {
    try {
      const response = await apiClient.get<ToolDetail>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin'
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching admin tool with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Tạo tool mới
   * @param data Dữ liệu tạo tool
   * @returns ID của tool đã tạo
   */
  async createTool(data: CreateToolParams): Promise<string> {
    try {
      const response = await apiClient.post<{ id: string }>(this.baseUrl, data, {
        tokenType: 'admin'
      });
      return response.result.id;
    } catch (error) {
      console.error('Error creating admin tool:', error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin tool
   * @param id ID của tool
   * @param data Dữ liệu cập nhật
   * @returns Kết quả cập nhật
   */
  async updateTool(id: string, data: UpdateToolParams): Promise<boolean> {
    try {
      const response = await apiClient.put<{ success: boolean }>(`${this.baseUrl}/${id}`, data, {
        tokenType: 'admin'
      });
      return response.result.success;
    } catch (error) {
      console.error(`Error updating admin tool with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa tool
   * @param id ID của tool
   * @returns Kết quả xóa
   */
  async deleteTool(id: string): Promise<boolean> {
    try {
      const response = await apiClient.delete<{ success: boolean }>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin'
      });
      return response.result.success;
    } catch (error) {
      console.error(`Error deleting admin tool with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Đặt phiên bản mặc định cho tool
   * @param toolId ID của tool
   * @param versionId ID của phiên bản
   * @returns Kết quả cập nhật
   */
  async setDefaultVersion(toolId: string, versionId: string): Promise<boolean> {
    try {
      const response = await apiClient.put<{ success: boolean }>(
        `${this.baseUrl}/${toolId}/default-version/${versionId}`,
        {},
        { tokenType: 'admin' }
      );
      return response.result.success;
    } catch (error) {
      console.error(`Error setting default version for tool ${toolId}:`, error);
      throw error;
    }
  }
}
