import { ToolStatus, ToolSortBy, AccessType } from './common.types';

/**
 * Interface cho thông tin phiên bản tool
 */
export interface ToolVersion {
  id: string;
  versionNumber: number;
  toolName: string;
  versionName?: string;
  toolDescription: string | null;
  parameters: Record<string, unknown>;
  changeDescription: string | null;
  status: ToolStatus;
  createdAt: number;
  edited: boolean;
}

/**
 * Interface cho thông tin tool trong danh sách
 */
export interface ToolListItem {
  id: string;
  name: string;
  description: string | null;
  status: ToolStatus;
  createdAt: number;
  updatedAt: number;
  originalId: string | null;
  hasUpdate: boolean;
  groupId: number | null;
  groupName: string | null;
  isActive?: boolean;
}

/**
 * Interface cho thông tin chi tiết tool
 */
export interface ToolDetail extends ToolListItem {
  defaultVersion: ToolVersion | null;
  versions: ToolVersion[];
}

/**
 * Interface cho tham số truy vấn danh sách tool
 */
export interface ToolQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: ToolStatus;
  hasUpdate?: boolean;
  sortBy?: ToolSortBy;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho tham số của tool
 */
export interface ToolParameter {
  name: string;
  type: string;
  description?: string;
  required: boolean;
  defaultValue?: unknown;
  options?: unknown[];
}

/**
 * Interface cho tham số tạo mới tool
 */
export interface CreateToolParams {
  name: string;
  description?: string;
  groupId?: number;
  toolName: string;
  toolDescription?: string;
  parameters: Record<string, unknown>;
  status: ToolStatus;
  accessType: AccessType;
}

/**
 * Interface cho tham số cập nhật tool
 */
export interface UpdateToolParams {
  name?: string;
  description?: string;
  groupId?: number;
}

/**
 * Interface cho tham số chỉnh sửa phiên bản tool
 */
export interface EditToolVersionParams {
  toolName?: string;
  toolDescription?: string;
  parameters?: Record<string, unknown>;
  changeDescription?: string;
}

/**
 * Interface cho tham số sao chép tool từ admin
 */
export interface CloneAdminToolParams {
  adminToolId: string;
  adminVersionId?: string;
}

/**
 * Interface cho tham số cập nhật tool từ phiên bản admin
 */
export interface UpdateFromAdminParams {
  userToolId: string;
  adminVersionId: string;
}

/**
 * Interface cho tham số sao chép tất cả tool công khai từ admin
 */
export interface CloneAllPublicToolsParams {
  groupId?: number;
}

/**
 * Interface cho tham số khôi phục về phiên bản admin
 */
export interface RollbackToAdminVersionParams {
  userToolId: string;
  adminVersionId: string;
}
