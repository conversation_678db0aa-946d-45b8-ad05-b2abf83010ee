import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested
} from 'class-validator';

/**
 * DTO cho giá trị của phân loại
 */
export class ClassificationValueDto {
  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh',
    example: 'Đỏ',
  })
  @IsString()
  @IsNotEmpty()
  value: string;
}

/**
 * DTO cho giá của phân loại khi typePrice là HAS_PRICE
 */
export class ClassificationPriceDto {
  @ApiProperty({
    description: '<PERSON>i<PERSON> niêm yết',
    example: 200000,
  })
  @IsNumber()
  @IsNotEmpty()
  listPrice: number;

  @ApiProperty({
    description: 'Giá bán',
    example: 150000,
  })
  @IsNumber()
  @IsNotEmpty()
  salePrice: number;

  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
  })
  @IsString()
  @IsNotEmpty()
  currency: string;
}

/**
 * DTO cho trường tùy chỉnh của phân loại
 */
export class ClassificationCustomFieldDto {
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  customFieldId: number;

  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh',
    type: ClassificationValueDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => ClassificationValueDto)
  value: ClassificationValueDto;
}

/**
 * DTO cho tạo phân loại sản phẩm
 */
export class CreateClassificationDto {
  @ApiProperty({
    description: 'Loại phân loại',
    example: 'Màu sắc',
  })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({
    description: 'Giá của phân loại',
    type: ClassificationPriceDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ClassificationPriceDto)
  price?: ClassificationPriceDto;

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh của phân loại',
    type: [ClassificationCustomFieldDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClassificationCustomFieldDto)
  customFields?: ClassificationCustomFieldDto[];
}

/**
 * DTO cho cập nhật phân loại sản phẩm
 */
export class UpdateClassificationDto {
  @ApiProperty({
    description: 'ID của phân loại',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'Loại phân loại',
    example: 'Màu sắc',
    required: false,
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({
    description: 'Giá của phân loại',
    type: ClassificationPriceDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ClassificationPriceDto)
  price?: ClassificationPriceDto;

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh của phân loại',
    type: [ClassificationCustomFieldDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClassificationCustomFieldDto)
  customFields?: ClassificationCustomFieldDto[];
}

/**
 * DTO cho phản hồi phân loại sản phẩm
 */
export class ClassificationResponseDto {
  @ApiProperty({
    description: 'ID của phân loại',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Loại phân loại',
    example: 'Màu sắc',
  })
  type: string;

  @ApiProperty({
    description: 'Giá của phân loại',
    example: { value: 150000, listPrice: 200000, salePrice: 150000, currency: 'VND' },
    required: false,
  })
  price?: { value?: number; listPrice?: number; salePrice?: number; currency?: string };

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh của phân loại',
    type: [ClassificationCustomFieldDto],
    required: false,
  })
  customFields?: ClassificationCustomFieldDto[];
}
