import { apiClient } from '@/shared/api/axios';
import { PaginatedResult, UserGroupToolDetail, UserGroupToolListItem, UserGroupToolQueryParams, CreateUserGroupToolParams, UpdateUserGroupToolParams } from '../types';

/**
 * Service xử lý các API liên quan đến nhóm tool của người dùng
 */
export class UserGroupToolService {
  private baseUrl = '/user/group-tools';

  /**
   * Lấy danh sách nhóm tool
   * @param params Tham số truy vấn
   * @returns Danh sách nhóm tool với phân trang
   */
  async getGroupTools(params: UserGroupToolQueryParams): Promise<PaginatedResult<UserGroupToolListItem>> {
    try {
      const response = await apiClient.get<PaginatedResult<UserGroupToolListItem>>(
        this.baseUrl,
        {
          params,
          tokenType: 'user',
        }
      );
      return response.result;
    } catch (error) {
      console.error('Error fetching group tools:', error);
      throw error;
    }
  }

  /**
   * <PERSON><PERSON>y thông tin chi tiết nhóm tool
   * @param id ID của nhóm tool
   * @returns Thông tin chi tiết nhóm tool
   */
  async getGroupToolById(id: number): Promise<UserGroupToolDetail> {
    try {
      const response = await apiClient.get<UserGroupToolDetail>(
        `${this.baseUrl}/${id}`,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error(`Error fetching group tool ${id}:`, error);
      throw error;
    }
  }

  /**
   * Lấy danh sách nhóm tool theo loại agent
   * @param typeAgentId ID của loại agent
   * @returns Danh sách nhóm tool
   */
  async getGroupToolsByTypeAgentId(typeAgentId: number): Promise<UserGroupToolListItem[]> {
    try {
      const response = await apiClient.get<UserGroupToolListItem[]>(
        `${this.baseUrl}/by-type-agent/${typeAgentId}`,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error(`Error fetching group tools by type agent ${typeAgentId}:`, error);
      throw error;
    }
  }

  /**
   * Tạo mới nhóm tool
   * @param params Tham số tạo mới
   * @returns Nhóm tool đã tạo
   */
  async createGroupTool(params: CreateUserGroupToolParams): Promise<UserGroupToolDetail> {
    try {
      const response = await apiClient.post<UserGroupToolDetail>(
        this.baseUrl,
        params,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error('Error creating group tool:', error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin nhóm tool
   * @param id ID của nhóm tool
   * @param params Tham số cập nhật
   * @returns Nhóm tool đã cập nhật
   */
  async updateGroupTool(id: number, params: UpdateUserGroupToolParams): Promise<UserGroupToolDetail> {
    try {
      const response = await apiClient.put<UserGroupToolDetail>(
        `${this.baseUrl}/${id}`,
        params,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error(`Error updating group tool ${id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa nhóm tool
   * @param id ID của nhóm tool
   * @returns true nếu xóa thành công
   */
  async deleteGroupTool(id: number): Promise<boolean> {
    try {
      const response = await apiClient.delete<boolean>(
        `${this.baseUrl}/${id}`,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error(`Error deleting group tool ${id}:`, error);
      throw error;
    }
  }

  /**
   * Cập nhật danh sách tool của nhóm
   * @param id ID của nhóm tool
   * @param toolIds Danh sách ID của tool
   * @returns true nếu cập nhật thành công
   */
  async updateGroupToolTools(id: number, toolIds: string[]): Promise<boolean> {
    try {
      const response = await apiClient.put<boolean>(
        `${this.baseUrl}/${id}/tools`,
        { toolIds },
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error(`Error updating group tool tools ${id}:`, error);
      throw error;
    }
  }
}

export const userGroupToolService = new UserGroupToolService();
