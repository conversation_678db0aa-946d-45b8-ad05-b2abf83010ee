import React from 'react';
import Icon from '@/shared/components/common/Icon/Icon';
import type { IconName } from '@/shared/components/common/Icon/Icon';
import { useTheme } from '@/shared/contexts/theme';

export interface FileIconProps {
  /**
   * Tên file
   */
  fileName: string;

  /**
   * <PERSON>ích thước của icon
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị icon cho file dựa trên phần mở rộng
 */
const FileIcon: React.FC<FileIconProps> = ({
  fileName,
  size = 'md',
  className = '',
}) => {
  const { currentTheme } = useTheme();

  // Lấy phần mở rộng từ tên file
  const getFileExtension = (): string => {
    return fileName.split('.').pop()?.toLowerCase() || '';
  };

  // Lấy icon dựa trên phần mở rộng của file
  const getFileIcon = (): IconName => {
    const extension = getFileExtension();

    if (extension === 'pdf') return 'file-pdf';
    if (['doc', 'docx'].includes(extension)) return 'file-text';
    if (['xls', 'xlsx', 'csv'].includes(extension)) return 'file-spreadsheet';
    if (['ppt', 'pptx'].includes(extension)) return 'file-presentation';
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension)) return 'image';
    if (['mp4', 'avi', 'mov', 'wmv'].includes(extension)) return 'video';
    if (['mp3', 'wav', 'ogg'].includes(extension)) return 'music';
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) return 'archive';
    if (['html', 'htm'].includes(extension)) return 'code';
    if (['js', 'ts', 'jsx', 'tsx'].includes(extension)) return 'code';
    if (['css', 'scss', 'less'].includes(extension)) return 'code';
    if (['json', 'xml'].includes(extension)) return 'code';

    return 'file';
  };

  // Lấy màu cho icon dựa trên phần mở rộng
  const getFileIconColor = (): string => {
    const extension = getFileExtension();

    if (['pdf'].includes(extension)) return 'text-red-500';
    if (['doc', 'docx'].includes(extension)) return 'text-blue-500';
    if (['xls', 'xlsx', 'csv'].includes(extension)) return 'text-green-500';
    if (['ppt', 'pptx'].includes(extension)) return 'text-orange-500';
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension)) return 'text-purple-500';
    if (['mp4', 'avi', 'mov', 'wmv'].includes(extension)) return 'text-pink-500';
    if (['mp3', 'wav', 'ogg'].includes(extension)) return 'text-yellow-500';
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) return 'text-gray-500';

    return currentTheme?.mode === 'dark' ? 'text-gray-300' : 'text-gray-600';
  };

  return (
    <Icon
      name={getFileIcon()}
      size={size}
      className={`${getFileIconColor()} ${className}`}
    />
  );
};

export default FileIcon;
