import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng folders trong cơ sở dữ liệu
 * Bảng quản lý thư mục trong hệ thống
 */
@Entity('folders')
export class Folder {
  /**
   * ID của thư mục
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Tên thư mục
   */
  @Column({
    name: 'name',
    length: 255,
    nullable: false,
    comment: 'Tên thư mục',
  })
  name: string;

  /**
   * ID thư mục cha
   */
  @Column({ name: 'parent_id', nullable: true, comment: 'ID thư mục cha' })
  parentId: number;

  /**
   * ID người dùng sở hữu
   */
  @Column({ name: 'user_id', nullable: false, comment: 'ID người dùng sở hữu' })
  userId: number;

  /**
   * Đường dẫn thư mục
   */
  @Column({
    name: 'path',
    length: 1000,
    nullable: true,
    comment: 'Đường dẫn thư mục',
  })
  path: string;

  /**
   * ID kho ảo gốc
   */
  @Column({ name: 'root', nullable: true, comment: 'ID kho ảo gốc' })
  root: number;

  /**
   * Thời gian tạo (millis)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian tạo (millis)',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian cập nhật (millis)',
  })
  updatedAt: number;
}
