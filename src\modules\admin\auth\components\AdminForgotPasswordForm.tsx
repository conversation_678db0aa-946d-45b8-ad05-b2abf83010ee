import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Form, FormItem, Input, Button, Icon, Alert } from '@/shared/components/common';
import { createAdminForgotPasswordSchema } from '../schemas/admin-auth.schema';
import { useAdminForgotPassword } from '../hooks/useAdminAuthQuery';
import { useAuthCommon } from '@/shared/hooks/useAuthCommon';
import { AdminForgotPasswordRequest, AdminForgotPasswordResponse } from '../types/admin-auth.types';

interface AdminForgotPasswordFormProps {
  onSuccess?: () => void;
  onBackToLogin?: () => void;
}

/**
 * Admin forgot password form component
 */
const AdminForgotPasswordForm: React.FC<AdminForgotPasswordFormProps> = ({
  onSuccess,
  onBackToLogin,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { saveVerifyInfo } = useAuthCommon();
  const { mutate: forgotPassword, isPending } = useAdminForgotPassword();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Create forgot password schema with translations
  const forgotPasswordSchema = createAdminForgotPasswordSchema(t);

  // Handle form submission
  const handleSubmit = (values: Record<string, unknown>) => {
    // Reset error message
    setErrorMessage(null);

    const email = values.email as string;

    // Call forgot password API
    forgotPassword(
      {
        email,
        role: 'admin',
      } as AdminForgotPasswordRequest,
      {
        onSuccess: (response: { result: unknown }) => {
          if (response.result) {
            // Lưu thông tin xác thực vào Redux
            const result = response.result as unknown as AdminForgotPasswordResponse;

            saveVerifyInfo({
              verifyToken: result.otpToken,
              expiresAt: result.expiresAt,
              info: {
                email: result.maskedEmail,
              },
            });

            // Chuyển hướng đến trang xác thực OTP
            navigate('/admin/auth/verify-forgot-password');
          }

          // Call success callback if provided
          if (onSuccess) {
            onSuccess();
          }
        },
        onError: (error: unknown) => {
          console.error('Admin forgot password error:', error);

          // Lấy thông báo lỗi từ response API
          let errorMsg = t(
            'adminAuth:auth.forgotPasswordError',
            'Đã xảy ra lỗi khi yêu cầu đặt lại mật khẩu'
          );

          // Kiểm tra xem error có phải là AxiosError không
          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as { response: { data?: { message?: string } } };
            if (axiosError.response.data?.message) {
              errorMsg = axiosError.response.data.message;
            }
          }

          setErrorMessage(errorMsg);
        },
      }
    );
  };

  return (
    <div>
      <Form schema={forgotPasswordSchema} onSubmit={handleSubmit} className="space-y-4">
        <FormItem name="email" label={t('auth:email')} required>
          <Input
            type="email"
            placeholder="<EMAIL>"
            leftIcon={<Icon name="mail" size="sm" />}
            fullWidth
          />
        </FormItem>

        <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
          {t(
            'adminAuth:auth.forgotPasswordDescription',
            'Nhập địa chỉ email của bạn và chúng tôi sẽ gửi cho bạn mã OTP để đặt lại mật khẩu.'
          )}
        </div>

        {errorMessage && <Alert type="error" message={errorMessage} className="mb-4" />}

        <Button type="submit" variant="primary" fullWidth isLoading={isPending}>
          {t('adminAuth:auth.resetPassword')}
        </Button>

        <div className="text-center mt-4">
          <a
            href="#"
            onClick={e => {
              e.preventDefault();
              onBackToLogin?.();
            }}
            className="text-primary hover:text-primary/80 text-sm font-medium"
          >
            {t('auth:backToLogin')}
          </a>
        </div>
      </Form>
    </div>
  );
};

export default AdminForgotPasswordForm;
