import { ApiKeyLocation, AuthType, TokenSource, UserInfo } from './common.types';

/**
 * Interface định nghĩa cấu hình xác thực API Key
 */
export interface ApiKeyAuthConfig {
  authType: AuthType.API_KEY;
  schemeName: string;
  apiKey: string;
  apiKeyLocation: ApiKeyLocation;
  paramName: string;
}

/**
 * Interface định nghĩa cấu hình xác thực OAuth
 */
export interface OAuthAuthConfig {
  authType: AuthType.OAUTH;
  schemeName: string;
  token: string;
  tokenSource: TokenSource;
}

/**
 * Interface định nghĩa cấu hình không xác thực
 */
export interface NoAuthConfig {
  authType: AuthType.NONE;
}

/**
 * Type định nghĩa cấu hình xác thực
 */
export type AuthConfig = ApiKeyAuthConfig | OAuthAuthConfig | NoAuthConfig;

/**
 * Interface định nghĩa thông tin endpoint bị lỗi
 */
export interface FailedEndpoint {
  path: string;
  method: string;
  reason: string;
}

/**
 * Interface định nghĩa kết quả tích hợp từ OpenAPI
 */
export interface IntegrationResult {
  toolsCreated: number;
  resourcesCreated: number;
  failedEndpoints: FailedEndpoint[];
  authConfig?: {
    apiKeyCreated: number;
    oauthCreated: number;
  };
}

/**
 * Interface định nghĩa tham số tích hợp từ OpenAPI
 */
export interface IntegrateFromOpenApiParams {
  openapiSpec: Record<string, unknown>;
  baseUrl?: string;
  authConfig?: AuthConfig;
}

/**
 * Interface định nghĩa thông tin cơ bản của tool tích hợp
 */
export interface IntegrationToolListItem {
  id: string;
  name: string;
  description?: string;
  baseUrl: string;
  hasAuth: boolean;
  authType?: AuthType;
  createdAt: number;
  updatedAt: number;
  createdBy?: UserInfo;
  updatedBy?: UserInfo;
  endpointCount: number;
}

/**
 * Interface định nghĩa thông tin chi tiết của tool tích hợp
 */
export interface IntegrationToolDetail extends IntegrationToolListItem {
  authConfig?: AuthConfig;
  endpoints: IntegrationEndpoint[];
}

/**
 * Interface định nghĩa thông tin endpoint của tool tích hợp
 */
export interface IntegrationEndpoint {
  id: string;
  path: string;
  method: string;
  summary?: string;
  description?: string;
  parameters?: Record<string, unknown>[];
  requestBody?: Record<string, unknown>;
  responses?: Record<string, unknown>;
}

/**
 * Interface định nghĩa tham số cập nhật xác thực
 */
export interface UpdateToolAuthParams {
  toolId: string;
  authConfig: AuthConfig;
}

/**
 * Interface định nghĩa tham số cập nhật base URL
 */
export interface UpdateBaseUrlParams {
  toolId: string;
  baseUrl: string;
}
