import React from 'react';
import { Card, Typography, Icon } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';
import { FileResponseDto } from '@/modules/data/knowledge-files';

export interface FileItemCardProps {
  /**
   * Thông tin file
   */
  file: FileResponseDto;

  /**
   * Kích thước của card
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Hiệu ứng hover
   */
  hoverable?: boolean;

  /**
   * Sự kiện khi click vào card
   */
  onClick?: (file: FileResponseDto) => void;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị thông tin file dạng card
 */
const FileItemCard: React.FC<FileItemCardProps> = ({
  file,
  size = 'md',
  hoverable = false,
  onClick,
  className = '',
}) => {
  // Không sử dụng useTranslation vì không có text cần dịch

  // Xác định kích thước dựa trên prop size
  const sizeClasses = {
    sm: 'p-3 lg:p-4',
    md: 'p-4 lg:p-6',
    lg: 'p-5 lg:p-8',
  }[size];

  // Xác định kích thước icon dựa trên size
  const iconSizes = {
    sm: 'w-10 h-10 lg:w-12 lg:h-12',
    md: 'w-12 h-12 lg:w-14 lg:h-14',
    lg: 'w-14 h-14 lg:w-16 lg:h-16',
  }[size];

  // Xác định kích thước font cho tiêu đề
  const titleSizes = {
    sm: 'text-sm lg:text-base',
    md: 'text-base lg:text-lg',
    lg: 'text-lg lg:text-xl',
  }[size];

  // Xác định kích thước font cho thông tin
  const infoSizes = {
    sm: 'text-xs lg:text-sm',
    md: 'text-sm lg:text-base',
    lg: 'text-base lg:text-lg',
  }[size];

  // Xử lý sự kiện click
  const handleClick = () => {
    if (onClick) {
      onClick(file);
    }
  };

  // Định dạng kích thước file
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  // Lấy icon dựa trên phần mở rộng của file
  const getFileIcon = (): IconName => {
    const extension = file.extension.toLowerCase();

    if (['pdf'].includes(extension)) return 'file-pdf';
    if (['doc', 'docx'].includes(extension)) return 'file-text';
    if (['xls', 'xlsx', 'csv'].includes(extension)) return 'file-spreadsheet';
    if (['ppt', 'pptx'].includes(extension)) return 'presentation';
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension)) return 'image';
    if (['mp4', 'avi', 'mov', 'wmv'].includes(extension)) return 'video';
    if (['mp3', 'wav', 'ogg'].includes(extension)) return 'music';
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) return 'archive';
    if (['html', 'htm'].includes(extension)) return 'code';
    if (['txt', 'md'].includes(extension)) return 'file-text';
    if (['json', 'xml'].includes(extension)) return 'code';

    return 'file';
  };

  // Lấy màu cho icon dựa trên phần mở rộng
  const getFileIconColor = (): string => {
    const extension = file.extension.toLowerCase();

    if (['pdf'].includes(extension)) return 'text-red-500';
    if (['doc', 'docx'].includes(extension)) return 'text-blue-500';
    if (['xls', 'xlsx', 'csv'].includes(extension)) return 'text-green-500';
    if (['ppt', 'pptx'].includes(extension)) return 'text-orange-500';
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension)) return 'text-purple-500';
    if (['mp4', 'avi', 'mov', 'wmv'].includes(extension)) return 'text-pink-500';
    if (['mp3', 'wav', 'ogg'].includes(extension)) return 'text-yellow-500';
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) return 'text-gray-500';

    return 'text-gray-400';
  };

  return (
    <Card
      className={`overflow-hidden shadow-sm ${hoverable ? 'hover:shadow-lg transition-shadow duration-300' : ''} ${className}`}
      variant="default" // Không sử dụng border
      onClick={handleClick}
    >
      <div className={`flex items-center justify-between ${sizeClasses}`}>
        <div className="flex items-center">
          {/* Icon file */}
          <div className="mr-3 flex-shrink-0">
            <div className={`rounded-lg flex items-center justify-center ${iconSizes} bg-gray-100 dark:bg-gray-800`}>
              <Icon
                name={getFileIcon()}
                size="lg"
                className={`${getFileIconColor()}`}
              />
            </div>
          </div>

          {/* Thông tin file */}
          <div className="flex-1 min-w-0">
            <Typography
              variant="body1"
              weight="medium"
              className={`text-gray-900 dark:text-white ${titleSizes} line-clamp-2`}
            >
              {file.name}
            </Typography>
            <div className="flex items-center mt-1">
              <Typography
                variant="body2"
                className={`text-gray-600 dark:text-gray-400 ${infoSizes}`}
              >
                {file.extension.toUpperCase()} • {formatFileSize(file.storage)}
              </Typography>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default FileItemCard;
