import React from 'react';
import { Card, IconCard, Tooltip } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { ToolListItem } from '../types/tool.types';

interface ToolCardProps {
  tool: ToolListItem;
  onView?: (tool: ToolListItem) => void;
  onEdit?: (tool: ToolListItem) => void;
}

/**
 * Component hiển thị thông tin của một Tool
 */
const ToolCard: React.FC<ToolCardProps> = ({ tool, onView, onEdit }) => {
  const { t } = useTranslation();

  const handleViewDetails = () => {
    if (onView) {
      onView(tool);
    }
  };

  const handleEditTool = () => {
    if (onEdit) {
      onEdit(tool);
    }
  };

 

  // Format date
  const formatDate = (timestamp: number) => {
    const time = Number(timestamp);
    return isNaN(time) ? 'N/A' : new Date(time).toLocaleString();
  };
  return (
    <Card
      className="h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
      variant="elevated"
    >
      <div className="p-4">
        <div className="flex flex-col space-y-4">
          {/* Hàng 1: Tên, trạng thái và loại truy cập */}
          <div className="flex items-center gap-3 overflow-hidden">
            {/* Icon tool */}
            <div className="relative w-12 h-12 flex-shrink-0 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <span className="text-xl">🛠️</span>
            </div>

            {/* Thông tin tool: tên, trạng thái và loại truy cập */}
            <div className="flex flex-col min-w-0 flex-grow">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
                <div className="min-w-0">
                  <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                    {tool.name}
                  </h3>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {formatDate(tool.createdAt)}
                  </div>
                </div>
                <div className="flex-shrink-0 mt-1 sm:mt-0 flex gap-2">
               
                </div>
              </div>
            </div>
          </div>

          {/* Hàng 2: Mô tả */}
          <div className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
            {tool.description || t('admin.tool.noDescription', 'No description')}
          </div>

          {/* Hàng 3: Người tạo và các nút chức năng */}
          <div className="flex justify-between items-center">
            <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
              <span>{t('admin.tool.createdBy', 'Created by')}:</span>
              <span className="font-medium">
                {tool.createdBy?.name || t('admin.tool.unknownUser', 'Unknown user')}
              </span>
            </div>

            {/* Các nút chức năng */}
            <div className="flex justify-end space-x-2">
              <Tooltip content={t('common.edit')} position="top">
                <IconCard icon="edit" variant="default" size="sm" onClick={handleEditTool} />
              </Tooltip>
              <Tooltip content={t('common.details')} position="top">
                <IconCard icon="eye" variant="default" size="sm" onClick={handleViewDetails} />
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ToolCard;
