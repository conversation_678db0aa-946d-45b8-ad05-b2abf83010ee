import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  GoogleAdsAccountDto, 
  GoogleAdsAccountQueryDto, 
  CreateGoogleAdsAccountDto,
  UpdateGoogleAdsAccountDto,
  PagingResponseDto,
  GoogleAdsAccountStatus
} from '../../types';
import { NotificationUtil } from '@/shared/utils/notification';
import { useTranslation } from 'react-i18next';

// Mock API response
const mockAccounts: PagingResponseDto<GoogleAdsAccountDto> = {
  items: [
    {
      id: 1,
      userId: 1,
      customerId: '123-456-7890',
      name: '<PERSON><PERSON><PERSON> khoản Google Ads chính',
      status: GoogleAdsAccountStatus.ACTIVE,
      createdAt: Date.now() - 30 * 24 * 60 * 60 * 1000,
      updatedAt: Date.now() - 7 * 24 * 60 * 60 * 1000,
    },
    {
      id: 2,
      userId: 1,
      customerId: '234-567-8901',
      name: '<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>n Google Ads thứ hai',
      status: GoogleAdsAccountStatus.INACTIVE,
      createdAt: Date.now() - 60 * 24 * 60 * 60 * 1000,
      updatedAt: Date.now() - 14 * 24 * 60 * 60 * 1000,
    },
  ],
  meta: {
    totalItems: 2,
    itemCount: 2,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1,
  },
};

/**
 * Hook quản lý tài khoản Google Ads
 */
export const useGoogleAdsAccounts = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const queryClient = useQueryClient();

  /**
   * Lấy danh sách tài khoản Google Ads
   */
  const useAccounts = (params: GoogleAdsAccountQueryDto) => {
    return useQuery<PagingResponseDto<GoogleAdsAccountDto>, Error>({
      queryKey: ['googleAdsAccounts', params],
      queryFn: async () => {
        // TODO: Gọi API thực tế khi có
        // const response = await fetch(`/api/marketing/google-ads/accounts`, {
        //   method: 'GET',
        //   headers: {
        //     'Content-Type': 'application/json',
        //   },
        //   body: JSON.stringify(params),
        // });
        // return response.json();

        // Mock response
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve(mockAccounts);
          }, 500);
        });
      },
    });
  };

  /**
   * Lấy chi tiết tài khoản Google Ads
   */
  const useAccount = (id: number) => {
    return useQuery<GoogleAdsAccountDto, Error>({
      queryKey: ['googleAdsAccount', id],
      queryFn: async () => {
        // TODO: Gọi API thực tế khi có
        // const response = await fetch(`/api/marketing/google-ads/accounts/${id}`);
        // return response.json();

        // Mock response
        return new Promise((resolve) => {
          setTimeout(() => {
            const account = mockAccounts.items.find((acc) => acc.id === id);
            resolve(account as GoogleAdsAccountDto);
          }, 500);
        });
      },
      enabled: !!id,
    });
  };

  /**
   * Tạo tài khoản Google Ads mới
   */
  const useCreateAccount = () => {
    return useMutation<GoogleAdsAccountDto, Error, CreateGoogleAdsAccountDto>({
      mutationFn: async (data) => {
        // TODO: Gọi API thực tế khi có
        // const response = await fetch(`/api/marketing/google-ads/accounts`, {
        //   method: 'POST',
        //   headers: {
        //     'Content-Type': 'application/json',
        //   },
        //   body: JSON.stringify(data),
        // });
        // return response.json();

        // Mock response
        return new Promise((resolve) => {
          setTimeout(() => {
            const newAccount: GoogleAdsAccountDto = {
              id: mockAccounts.items.length + 1,
              userId: 1,
              customerId: data.customerId,
              name: data.name || data.customerId,
              status: GoogleAdsAccountStatus.ACTIVE,
              createdAt: Date.now(),
              updatedAt: Date.now(),
            };
            resolve(newAccount);
          }, 500);
        });
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['googleAdsAccounts'] });
        NotificationUtil.success({
          message: t('marketing:googleAds.accountCreated', 'Tài khoản Google Ads đã được tạo thành công'),
        });
      },
      onError: () => {
        NotificationUtil.error({
          message: t('marketing:googleAds.accountCreateError', 'Lỗi khi tạo tài khoản Google Ads'),
        });
      },
    });
  };

  /**
   * Cập nhật tài khoản Google Ads
   */
  const useUpdateAccount = () => {
    return useMutation<
      GoogleAdsAccountDto,
      Error,
      { id: number; data: UpdateGoogleAdsAccountDto }
    >({
      mutationFn: async ({ id, data }) => {
        // TODO: Gọi API thực tế khi có
        // const response = await fetch(`/api/marketing/google-ads/accounts/${id}`, {
        //   method: 'PUT',
        //   headers: {
        //     'Content-Type': 'application/json',
        //   },
        //   body: JSON.stringify(data),
        // });
        // return response.json();

        // Mock response
        return new Promise((resolve) => {
          setTimeout(() => {
            const account = mockAccounts.items.find((acc) => acc.id === id);
            if (!account) {
              throw new Error('Tài khoản không tồn tại');
            }
            const updatedAccount = {
              ...account,
              ...data,
              updatedAt: Date.now(),
            };
            resolve(updatedAccount);
          }, 500);
        });
      },
      onSuccess: (data) => {
        queryClient.invalidateQueries({ queryKey: ['googleAdsAccounts'] });
        queryClient.invalidateQueries({ queryKey: ['googleAdsAccount', data.id] });
        NotificationUtil.success({
          message: t('marketing:googleAds.accountUpdated', 'Tài khoản Google Ads đã được cập nhật'),
        });
      },
      onError: () => {
        NotificationUtil.error({
          message: t('marketing:googleAds.accountUpdateError', 'Lỗi khi cập nhật tài khoản Google Ads'),
        });
      },
    });
  };

  /**
   * Xóa tài khoản Google Ads
   */
  const useDeleteAccount = () => {
    return useMutation<void, Error, number>({
      mutationFn: async () => {
        // TODO: Gọi API thực tế khi có
        // const response = await fetch(`/api/marketing/google-ads/accounts/${id}`, {
        //   method: 'DELETE',
        // });
        // return response.json();

        // Mock response
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve();
          }, 500);
        });
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['googleAdsAccounts'] });
        NotificationUtil.success({
          message: t('marketing:googleAds.accountDeleted', 'Tài khoản Google Ads đã được xóa'),
        });
      },
      onError: () => {
        NotificationUtil.error({
          message: t('marketing:googleAds.accountDeleteError', 'Lỗi khi xóa tài khoản Google Ads'),
        });
      },
    });
  };

  return {
    useAccounts,
    useAccount,
    useCreateAccount,
    useUpdateAccount,
    useDeleteAccount,
  };
};

export default useGoogleAdsAccounts; 