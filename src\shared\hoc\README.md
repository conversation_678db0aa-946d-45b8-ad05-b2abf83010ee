# Route Guard HOC

Component b<PERSON><PERSON> vệ route dựa trên loại người dùng.

## Cách sử dụng

### 1. Sử dụng trực tiếp trong route

```tsx
import { RouteGuard } from '@/shared/hoc';
import { CustomerList } from '@/modules/customer/pages/CustomerList';

// Trong file routes
const routes = [
  {
    path: '/customers',
    element: <RouteGuard component={CustomerList} type="PROTECT" />
  },
  {
    path: '/admin/customers',
    element: <RouteGuard component={AdminCustomerList} type="ADMIN" />
  },
  {
    path: '/about',
    element: <RouteGuard component={AboutPage} type="PUBLIC" />
  }
];
```

### 2. Sử dụng HOC withRouteGuard

```tsx
import { withRouteGuard } from '@/shared/hoc';
import { CustomerList } from '@/modules/customer/pages/CustomerList';

// Bảo vệ component với quyền người dùng
const ProtectedCustomerList = withRouteGuard(CustomerList, 'PROTECT');

// Bảo vệ component với quyền admin
const AdminProtectedPage = withRouteGuard(AdminPage, 'ADMIN');

// Bảo vệ component với đường dẫn chuyển hướng tùy chỉnh
const ProtectedWithCustomRedirect = withRouteGuard(
  CustomerList,
  'PROTECT',
  '/auth/login?redirect=/customers' // Đường dẫn chuyển hướng tùy chọn
);

// Trong file routes
const routes = [
  {
    path: '/customers',
    element: <ProtectedCustomerList />
  }
];
```

## Các loại bảo vệ route

- `PROTECT`: Yêu cầu người dùng đã đăng nhập
- `ADMIN`: Yêu cầu admin đã đăng nhập
- `PUBLIC`: Không yêu cầu đăng nhập

## Chuyển hướng

Khi người dùng không có quyền truy cập, họ sẽ được chuyển hướng đến:

1. Đường dẫn được chỉ định trong `redirectTo` (nếu có)
2. Nếu không có `redirectTo`:
   - Nếu cần quyền người dùng: `/auth/login`
   - Nếu cần quyền admin: `/admin/auth/login`
