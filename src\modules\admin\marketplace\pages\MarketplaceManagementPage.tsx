import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Trang tổng quan quản lý Marketplace
 */
const MarketplaceManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Products Card */}
        <ModuleCard
          title={t('admin:marketplace.products', 'Sản phẩm')}
          description={t(
            'admin:marketplace.productsDescription',
            'Quản lý các sản phẩm trong marketplace, bao gồm thêm, sử<PERSON>, xóa và phê duyệt sản phẩm.'
          )}
          icon="box"
          count="0"
          countLabel={t('admin:marketplace.totalProducts', 'Tổng số sản phẩm')}
          linkTo="/admin/marketplace/products"
          linkText={t('admin:marketplace.manageProducts', 'Quản lý sản phẩm')}
        />

        {/* Orders Card */}
        <ModuleCard
          title={t('admin:marketplace.orders', 'Đơn hàng')}
          description={t(
            'admin:marketplace.ordersDescription',
            'Quản lý các đơn hàng của khách hàng, theo dõi trạng thái và xử lý đơn hàng.'
          )}
          icon="shopping-cart"
          count="0"
          countLabel={t('admin:marketplace.totalOrders', 'Tổng số đơn hàng')}
          linkTo="/admin/marketplace/orders"
          linkText={t('admin:marketplace.manageOrders', 'Quản lý đơn hàng')}
        />

        {/* Cart Card */}
        <ModuleCard
          title={t('admin:marketplace.cart', 'Giỏ hàng')}
          description={t(
            'admin:marketplace.cartDescription',
            'Xem và quản lý giỏ hàng của khách hàng trong hệ thống.'
          )}
          icon="shopping-bag"
          count="0"
          countLabel={t('admin:marketplace.totalCarts', 'Tổng số giỏ hàng')}
          linkTo="/admin/marketplace/cart"
          linkText={t('admin:marketplace.manageCarts', 'Quản lý giỏ hàng')}
        />
      </ResponsiveGrid>
    </div>
  );
};

export default MarketplaceManagementPage;
