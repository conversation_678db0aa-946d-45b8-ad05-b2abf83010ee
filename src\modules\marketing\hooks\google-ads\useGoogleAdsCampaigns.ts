import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  GoogleAdsCampaignDto, 
  GoogleAdsCampaignQueryDto, 
  CreateGoogleAdsCampaignDto,
  UpdateGoogleAdsCampaignDto,
  PagingResponseDto,
  GoogleAdsCampaignStatus,
  GoogleAdsCampaignType
} from '../../types';
import { NotificationUtil } from '@/shared/utils/notification';
import { useTranslation } from 'react-i18next';

// Mock API response
const mockCampaigns: PagingResponseDto<GoogleAdsCampaignDto> = {
  items: [
    {
      id: 1,
      userId: 1,
      accountId: 1,
      campaignId: '*********',
      name: '<PERSON>ến dịch tìm kiếm sản phẩm mới',
      status: GoogleAdsCampaignStatus.ENABLED,
      type: GoogleAdsCampaignType.SEARCH,
      budget: 500000,
      startDate: '2025-05-01',
      endDate: '2025-06-30',
      createdAt: Date.now() - 10 * 24 * 60 * 60 * 1000,
      updatedAt: Date.now() - 5 * 24 * 60 * 60 * 1000,
    },
    {
      id: 2,
      userId: 1,
      accountId: 1,
      campaignId: '*********',
      name: 'Chiến dịch quảng cáo hiển thị',
      status: GoogleAdsCampaignStatus.PAUSED,
      type: GoogleAdsCampaignType.DISPLAY,
      budget: 300000,
      startDate: '2025-05-15',
      endDate: '2025-07-15',
      createdAt: Date.now() - 15 * 24 * 60 * 60 * 1000,
      updatedAt: Date.now() - 3 * 24 * 60 * 60 * 1000,
    },
    {
      id: 3,
      userId: 1,
      accountId: 2,
      campaignId: '*********',
      name: 'Chiến dịch video YouTube',
      status: GoogleAdsCampaignStatus.ENABLED,
      type: GoogleAdsCampaignType.VIDEO,
      budget: 1000000,
      startDate: '2025-06-01',
      endDate: '2025-08-31',
      createdAt: Date.now() - 20 * 24 * 60 * 60 * 1000,
      updatedAt: Date.now() - 2 * 24 * 60 * 60 * 1000,
    },
  ],
  meta: {
    totalItems: 3,
    itemCount: 3,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1,
  },
};

/**
 * Hook quản lý chiến dịch Google Ads
 */
export const useGoogleAdsCampaigns = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const queryClient = useQueryClient();

  /**
   * Lấy danh sách chiến dịch Google Ads
   */
  const useCampaigns = (params: GoogleAdsCampaignQueryDto) => {
    return useQuery<PagingResponseDto<GoogleAdsCampaignDto>, Error>({
      queryKey: ['googleAdsCampaigns', params],
      queryFn: async () => {
        // TODO: Gọi API thực tế khi có
        // const response = await fetch(`/api/marketing/google-ads/campaigns`, {
        //   method: 'GET',
        //   headers: {
        //     'Content-Type': 'application/json',
        //   },
        //   body: JSON.stringify(params),
        // });
        // return response.json();

        // Mock response
        return new Promise((resolve) => {
          setTimeout(() => {
            // Filter by accountId if provided
            if (params.accountId) {
              const filteredItems = mockCampaigns.items.filter(
                (campaign) => campaign.accountId === params.accountId
              );
              resolve({
                items: filteredItems,
                meta: {
                  ...mockCampaigns.meta,
                  totalItems: filteredItems.length,
                  itemCount: filteredItems.length,
                },
              });
            } else {
              resolve(mockCampaigns);
            }
          }, 500);
        });
      },
    });
  };

  /**
   * Lấy chi tiết chiến dịch Google Ads
   */
  const useCampaign = (id: number) => {
    return useQuery<GoogleAdsCampaignDto, Error>({
      queryKey: ['googleAdsCampaign', id],
      queryFn: async () => {
        // TODO: Gọi API thực tế khi có
        // const response = await fetch(`/api/marketing/google-ads/campaigns/${id}`);
        // return response.json();

        // Mock response
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            const campaign = mockCampaigns.items.find((c) => c.id === id);
            if (campaign) {
              resolve(campaign);
            } else {
              reject(new Error('Chiến dịch không tồn tại'));
            }
          }, 500);
        });
      },
      enabled: !!id,
    });
  };

  /**
   * Tạo chiến dịch Google Ads mới
   */
  const useCreateCampaign = () => {
    return useMutation<GoogleAdsCampaignDto, Error, CreateGoogleAdsCampaignDto>({
      mutationFn: async (data) => {
        // TODO: Gọi API thực tế khi có
        // const response = await fetch(`/api/marketing/google-ads/campaigns`, {
        //   method: 'POST',
        //   headers: {
        //     'Content-Type': 'application/json',
        //   },
        //   body: JSON.stringify(data),
        // });
        // return response.json();

        // Mock response
        return new Promise((resolve) => {
          setTimeout(() => {
            const newCampaign: GoogleAdsCampaignDto = {
              id: mockCampaigns.items.length + 1,
              userId: 1,
              accountId: data.accountId,
              campaignId: `new-${Date.now()}`,
              name: data.name,
              status: GoogleAdsCampaignStatus.ENABLED,
              type: data.type,
              budget: data.budget,
              startDate: data.startDate,
              endDate: data.endDate,
              userCampaignId: data.userCampaignId,
              createdAt: Date.now(),
              updatedAt: Date.now(),
            };
            resolve(newCampaign);
          }, 500);
        });
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['googleAdsCampaigns'] });
        NotificationUtil.success({
          message: t('marketing:googleAds.campaignCreated', 'Chiến dịch Google Ads đã được tạo thành công'),
        });
      },
      onError: () => {
        NotificationUtil.error({
          message: t('marketing:googleAds.campaignCreateError', 'Lỗi khi tạo chiến dịch Google Ads'),
        });
      },
    });
  };

  /**
   * Cập nhật chiến dịch Google Ads
   */
  const useUpdateCampaign = () => {
    return useMutation<
      GoogleAdsCampaignDto,
      Error,
      { id: number; data: UpdateGoogleAdsCampaignDto }
    >({
      mutationFn: async ({ id, data }) => {
        // TODO: Gọi API thực tế khi có
        // const response = await fetch(`/api/marketing/google-ads/campaigns/${id}`, {
        //   method: 'PUT',
        //   headers: {
        //     'Content-Type': 'application/json',
        //   },
        //   body: JSON.stringify(data),
        // });
        // return response.json();

        // Mock response
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            const campaignIndex = mockCampaigns.items.findIndex((c) => c.id === id);
            if (campaignIndex === -1) {
              reject(new Error('Chiến dịch không tồn tại'));
              return;
            }
            
            const updatedCampaign = {
              ...mockCampaigns.items[campaignIndex],
              ...data,
              updatedAt: Date.now(),
            };
            
            resolve(updatedCampaign);
          }, 500);
        });
      },
      onSuccess: (data) => {
        queryClient.invalidateQueries({ queryKey: ['googleAdsCampaigns'] });
        queryClient.invalidateQueries({ queryKey: ['googleAdsCampaign', data.id] });
        NotificationUtil.success({
          message: t('marketing:googleAds.campaignUpdated', 'Chiến dịch Google Ads đã được cập nhật'),
        });
      },
      onError: () => {
        NotificationUtil.error({
          message: t('marketing:googleAds.campaignUpdateError', 'Lỗi khi cập nhật chiến dịch Google Ads'),
        });
      },
    });
  };

  /**
   * Xóa chiến dịch Google Ads
   */
  const useDeleteCampaign = () => {
    return useMutation<void, Error, number>({
      mutationFn: async () => {
        // TODO: Gọi API thực tế khi có
        // const response = await fetch(`/api/marketing/google-ads/campaigns/${id}`, {
        //   method: 'DELETE',
        // });
        // return response.json();

        // Mock response
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve();
          }, 500);
        });
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['googleAdsCampaigns'] });
        NotificationUtil.success({
          message: t('marketing:googleAds.campaignDeleted', 'Chiến dịch Google Ads đã được xóa'),
        });
      },
      onError: () => {
        NotificationUtil.error({
          message: t('marketing:googleAds.campaignDeleteError', 'Lỗi khi xóa chiến dịch Google Ads'),
        });
      },
    });
  };

  /**
   * Cập nhật trạng thái chiến dịch Google Ads
   */
  const useUpdateCampaignStatus = () => {
    return useMutation<
      GoogleAdsCampaignDto,
      Error,
      { id: number; status: GoogleAdsCampaignStatus }
    >({
      mutationFn: async ({ id, status }) => {
        // TODO: Gọi API thực tế khi có
        // const response = await fetch(`/api/marketing/google-ads/campaigns/${id}/status`, {
        //   method: 'PUT',
        //   headers: {
        //     'Content-Type': 'application/json',
        //   },
        //   body: JSON.stringify({ status }),
        // });
        // return response.json();

        // Mock response
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            const campaignIndex = mockCampaigns.items.findIndex((c) => c.id === id);
            if (campaignIndex === -1) {
              reject(new Error('Chiến dịch không tồn tại'));
              return;
            }
            
            const updatedCampaign = {
              ...mockCampaigns.items[campaignIndex],
              status,
              updatedAt: Date.now(),
            };
            
            resolve(updatedCampaign);
          }, 500);
        });
      },
      onSuccess: (data) => {
        queryClient.invalidateQueries({ queryKey: ['googleAdsCampaigns'] });
        queryClient.invalidateQueries({ queryKey: ['googleAdsCampaign', data.id] });
        
        const statusMessage = data.status === GoogleAdsCampaignStatus.ENABLED
          ? t('marketing:googleAds.campaignEnabled', 'Chiến dịch Google Ads đã được kích hoạt')
          : t('marketing:googleAds.campaignPaused', 'Chiến dịch Google Ads đã được tạm dừng');
        
        NotificationUtil.success({
          message: statusMessage,
        });
      },
      onError: () => {
        NotificationUtil.error({
          message: t('marketing:googleAds.campaignStatusUpdateError', 'Lỗi khi cập nhật trạng thái chiến dịch Google Ads'),
        });
      },
    });
  };

  return {
    useCampaigns,
    useCampaign,
    useCreateCampaign,
    useUpdateCampaign,
    useDeleteCampaign,
    useUpdateCampaignStatus,
  };
};

export default useGoogleAdsCampaigns; 