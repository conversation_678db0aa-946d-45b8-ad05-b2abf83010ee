import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Textarea,
  Typography,
} from '@/shared/components/common';
import { audienceSchema } from '../../schemas/audience.schema';
import { AudienceStatus, AudienceType, Audience } from '../../types/audience.types';

interface AudienceFormProps {
  /**
   * Dữ liệu audience ban đầu (nếu là chỉnh sửa)
   */
  initialData?: Audience;

  /**
   * Callback khi submit form
   */
  onSubmit: (values: Record<string, unknown>) => void;

  /**
   * Callback khi hủy
   */
  onCancel: () => void;

  /**
   * Trạng thái loading
   */
  isLoading?: boolean;
}

/**
 * Form tạo/chỉnh sửa audience
 */
const AudienceForm: React.FC<AudienceFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const { t } = useTranslation();

  // <PERSON>ử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    onSubmit(values);
  };

  // Giá trị mặc định cho form
  const defaultValues = initialData
    ? {
        name: initialData.name,
        description: initialData.description,
        type: initialData.type,
        status: initialData.status,
      }
    : {
        status: AudienceStatus.DRAFT,
      };

  return (
    <Card className="mb-4 p-4">
      <Typography variant="h5" className="mb-4">
        {initialData
          ? t('audience.edit', 'Chỉnh sửa đối tượng')
          : t('audience.addNew', 'Thêm đối tượng mới')}
      </Typography>

      <Form
        schema={audienceSchema}
        onSubmit={handleSubmit}
        className="space-y-4"
        defaultValues={defaultValues}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="name" label={t('audience.name', 'Tên đối tượng')} required>
            <Input placeholder={t('audience.name.placeholder', 'Nhập tên đối tượng')} fullWidth />
          </FormItem>

          <FormItem name="type" label={t('audience.type', 'Loại đối tượng')} required>
            <Select
              options={[
                { value: AudienceType.CUSTOMER, label: t('audience.types.customer', 'Khách hàng') },
                { value: AudienceType.LEAD, label: t('audience.types.lead', 'Tiềm năng') },
                {
                  value: AudienceType.SUBSCRIBER,
                  label: t('audience.types.subscriber', 'Người đăng ký'),
                },
                { value: AudienceType.CUSTOM, label: t('audience.types.custom', 'Tùy chỉnh') },
              ]}
              placeholder={t('audience.type.placeholder', 'Chọn loại đối tượng')}
              fullWidth
            />
          </FormItem>
        </div>

        <FormItem name="description" label={t('audience.description', 'Mô tả')}>
          <Textarea
            placeholder={t('audience.description.placeholder', 'Nhập mô tả')}
            rows={3}
            fullWidth
          />
        </FormItem>

        <FormItem name="status" label={t('audience.status', 'Trạng thái')} required>
          <Select
            options={[
              { value: AudienceStatus.ACTIVE, label: t('audience.statuses.active', 'Hoạt động') },
              {
                value: AudienceStatus.INACTIVE,
                label: t('audience.statuses.inactive', 'Không hoạt động'),
              },
              { value: AudienceStatus.DRAFT, label: t('audience.statuses.draft', 'Bản nháp') },
            ]}
            placeholder={t('audience.status.placeholder', 'Chọn trạng thái')}
            fullWidth
          />
        </FormItem>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button type="submit" isLoading={isLoading}>
            {initialData ? t('common.update', 'Cập nhật') : t('common.create', 'Tạo mới')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default AudienceForm;
