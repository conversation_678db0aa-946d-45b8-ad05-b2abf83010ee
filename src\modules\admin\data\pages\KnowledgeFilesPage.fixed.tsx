import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Table, ConfirmDeleteModal } from '@/shared/components/common';
import { SortOrder, TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ModernMenuTrigger } from '@/shared/components/common/ModernMenu';
import { NotificationUtil } from '@/shared/utils/notification';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';

import {
  useKnowledgeFiles,
  useCreateKnowledgeFiles,
  useDeleteKnowledgeFile,
} from '@/modules/admin/data/knowledge-files/hooks';
import {
  KnowledgeFileDto,
  KnowledgeFileQueryParams,
  CreateKnowledgeFileDto,
} from '@/modules/admin/data/knowledge-files/types';
import { formatDate } from '@/shared/utils/format';

// Hàm formatBytes tạm thời
const formatBytes = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Form tạo file tri thức
 */
const KnowledgeFileForm: React.FC<{
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  isLoading?: boolean;
  readOnly?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  initialValues?: any; // Sử dụng any để tránh lỗi type
}> = ({ onSubmit, onCancel, isLoading, readOnly, initialValues }) => {
  const { t } = useTranslation();
  const [files, setFiles] = useState<File[]>([]);
  // Không cần state formValues vì chỉ cần xử lý files

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFiles(Array.from(e.target.files));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (files.length === 0 && !readOnly) {
      NotificationUtil.info({
        message: t('admin.data.knowledgeFiles.selectFilesToUpload', 'Please select at least one file to upload'),
        duration: 3000,
      });
      return;
    }

    // Create an array of file data
    const fileData: CreateKnowledgeFileDto[] = files.map(file => ({
      name: file.name,
      mime: file.type,
      storage: file.size > 0 ? file.size : 1, // Ensure storage is never 0
    }));

    // Pass the array of files to the onSubmit handler
    onSubmit({ files: fileData });
  };

  return (
    <div className="p-6 bg-white dark:bg-gray-800">
      <form onSubmit={handleSubmit} className="space-y-6">
        <Typography variant="h5" className="mb-4 text-primary-600 dark:text-primary-400">
          {readOnly
            ? t('admin.data.knowledgeFiles.viewFile', 'View Knowledge File')
            : t('admin.data.knowledgeFiles.uploadFiles', 'Upload Knowledge Files')}
        </Typography>

        {!readOnly && (
          <div className="space-y-4">
            <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-primary-500 dark:hover:border-primary-400 transition-colors">
              <label className="block cursor-pointer">
                <div className="flex flex-col items-center justify-center space-y-2">
                  <svg
                    className="w-12 h-12 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    />
                  </svg>
                  <Typography variant="body1" className="font-medium">
                    {t('admin.data.knowledgeFiles.dragAndDrop', 'Drag and drop files here, or click to select files')}
                  </Typography>
                  <Typography variant="caption" className="text-gray-500 dark:text-gray-400">
                    {t(
                      'admin.data.knowledgeFiles.supportedFormats',
                      'Supported formats: PDF, DOCX, TXT, CSV, JSON'
                    )}
                  </Typography>
                </div>
                <input
                  type="file"
                  multiple
                  onChange={handleFileChange}
                  className="hidden"
                  disabled={readOnly || isLoading}
                  accept=".pdf,.docx,.txt,.csv,.json,.doc,.xlsx,.xls"
                />
              </label>
            </div>

            {files.length > 0 && (
              <div className="mt-4">
                <Typography variant="body2" className="font-medium mb-2">
                  {t('admin.data.knowledgeFiles.selectedFiles', 'Selected Files')} ({files.length})
                </Typography>
                <div className="space-y-2 max-h-60 overflow-y-auto p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  {files.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600">
                      <div className="flex items-center space-x-3">
                        <svg className="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <div className="truncate max-w-xs">
                          <Typography variant="body2" className="font-medium truncate">{file.name}</Typography>
                          <Typography variant="caption" className="text-gray-500">{formatBytes(file.size)}</Typography>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          setFiles(files.filter((_, i) => i !== index));
                        }}
                        className="text-red-500 hover:text-red-700"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {readOnly && initialValues && (
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Typography variant="body2" className="font-semibold text-gray-600 dark:text-gray-300">
                  {t('admin.data.knowledgeFiles.fileName', 'File name')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">{initialValues.name as string}</Typography>
              </div>
              <div>
                <Typography variant="body2" className="font-semibold text-gray-600 dark:text-gray-300">
                  {t('admin.data.knowledgeFiles.fileSize', 'Size')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">{formatBytes(initialValues.storage as number)}</Typography>
              </div>
              <div>
                <Typography variant="body2" className="font-semibold text-gray-600 dark:text-gray-300">
                  {t('admin.data.knowledgeFiles.fileType', 'File type')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">{initialValues.extension as string}</Typography>
              </div>
              <div>
                <Typography variant="body2" className="font-semibold text-gray-600 dark:text-gray-300">
                  {t('admin.data.knowledgeFiles.vectorStore', 'Vector Store')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">
                  {(initialValues.vectorStoreName as string) ||
                    t('admin.data.knowledgeFiles.noVectorStore', 'Not assigned to any vector store')}
                </Typography>
              </div>
              <div>
                <Typography variant="body2" className="font-semibold text-gray-600 dark:text-gray-300">
                  {t('admin.data.knowledgeFiles.createdAt', 'Created at')}
                </Typography>
                <Typography className="text-gray-800 dark:text-gray-100">{formatDate(initialValues.createdAt as number)}</Typography>
              </div>
            </div>

            {initialValues && initialValues.viewUrl && (
              <div className="mt-4">
                <a
                  href={initialValues.viewUrl as string}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  {t('admin.data.knowledgeFiles.viewFile', 'View file')}
                </a>
              </div>
            )}
          </div>
        )}

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700 mt-6">
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            {t('common.cancel', 'Cancel')}
          </Button>
          {!readOnly && (
            <Button type="submit" variant="primary" disabled={isLoading || files.length === 0} isLoading={isLoading}>
              {isLoading
                ? t('admin.data.common.uploading', 'Uploading...')
                : t('admin.data.common.upload', 'Upload')}
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};

/**
 * Trang quản lý file tri thức
 */
const KnowledgeFilesPage: React.FC = () => {
  const { t } = useTranslation();
  const [files, setFiles] = useState<KnowledgeFileDto[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [totalItems, setTotalItems] = useState(0);
  const [fileToDelete, setFileToDelete] = useState<KnowledgeFileDto | null>(null);
  const [fileToView, setFileToView] = useState<KnowledgeFileDto | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // State cho chọn nhiều file để xóa
  const [selectedFileIds, setSelectedFileIds] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isViewFormVisible,
    showForm: showViewSlideForm,
    hideForm: hideViewForm,
  } = useSlideForm();

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((file: KnowledgeFileDto) => {
    setFileToDelete(file);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hiển thị form xem chi tiết file
  const handleShowViewForm = useCallback(
    (file: KnowledgeFileDto) => {
      setFileToView(file);
      showViewSlideForm();
    },
    [showViewSlideForm]
  );

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<KnowledgeFileDto>[]>(() => {
    const allColumns: TableColumn<KnowledgeFileDto>[] = [
      {
        key: 'name',
        title: t('data.knowledgeFiles.table.name', 'File name'),
        dataIndex: 'name',
        width: '25%',
        sortable: true,
      },
      {
        key: 'extension',
        title: t('data.knowledgeFiles.table.type', 'Type'),
        dataIndex: 'extension',
        width: '10%',
        sortable: true,
        render: (value: unknown) => (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {value ? String(value) : '-'}
          </span>
        ),
      },
      {
        key: 'storage',
        title: t('data.knowledgeFiles.table.size', 'Size'),
        dataIndex: 'storage',
        width: '15%',
        sortable: true,
        render: (value: unknown) => <span>{formatBytes(value as number)}</span>,
      },
      {
        key: 'vectorStoreName',
        title: t('data.knowledgeFiles.table.vectorStore', 'Vector Store'),
        dataIndex: 'vectorStoreName',
        width: '20%',
        render: (value: unknown) => <span>{value ? String(value) : '-'}</span>,
      },
      {
        key: 'createdAt',
        title: t('data.knowledgeFiles.table.createdAt', 'Created at'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => <span>{formatDate(value as number)}</span>,
      },
      {
        key: '',
        title: t('', ''),
        render: (_: unknown, record: KnowledgeFileDto) => {
          const menuItems = [
            {
              label: t('common.view', 'View'),
              icon: 'eye',
              onClick: () => handleShowViewForm(record),
            },
            {
              label: t('common.delete', 'Delete'),
              icon: 'trash',
              onClick: () => handleShowDeleteConfirm(record),
              variant: 'primary' as const,
            },
          ];

          return <ModernMenuTrigger items={menuItems} placement="left" />;
        },
      },
    ];

    return allColumns;
  }, [t, handleShowDeleteConfirm, handleShowViewForm]);

  // Sử dụng hook useDataTable để quản lý dữ liệu bảng
  const dataTableConfig = useDataTableConfig<KnowledgeFileDto, KnowledgeFileQueryParams>({
    columns,
    filterOptions: [],
    showDateFilter: false,
    createQueryParams: (params) => ({
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection as 'ASC' | 'DESC' | undefined,
    }),
  });

  const dataTable = useDataTable(dataTableConfig);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Tạo query params cho API
  const queryParams = useMemo<KnowledgeFileQueryParams>(() => {
    const params: KnowledgeFileQueryParams = {
      page: dataTable.tableData.currentPage,
      limit: dataTable.tableData.pageSize,
      search: dataTable.tableData.searchTerm || undefined,
      sortBy: dataTable.tableData.sortBy || undefined,
      sortDirection: dataTable.tableData.sortDirection as 'ASC' | 'DESC' | undefined,
    };

    return params;
  }, [
    dataTable.tableData.currentPage,
    dataTable.tableData.pageSize,
    dataTable.tableData.searchTerm,
    dataTable.tableData.sortBy,
    dataTable.tableData.sortDirection,
  ]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearSort,
    handleClearAll,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {},
    t,
  });

  // Hooks để gọi API
  const {
    data: filesData,
    isLoading: isLoadingFiles,
    error: filesError,
  } = useKnowledgeFiles(queryParams);

  const { mutateAsync: createFiles } = useCreateKnowledgeFiles();
  const { deleteFiles } = useDeleteKnowledgeFile();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (filesData) {
      setFiles(filesData.items);
      setTotalItems(filesData.meta.totalItems);
    }

    setIsLoading(isLoadingFiles);
  }, [filesData, filesError, isLoadingFiles]);

  // Xử lý thay đổi trang - sử dụng dataTable
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      dataTable.tableData.handlePageChange(page, newPageSize);
    },
    [dataTable.tableData]
  );

  // Xử lý tìm kiếm - sử dụng dataTable
  const handleSearch = useCallback((term: string) => {
    dataTable.tableData.handleSearch(term);
  }, [dataTable.tableData]);

  // Xử lý thay đổi sắp xếp - sử dụng dataTable
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    dataTable.tableData.handleSortChange(column, order);
  }, [dataTable.tableData]);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setFileToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!fileToDelete) return;

    try {
      await deleteFiles(fileToDelete.id);
      setShowDeleteConfirm(false);
      setFileToDelete(null);

      NotificationUtil.success({
        message: t('admin.data.knowledgeFiles.deleteSuccess', 'File deleted successfully'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting file:', error);
      NotificationUtil.error({
        message: t('admin.data.knowledgeFiles.deleteError', 'Error deleting file'),
        duration: 3000,
      });
    }
  }, [fileToDelete, deleteFiles, t]);

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedFileIds.length === 0) {
      NotificationUtil.info({
        message: t('admin.data.knowledgeFiles.selectFilesToDelete', 'Please select at least one file to delete'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedFileIds, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedFileIds.length === 0) return;

    try {
      // Xóa tất cả các file đã chọn cùng một lúc
      await deleteFiles(selectedFileIds as string[]);
      setShowBulkDeleteConfirm(false);
      setSelectedFileIds([]);

      NotificationUtil.success({
        message: t('admin.data.knowledgeFiles.bulkDeleteSuccess', 'Selected files deleted successfully'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting files:', error);
      NotificationUtil.error({
        message: t('admin.data.knowledgeFiles.bulkDeleteError', 'Error deleting selected files'),
        duration: 3000,
      });
    }
  }, [selectedFileIds, deleteFiles, t]);

  // Xử lý thay đổi lựa chọn
  const handleSelectionChange = useCallback((selectedRowKeys: React.Key[]) => {
    setSelectedFileIds(selectedRowKeys);
  }, []);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý đóng form xem chi tiết file
  const handleCloseViewForm = useCallback(() => {
    hideViewForm();
    // Đặt timeout để đảm bảo animation đóng hoàn tất trước khi xóa dữ liệu
    setTimeout(() => {
      setFileToView(null);
    }, 300);
  }, [hideViewForm]);

  // Xử lý submit form tạo file
  const handleSubmitCreateFile = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        setIsUploading(true);

        // Chuẩn bị dữ liệu cho API
        const fileData = values.files as CreateKnowledgeFileDto[];

        // Đảm bảo fileData là một mảng
        if (!Array.isArray(fileData)) {
          console.error('File data không phải là mảng:', fileData);
          throw new Error('File data phải là một mảng');
        }

        // Gọi API tạo file - đảm bảo gửi đúng định dạng { files: [...] }
        await createFiles({ files: fileData });
        hideForm();

        NotificationUtil.success({
          message: t('admin.data.knowledgeFiles.uploadSuccess', 'Files uploaded successfully'),
          duration: 3000,
        });
      } catch (error) {
        console.error('Lỗi khi tạo files:', error);
        NotificationUtil.error({
          message: t('admin.data.knowledgeFiles.uploadError', 'Error uploading files'),
          duration: 3000,
        });
      } finally {
        setIsUploading(false);
      }
    },
    [createFiles, hideForm, t]
  );

  // Lọc các cột hiển thị
  const filteredColumns = useMemo<TableColumn<KnowledgeFileDto>[]>(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: t('common.all', 'All'), visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns, t]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          {/* Thêm MenuIconBar */}
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showForm()}
            items={[
              {
                id: 'all',
                label: t('common.all', 'All'),
                icon: 'list',
                onClick: () => '',
              },
              {
                id: 'bulkDelete',
                label: t('common.bulkDelete', 'Batch Delete'),
                icon: 'trash',
                onClick: handleShowBulkDeleteConfirm,
                disabled: selectedFileIds.length === 0,
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
          />

          {/* Hiển thị ActiveFilters */}
          <ActiveFilters
            searchTerm={dataTable.tableData.searchTerm}
            onClearSearch={handleClearSearch}
            sortBy={dataTable.tableData.sortBy}
            sortDirection={dataTable.tableData.sortDirection as SortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        {/* SlideInForm cho form thêm mới */}
        <SlideInForm isVisible={isVisible}>
          <KnowledgeFileForm
            onSubmit={handleSubmitCreateFile}
            onCancel={hideForm}
            isLoading={isUploading}
          />
        </SlideInForm>

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {fileToView && (
            <KnowledgeFileForm
              initialValues={fileToView}
              onSubmit={() => {}} // Không cần xử lý submit vì chỉ xem
              onCancel={handleCloseViewForm}
              readOnly={true} // Chế độ chỉ đọc
            />
          )}
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<KnowledgeFileDto>
            columns={filteredColumns}
            data={files}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={handleSortChange}
            defaultSort={{
              column: dataTable.tableData.sortBy || '',
              order: dataTable.tableData.sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: dataTable.tableData.currentPage,
              pageSize: dataTable.tableData.pageSize,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
            rowSelection={{
              selectedRowKeys: selectedFileIds,
              onChange: handleSelectionChange,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin.data.common.confirmDelete', 'Confirm Delete')}
        message={t(
          'admin.data.knowledgeFiles.confirmDeleteMessage',
          'Are you sure you want to delete this knowledge file?'
        )}
        itemName={fileToDelete?.name}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('admin.data.common.confirmBatchDelete', 'Confirm Batch Delete')}
        message={t(
          'admin.data.knowledgeFiles.confirmBatchDeleteMessage',
          'Are you sure you want to delete {{count}} selected knowledge files?',
          { count: selectedFileIds.length }
        )}
      />
    </div>
  );
};

export default KnowledgeFilesPage;