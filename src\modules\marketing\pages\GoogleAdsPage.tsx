import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Card, 
  Typography, 
  Icon, 
  Table, 
  Button, 
  ActionMenu, 
  ActionMenuItem 
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { useGoogleAdsAccounts } from '../hooks';
import { GoogleAdsAccountDto, GoogleAdsAccountStatus } from '../types';

/**
 * Trang tích hợp & quản lý Google Ads
 */
const GoogleAdsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isAddFormVisible, setIsAddFormVisible] = useState(false);

  // Sử dụng hook quản lý tài khoản Google Ads
  const { useAccounts } = useGoogleAdsAccounts();
  const { data: accountsData, isLoading } = useAccounts({
    page: currentPage,
    limit: pageSize,
    search: searchTerm || undefined,
  });

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  // Xử lý xóa tìm kiếm
  const handleClearSearch = () => {
    setSearchTerm('');
    setCurrentPage(1);
  };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number, newPageSize: number) => {
    setCurrentPage(page);
    if (newPageSize !== pageSize) {
      setPageSize(newPageSize);
      setCurrentPage(1);
    }
  };

  // Xử lý thêm mới
  const handleAdd = () => {
    setIsAddFormVisible(true);
  };

  // Xử lý đóng form
  const handleCloseForm = () => {
    setIsAddFormVisible(false);
  };

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<GoogleAdsAccountDto>[]>(
    () => [
      {
        key: 'id',
        title: t('common:id', 'ID'),
        dataIndex: 'id',
        width: '10%',
      },
      {
        key: 'name',
        title: t('marketing:googleAds.name', 'Tên tài khoản'),
        dataIndex: 'name',
        width: '25%',
      },
      {
        key: 'customerId',
        title: t('marketing:googleAds.customerId', 'Customer ID'),
        dataIndex: 'customerId',
        width: '20%',
      },
      {
        key: 'status',
        title: t('common:status', 'Trạng thái'),
        dataIndex: 'status',
        width: '15%',
        render: (value: unknown) => {
          const status = value as GoogleAdsAccountStatus;
          let statusClass = '';
          let statusText = '';

          switch (status) {
            case GoogleAdsAccountStatus.ACTIVE:
              statusClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
              statusText = t('common:active', 'Hoạt động');
              break;
            case GoogleAdsAccountStatus.INACTIVE:
              statusClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
              statusText = t('common:inactive', 'Không hoạt động');
              break;
            case GoogleAdsAccountStatus.PENDING:
              statusClass = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
              statusText = t('common:pending', 'Đang chờ');
              break;
            case GoogleAdsAccountStatus.ERROR:
              statusClass = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
              statusText = t('common:error', 'Lỗi');
              break;
            default:
              statusClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
              statusText = t('common:unknown', 'Không xác định');
          }

          return (
            <div className={`px-2 py-1 rounded-full text-center text-xs font-medium ${statusClass}`}>
              {statusText}
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('common:createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        render: (value: unknown) => {
          const timestamp = value as number;
          return new Date(timestamp).toLocaleDateString();
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '15%',
        render: (_: unknown, record: GoogleAdsAccountDto) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view', 'Xem'),
              icon: 'eye',
              onClick: () => console.log('View', record.id),
            },
            {
              id: 'edit',
              label: t('common:edit', 'Sửa'),
              icon: 'edit',
              onClick: () => console.log('Edit', record.id),
            },
            {
              id: 'delete',
              label: t('common:delete', 'Xóa'),
              icon: 'trash',
              onClick: () => console.log('Delete', record.id),
            },
            {
              id: 'campaigns',
              label: t('marketing:googleAds.viewCampaigns', 'Xem chiến dịch'),
              icon: 'list',
              onClick: () => console.log('View campaigns', record.id),
            },
          ];

          return (
            <div className="flex justify-center">
              <ActionMenu
                items={actionItems}
                menuTooltip={t('common:moreActions', 'Thêm hành động')}
                iconSize="sm"
                iconVariant="default"
                placement="bottom"
                menuWidth="180px"
                menuIcon="more-horizontal"
                showAllInMenu={true}
                preferRight={true}
                preferTop={true}
              />
            </div>
          );
        },
      },
    ],
    [t]
  );

  return (
    <div className="space-y-4">
      {/* Header và các công cụ */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Icon name="google" size="lg" className="text-primary mr-2" />
          <Typography variant="h4">
            {t('marketing:googleAds.title', 'Google Ads')}
          </Typography>
        </div>
        <Button variant="primary" onClick={handleAdd}>
          {t('marketing:googleAds.connectAccount', 'Kết nối tài khoản')}
        </Button>
      </div>

      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={handleSearch}
        items={[
          {
            id: 'all',
            label: t('common:all', 'Tất cả'),
            icon: 'list',
            onClick: () => console.log('All'),
          },
          {
            id: 'active',
            label: t('common:active', 'Hoạt động'),
            icon: 'check',
            onClick: () => console.log('Active'),
          },
          {
            id: 'inactive',
            label: t('common:inactive', 'Không hoạt động'),
            icon: 'eye-off',
            onClick: () => console.log('Inactive'),
          },
        ]}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* ActiveFilters */}
      {searchTerm && (
        <ActiveFilters
          searchTerm={searchTerm}
          onClearSearch={handleClearSearch}
          onClearAll={handleClearSearch}
        />
      )}

      {/* SlideInForm cho form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <div className="p-6">
          <Typography variant="h5" className="mb-4">
            {t('marketing:googleAds.connectAccount', 'Kết nối tài khoản Google Ads')}
          </Typography>
          <Typography variant="body1" className="mb-6">
            {t(
              'marketing:googleAds.connectAccountDescription',
              'Form kết nối tài khoản sẽ được phát triển ở giai đoạn tiếp theo.'
            )}
          </Typography>
          <div className="flex justify-end">
            <Button variant="secondary" onClick={handleCloseForm}>
              {t('common:cancel', 'Hủy')}
            </Button>
          </div>
        </div>
      </SlideInForm>

      {/* Bảng danh sách tài khoản */}
      <Card className="overflow-hidden">
        <Table
          columns={columns}
          data={accountsData?.items || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: accountsData?.meta?.totalItems || 0,
            onChange: handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default GoogleAdsPage; 