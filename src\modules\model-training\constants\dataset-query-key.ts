import { QueryDatasetDto } from '../types/dataset.types';

/**
 * Query keys cho dataset
 */
export const DATASET_QUERY_KEYS = {
  all: ['datasets'] as const,
  lists: () => [...DATASET_QUERY_KEYS.all, 'list'] as const,
  list: (filters: QueryDatasetDto) => [...DATASET_QUERY_KEYS.lists(), filters] as const,
  details: () => [...DATASET_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...DATASET_QUERY_KEYS.details(), id] as const,
};
