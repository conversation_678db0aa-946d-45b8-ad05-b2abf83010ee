import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from '@/shared/components/common';
import useNotification from '@/shared/hooks/common/useNotification';
import { Clipboard, Download, Check, Save } from 'lucide-react';
import '../styles/scrollbar.css';
import styles from '../styles/CodeViewer.module.css';
import { saveHTMLFile } from '../utils/export-utils';

// Kích thước icon
const iconSize = 16;

interface CodeViewerProps {
  open: boolean;
  onClose: () => void;
  htmlCode: string;
  cssCode: string;
  jsonCode?: string;
}

const CodeViewer: React.FC<CodeViewerProps> = ({
  open,
  onClose,
  htmlCode,
  cssCode,
  jsonCode
}) => {
  const { addNotification } = useNotification();
  const [activeTab, setActiveTab] = useState<'html' | 'css' | 'json'>('html');
  const [copied, setCopied] = useState(false);

  // Xử lý sao chép mã
  const handleCopyCode = () => {
    let codeToCopy = '';

    switch (activeTab) {
      case 'html':
        codeToCopy = htmlCode;
        break;
      case 'css':
        codeToCopy = cssCode;
        break;
      case 'json':
        codeToCopy = jsonCode || '';
        break;
    }

    navigator.clipboard.writeText(codeToCopy).then(() => {
      setCopied(true);
      addNotification('success', `Mã ${activeTab.toUpperCase()} đã được sao chép vào clipboard.`, 2000);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  // Xử lý khi download code
  const handleDownload = async () => {
    try {
      switch (activeTab) {
        case 'html':
          if (activeTab === 'html' && cssCode) {
            // Nếu đang ở tab HTML và có CSS, lưu file HTML với CSS nhúng
            await saveHTMLFile(htmlCode, cssCode, 'email-template');
            addNotification('success', 'Đã tải xuống file HTML với CSS nhúng.', 3000);
            return;
          }

          // Nếu không có CSS, lưu file HTML thông thường
          {
            const htmlBlob = new Blob([htmlCode], { type: 'text/html' });
            const htmlUrl = URL.createObjectURL(htmlBlob);
            const htmlLink = document.createElement('a');
            htmlLink.href = htmlUrl;
            htmlLink.download = 'email-template.html';
            document.body.appendChild(htmlLink);
            htmlLink.click();
            document.body.removeChild(htmlLink);
            URL.revokeObjectURL(htmlUrl);
          }
          break;

        case 'css':
          {
            const cssBlob = new Blob([cssCode], { type: 'text/css' });
            const cssUrl = URL.createObjectURL(cssBlob);
            const cssLink = document.createElement('a');
            cssLink.href = cssUrl;
            cssLink.download = 'email-template.css';
            document.body.appendChild(cssLink);
            cssLink.click();
            document.body.removeChild(cssLink);
            URL.revokeObjectURL(cssUrl);
          }
          break;

        case 'json':
          if (jsonCode) {
            // Bọc trong block để tránh lỗi ESLint "no-case-declarations"
            {
              const jsonBlob = new Blob([jsonCode], { type: 'application/json' });
              const jsonUrl = URL.createObjectURL(jsonBlob);
              const jsonLink = document.createElement('a');
              jsonLink.href = jsonUrl;
              jsonLink.download = 'email-template.json';
              document.body.appendChild(jsonLink);
              jsonLink.click();
              document.body.removeChild(jsonLink);
              URL.revokeObjectURL(jsonUrl);
            }
          }
          break;
      }

      addNotification('success', `Đã tải xuống file ${activeTab.toUpperCase()}.`, 3000);
    } catch (error) {
      console.error('Lỗi khi tải xuống file:', error);
      addNotification('error', 'Có lỗi xảy ra khi tải xuống file.', 3000);
    }
  };

  return (
    <div className={styles.codeViewerModal}>
      <Modal
        title="Mã nguồn email"
        isOpen={open}
        onClose={onClose}
        size="xl"
      footer={
        <div className="flex justify-end">
          <Button variant="outline" onClick={onClose}>
            Đóng
          </Button>
        </div>
      }
    >
      <div className="h-[75vh] flex flex-col w-full">
        <div className="flex flex-col flex-1 overflow-hidden w-full" style={{ height: 'calc(75vh - 120px)' }}>
          <div className="flex justify-between items-center border-b border-gray-200 mb-2">
            <div className="flex">
              <div
                className={`px-4 py-2 cursor-pointer ${activeTab === 'html' ? 'border-b-2 border-blue-500 text-blue-500' : ''}`}
                onClick={() => setActiveTab('html')}
              >
                HTML
              </div>
              <div
                className={`px-4 py-2 cursor-pointer ${activeTab === 'css' ? 'border-b-2 border-blue-500 text-blue-500' : ''}`}
                onClick={() => setActiveTab('css')}
              >
                CSS
              </div>
              {jsonCode && (
                <div
                  className={`px-4 py-2 cursor-pointer ${activeTab === 'json' ? 'border-b-2 border-blue-500 text-blue-500' : ''}`}
                  onClick={() => setActiveTab('json')}
                >
                  JSON
                </div>
              )}
            </div>

            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleCopyCode}>
                <span className="mr-2">{copied ? <Check size={iconSize} /> : <Clipboard size={iconSize} />}</span>
                {copied ? 'Đã sao chép' : 'Sao chép'}
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <span className="mr-2"><Download size={iconSize} /></span>
                Tải xuống
              </Button>
              {activeTab === 'html' && cssCode && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => saveHTMLFile(htmlCode, cssCode, 'email-template')}
                >
                  <span className="mr-2"><Save size={iconSize} /></span>
                  Lưu HTML+CSS
                </Button>
              )}
            </div>
          </div>

          {activeTab === 'html' && (
            <div className="flex-1 mt-2" style={{ height: 'calc(100% - 50px)' }}>
              <div className={styles.codeContainer}>
                <div className={`${styles.codeScroller} custom-scrollbar`}>
                  <code className={styles.codeBlock}>{htmlCode}</code>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'css' && (
            <div className="flex-1 mt-2" style={{ height: 'calc(100% - 50px)' }}>
              <div className={styles.codeContainer}>
                <div className={`${styles.codeScroller} custom-scrollbar`}>
                  <code className={styles.codeBlock}>{cssCode}</code>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'json' && jsonCode && (
            <div className="flex-1 mt-2" style={{ height: 'calc(100% - 50px)' }}>
              <div className={styles.codeContainer}>
                <div className={`${styles.codeScroller} custom-scrollbar`}>
                  <code className={styles.codeBlock}>{jsonCode}</code>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Modal>
    </div>
  );
};

export default CodeViewer;
