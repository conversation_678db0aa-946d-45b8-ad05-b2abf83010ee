import { apiClient } from '@/shared/api/axios';
import {
  ToolGroupListItem,
  ToolGroupDetail,
  ToolGroupQueryParams,
  PaginatedResult,
  CreateToolGroupParams,
  UpdateToolGroupParams,
  UpdateToolGroupToolsParams,
} from '../types/tool.types';

/**
 * Service để tương tác với API nhóm tool của admin
 */
export class AdminToolGroupService {
  private baseUrl = `/admin/tools/groups`;

  /**
   * Lấy danh sách nhóm tool
   * @param params Tham số truy vấn
   * @returns Danh sách nhóm tool với phân trang
   */
  async getToolGroups(params?: ToolGroupQueryParams): Promise<PaginatedResult<ToolGroupListItem>> {
    try {
      const response = await apiClient.get<PaginatedResult<ToolGroupListItem>>(this.baseUrl, {
        params,
        tokenType: 'admin'
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching admin tool groups:', error);
      throw error;
    }
  }

  /**
   * L<PERSON>y thông tin chi tiết nhóm tool
   * @param id ID của nhóm tool
   * @returns Thông tin chi tiết nhóm tool
   */
  async getToolGroupById(id: number): Promise<ToolGroupDetail> {
    try {
      const response = await apiClient.get<ToolGroupDetail>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin'
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching admin tool group with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Tạo nhóm tool mới
   * @param data Dữ liệu tạo nhóm tool
   * @returns ID của nhóm tool đã tạo
   */
  async createToolGroup(data: CreateToolGroupParams): Promise<number> {
    try {
      const response = await apiClient.post<{ id: number }>(this.baseUrl, data, {
        tokenType: 'admin'
      });
      return response.result.id;
    } catch (error) {
      console.error('Error creating admin tool group:', error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin nhóm tool
   * @param id ID của nhóm tool
   * @param data Dữ liệu cập nhật
   * @returns Kết quả cập nhật
   */
  async updateToolGroup(id: number, data: UpdateToolGroupParams): Promise<boolean> {
    try {
      const response = await apiClient.put<{ success: boolean }>(`${this.baseUrl}/${id}`, data, {
        tokenType: 'admin'
      });
      return response.result.success;
    } catch (error) {
      console.error(`Error updating admin tool group with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa nhóm tool
   * @param id ID của nhóm tool
   * @returns Kết quả xóa
   */
  async deleteToolGroup(id: number): Promise<boolean> {
    try {
      const response = await apiClient.delete<{ success: boolean }>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin'
      });
      return response.result.success;
    } catch (error) {
      console.error(`Error deleting admin tool group with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Cập nhật danh sách tool trong nhóm
   * @param id ID của nhóm tool
   * @param data Dữ liệu cập nhật
   * @returns Kết quả cập nhật
   */
  async updateToolGroupTools(id: number, data: UpdateToolGroupToolsParams): Promise<boolean> {
    try {
      const response = await apiClient.put<{ success: boolean }>(
        `${this.baseUrl}/${id}/tools`,
        data,
        { tokenType: 'admin' }
      );
      return response.result.success;
    } catch (error) {
      console.error(`Error updating tools in group with ID ${id}:`, error);
      throw error;
    }
  }
}
