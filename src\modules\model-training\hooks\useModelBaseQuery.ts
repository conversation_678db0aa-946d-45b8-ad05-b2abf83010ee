import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getModelBases,
  getModelBaseById,
  createModelBase,
  updateModelBase,
  deleteModelBase,
} from '../services/model-base.service';
import { ModelBaseQueryKeys } from '../constants/model-base-query-key';
import {
  CreateModelBaseDto,
  ModelBaseQueryParams,
} from '../types/model-base.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

/**
 * Interface cho lỗi API
 */
interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
  message?: string;
}

/**
 * Hook để lấy danh sách model base
 */
export const useModelBases = (params: ModelBaseQueryParams = {}) => {
  return useQuery({
    queryKey: ModelBaseQueryKeys.list(params),
    queryFn: () => getModelBases(params),
  });
};

/**
 * Hook để lấy thông tin chi tiết của model base
 */
export const useModelBaseDetail = (id: string) => {
  return useQuery({
    queryKey: ModelBaseQueryKeys.detail(id),
    queryFn: () => getModelBaseById(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo model base mới
 */
export const useCreateModelBase = () => {
  const queryClient = useQueryClient();
  const notification = useSmartNotification();

  return useMutation({
    mutationFn: (data: CreateModelBaseDto) => createModelBase(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ModelBaseQueryKeys.lists() });
      notification.success({ message: 'Tạo model base thành công' });
    },
    onError: (error: ApiError) => {
      notification.error({
        message: error.response?.data?.message || 'Đã xảy ra lỗi khi tạo model base'
      });
    },
  });
};

/**
 * Hook để cập nhật thông tin model base
 */
export const useUpdateModelBase = (id: string) => {
  const queryClient = useQueryClient();
  const notification = useSmartNotification();

  return useMutation({
    mutationFn: (data: Partial<CreateModelBaseDto>) => updateModelBase(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ModelBaseQueryKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: ModelBaseQueryKeys.lists() });
      notification.success({ message: 'Cập nhật model base thành công' });
    },
    onError: (error: ApiError) => {
      notification.error({
        message: error.response?.data?.message || 'Đã xảy ra lỗi khi cập nhật model base'
      });
    },
  });
};

/**
 * Hook để xóa model base
 */
export const useDeleteModelBase = () => {
  const queryClient = useQueryClient();
  const notification = useSmartNotification();

  return useMutation({
    mutationFn: (id: string) => deleteModelBase(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ModelBaseQueryKeys.lists() });
      notification.success({ message: 'Xóa model base thành công' });
    },
    onError: (error: ApiError) => {
      notification.error({
        message: error.response?.data?.message || 'Đã xảy ra lỗi khi xóa model base'
      });
    },
  });
};
