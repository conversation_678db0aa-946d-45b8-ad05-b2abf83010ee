import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, MaxLength } from 'class-validator';
import { WarehouseTypeEnum } from '@modules/business/enums';

/**
 * DTO cho việc cập nhật kho
 */
export class UpdateWarehouseDto {
  /**
   * Tên kho
   * @example "Kho hàng chính (đã cập nhật)"
   */
  @ApiProperty({
    description: 'Tên kho',
    example: '<PERSON><PERSON> hàng <PERSON> (đã cập nhật)',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên kho phải là chuỗi' })
  @MaxLength(100, { message: 'Tên kho không được vượt quá 100 ký tự' })
  name?: string;

  /**
   * Mô tả kho
   * @example "Kho chứa các sản phẩm chính của công ty (đã cập nhật)"
   */
  @ApiProperty({
    description: '<PERSON>ô tả kho',
    example: '<PERSON><PERSON> chứa các sản phẩm chính của công ty (đã cập nhật)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả kho phải là chuỗi' })
  description?: string;

  /**
   * Loại kho
   * @example "VIRTUAL"
   */
  @ApiProperty({
    description: 'Loại kho',
    enum: WarehouseTypeEnum,
    example: WarehouseTypeEnum.VIRTUAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(WarehouseTypeEnum, { message: 'Loại kho không hợp lệ' })
  type?: WarehouseTypeEnum;
}
