import { BaseQueryParams, UserInfo } from './common.types';
import { UserToolListItem } from './user-tool.types';

/**
 * Interface định nghĩa tham số truy vấn cho danh sách nhóm tool
 */
export interface UserGroupToolQueryParams extends BaseQueryParams {
  typeAgentId?: number;
}

/**
 * Interface định nghĩa thông tin cơ bản của nhóm tool
 */
export interface UserGroupToolListItem {
  id: number;
  name: string;
  description?: string;
  toolCount: number;
  createdAt: number;
  updatedAt: number;
  createdBy?: UserInfo;
  updatedBy?: UserInfo;
  typeAgentId?: number;
}

/**
 * Interface định nghĩa mapping giữa nhóm tool và tool
 */
export interface UserGroupToolMapping {
  id: number;
  groupId: number;
  toolId: string;
  tool: UserToolListItem;
}

/**
 * Interface định nghĩa thông tin chi tiết của nhóm tool
 */
export interface UserGroupToolDetail extends UserGroupToolListItem {
  tools: UserGroupToolMapping[];
}

/**
 * Interface định nghĩa tham số tạo mới nhóm tool
 */
export interface CreateUserGroupToolParams {
  name: string;
  description?: string;
  toolIds?: string[];
  typeAgentId?: number;
}

/**
 * Interface định nghĩa tham số cập nhật nhóm tool
 */
export interface UpdateUserGroupToolParams {
  name?: string;
  description?: string;
  typeAgentId?: number;
}

/**
 * Interface định nghĩa tham số cập nhật danh sách tool của nhóm
 */
export interface UpdateGroupToolToolsParams {
  toolIds: string[];
}
