import { useCallback, useEffect } from 'react';
import {
  userLoginSuccess,
  adminLoginSuccess,
  logout,
  updateUserToken,
  updateAdminToken,
  updateUser,
  updateEmployee,
  saveVerifyInfo,
  saveTwoFactorInfo,
  AuthType,
} from '@/shared/store/slices/authCommonSlice';
import { User } from '@/modules/auth/types/auth.types';
import { Employee } from '@/modules/admin/auth/types/admin-auth.types';
import { useAppDispatch, useAppSelector } from '../store';
import { setLocalStorage, getLocalStorage, removeLocalStorage } from '@/shared/utils/storage-utils';

// Re-export AuthType để các component có thể import từ hook này
export { AuthType };

// Định nghĩa các khóa localStorage
const AUTH_STORAGE_KEY = 'auth_data';
const USER_DATA_KEY = 'user_data';
const EMPLOYEE_DATA_KEY = 'employee_data';
const VERIFY_DATA_KEY = 'verify_data';
const TWO_FACTOR_DATA_KEY = 'two_factor_data';

/**
 * Interface cho dữ liệu xác thực lưu trong localStorage
 */
interface AuthStorageData {
  accessToken: string;
  expiresAt: number;
  authType: AuthType;
}

/**
 * Hook quản lý thông tin xác thực chung cho cả user và admin
 * Tập trung việc lưu trữ và truy xuất token, thông tin người dùng
 */
export const useAuthCommon = () => {
  const dispatch = useAppDispatch();
  const authCommon = useAppSelector(state => state.authCommon);

  /**
   * Khôi phục thông tin xác thực từ localStorage
   */
  const restoreAuthFromStorage = useCallback(() => {
    try {
      // Lấy thông tin xác thực từ localStorage
      const authData = getLocalStorage<AuthStorageData>(AUTH_STORAGE_KEY);

      if (authData && authData.accessToken && authData.expiresAt > Date.now()) {
        // Lấy thông tin user hoặc employee tùy theo loại xác thực
        if (authData.authType === AuthType.USER) {
          const userData = getLocalStorage<User>(USER_DATA_KEY);

          // Khôi phục thông tin user
          dispatch(
            userLoginSuccess({
              accessToken: authData.accessToken,
              expiresAt: authData.expiresAt,
              user: userData || undefined,
            })
          );

          console.log('Restored user auth from localStorage');
        } else if (authData.authType === AuthType.ADMIN) {
          const employeeData = getLocalStorage<Employee>(EMPLOYEE_DATA_KEY);

          // Khôi phục thông tin admin
          dispatch(
            adminLoginSuccess({
              accessToken: authData.accessToken,
              expiresAt: authData.expiresAt,
              employee: employeeData || undefined,
            })
          );

          console.log('Restored admin auth from localStorage');
        }
      }
    } catch (error) {
      console.error('Error restoring auth from localStorage:', error);
    }
  }, [dispatch]);

  // Khôi phục thông tin xác thực từ localStorage khi component mount
  useEffect(() => {
    // Chỉ khôi phục nếu chưa đăng nhập trong Redux store
    if (!authCommon.isAuthenticated) {
      restoreAuthFromStorage();
    }
  }, [authCommon.isAuthenticated, restoreAuthFromStorage]);

  /**
   * Lưu thông tin đăng nhập user
   */
  const setUserAuth = useCallback(
    (data: { accessToken: string; expiresIn?: number; expiresAt?: number; user?: User }) => {
      // Tính toán expiresAt nếu không có
      const expiresAt = data.expiresAt || (data.expiresIn ? Date.now() + data.expiresIn * 1000 : 0);

      // Lưu vào Redux store
      dispatch(
        userLoginSuccess({
          accessToken: data.accessToken,
          expiresIn: data.expiresIn,
          expiresAt: expiresAt,
          user: data.user,
        })
      );

      // Lưu thông tin xác thực vào localStorage
      setLocalStorage<AuthStorageData>(AUTH_STORAGE_KEY, {
        accessToken: data.accessToken,
        expiresAt: expiresAt,
        authType: AuthType.USER,
      });

      // Lưu thông tin user vào localStorage nếu có
      if (data.user) {
        setLocalStorage<User>(USER_DATA_KEY, data.user);
      }

      console.log('User login success - token saved to Redux store and localStorage:', {
        hasExpiresAt: !!expiresAt,
        expiresAt: expiresAt,
        formattedExpiresAt: expiresAt ? new Date(expiresAt).toLocaleString() : 'N/A',
      });
    },
    [dispatch]
  );

  /**
   * Lưu thông tin đăng nhập admin
   */
  const setAdminAuth = useCallback(
    (data: {
      accessToken: string;
      expiresIn?: number;
      expiresAt?: number;
      employee?: Employee;
    }) => {
      // Tính toán expiresAt nếu không có
      const expiresAt = data.expiresAt || (data.expiresIn ? Date.now() + data.expiresIn * 1000 : 0);

      // Lưu vào Redux store
      dispatch(
        adminLoginSuccess({
          accessToken: data.accessToken,
          expiresIn: data.expiresIn,
          expiresAt: expiresAt,
          employee: data.employee,
        })
      );

      // Lưu thông tin xác thực vào localStorage
      setLocalStorage<AuthStorageData>(AUTH_STORAGE_KEY, {
        accessToken: data.accessToken,
        expiresAt: expiresAt,
        authType: AuthType.ADMIN,
      });

      // Lưu thông tin employee vào localStorage nếu có
      if (data.employee) {
        setLocalStorage<Employee>(EMPLOYEE_DATA_KEY, data.employee);
      }

      console.log('Admin login success - token saved to Redux store and localStorage');
    },
    [dispatch]
  );

  /**
   * Cập nhật token cho user
   */
  const updateUserAuthToken = useCallback(
    (data: { accessToken: string; refreshToken?: string; expiresIn: number }) => {
      // Tính toán thời điểm hết hạn
      const expiresAt = Date.now() + data.expiresIn * 1000;

      // Cập nhật trong Redux store
      dispatch(
        updateUserToken({
          accessToken: data.accessToken,
          expiresIn: data.expiresIn,
        })
      );

      // Cập nhật trong localStorage
      const authData = getLocalStorage<AuthStorageData>(AUTH_STORAGE_KEY);
      if (authData && authData.authType === AuthType.USER) {
        setLocalStorage<AuthStorageData>(AUTH_STORAGE_KEY, {
          ...authData,
          accessToken: data.accessToken,
          expiresAt: expiresAt,
        });
      }

      console.log('User token updated in Redux store and localStorage');
    },
    [dispatch]
  );

  /**
   * Cập nhật token cho admin
   */
  const updateAdminAuthToken = useCallback(
    (token: string, expiresIn: number, expiresAt: number) => {
      // Đảm bảo expiresAt được tính toán đúng
      const calculatedExpiresAt = expiresAt || (expiresIn ? Date.now() + expiresIn * 1000 : 0);

      // Cập nhật trong Redux store
      dispatch(
        updateAdminToken({
          accessToken: token,
          expiresIn,
          expiresAt: calculatedExpiresAt,
        })
      );

      // Cập nhật trong localStorage
      const authData = getLocalStorage<AuthStorageData>(AUTH_STORAGE_KEY);
      if (authData && authData.authType === AuthType.ADMIN) {
        setLocalStorage<AuthStorageData>(AUTH_STORAGE_KEY, {
          ...authData,
          accessToken: token,
          expiresAt: calculatedExpiresAt,
        });
      }

      console.log('Admin token updated in Redux store and localStorage');
    },
    [dispatch]
  );

  /**
   * Cập nhật thông tin người dùng
   */
  const updateAuthUser = useCallback(
    (user: User) => {
      // Cập nhật trong Redux store
      dispatch(updateUser({ user }));

      // Cập nhật trong localStorage
      setLocalStorage<User>(USER_DATA_KEY, user);

      console.log('User data updated in Redux store and localStorage');
    },
    [dispatch]
  );

  /**
   * Cập nhật thông tin nhân viên
   */
  const updateAuthEmployee = useCallback(
    (employee: Employee) => {
      // Cập nhật trong Redux store
      dispatch(updateEmployee({ employee }));

      // Cập nhật trong localStorage
      setLocalStorage<Employee>(EMPLOYEE_DATA_KEY, employee);

      console.log('Employee data updated in Redux store and localStorage');
    },
    [dispatch]
  );

  /**
   * Đăng xuất
   */
  const clearAuth = useCallback(() => {
    // Xóa trong Redux store
    dispatch(logout());

    // Xóa trong localStorage
    removeLocalStorage(AUTH_STORAGE_KEY);
    removeLocalStorage(USER_DATA_KEY);
    removeLocalStorage(EMPLOYEE_DATA_KEY);
    removeLocalStorage(VERIFY_DATA_KEY);
    removeLocalStorage(TWO_FACTOR_DATA_KEY);

    console.log('Logged out - auth data cleared from Redux store and localStorage');
  }, [dispatch]);

  /**
   * Lưu thông tin xác thực email
   */
  const saveVerifyInfoCallback = useCallback(
    (data: {
      verifyToken: string;
      expiresIn?: number;
      expiresAt?: number;
      info?: unknown | unknown[];
    }) => {
      // Chuẩn hóa dữ liệu
      const normalizedInfo = data.info
        ? Array.isArray(data.info)
          ? data.info
          : [data.info]
        : undefined;

      // Tính toán expiresAt nếu không có
      const expiresAt = data.expiresAt || (data.expiresIn ? Date.now() + data.expiresIn * 1000 : 0);

      // Lưu vào Redux store
      dispatch(
        saveVerifyInfo({
          ...data,
          expiresAt,
          info: normalizedInfo,
        })
      );

      // Lưu vào localStorage
      setLocalStorage(VERIFY_DATA_KEY, {
        verifyToken: data.verifyToken,
        expiresAt,
        info: normalizedInfo,
      });

      console.log('Verify info saved to Redux store and localStorage');
    },
    [dispatch]
  );

  /**
   * Lưu thông tin xác thực 2FA
   */
  const saveTwoFactorInfoCallback = useCallback(
    (data: {
      verifyToken: string;
      expiresAt: number;
      enabledMethods: Array<{ type: string; value: string }>;
    }) => {
      // Lưu vào Redux store
      dispatch(saveTwoFactorInfo(data));

      // Lưu vào localStorage
      setLocalStorage(TWO_FACTOR_DATA_KEY, {
        verifyToken: data.verifyToken,
        expiresAt: data.expiresAt,
        enabledMethods: data.enabledMethods,
      });

      console.log('Two-factor auth info saved to Redux store and localStorage');
    },
    [dispatch]
  );

  /**
   * Kiểm tra xem người dùng đã đăng nhập chưa
   */
  const isUserAuthenticated =
    authCommon.isAuthenticated && !!authCommon.accessToken && authCommon.authType === AuthType.USER;

  /**
   * Kiểm tra xem admin đã đăng nhập chưa
   */
  const isAdminAuthenticated =
    authCommon.isAuthenticated &&
    !!authCommon.accessToken &&
    authCommon.authType === AuthType.ADMIN;

  /**
   * Kiểm tra token có hợp lệ không
   */
  const isTokenValid = useCallback(() => {
    // Kiểm tra token trong Redux store
    if (authCommon.accessToken && authCommon.expiresAt) {
      const now = Date.now();
      const isValid = now < authCommon.expiresAt;

      console.log('Checking token validity from Redux store:', {
        now,
        formattedNow: new Date(now).toLocaleString(),
        expiresAt: authCommon.expiresAt,
        formattedExpiresAt: new Date(authCommon.expiresAt).toLocaleString(),
        isValid,
        authType: authCommon.authType,
      });

      return isValid;
    }

    // Nếu không có token trong Redux store, kiểm tra trong localStorage
    const authData = getLocalStorage<AuthStorageData>(AUTH_STORAGE_KEY);
    if (authData && authData.accessToken && authData.expiresAt) {
      const now = Date.now();
      const isValid = now < authData.expiresAt;

      console.log('Checking token validity from localStorage:', {
        now,
        formattedNow: new Date(now).toLocaleString(),
        expiresAt: authData.expiresAt,
        formattedExpiresAt: new Date(authData.expiresAt).toLocaleString(),
        isValid,
        authType: authData.authType,
      });

      // Nếu token hợp lệ, khôi phục thông tin xác thực từ localStorage
      if (isValid) {
        restoreAuthFromStorage();
        return true;
      }
    }

    return false;
  }, [authCommon.accessToken, authCommon.expiresAt, authCommon.authType, restoreAuthFromStorage]);

  /**
   * Kiểm tra xem người dùng có quyền truy cập vào một tính năng cụ thể không
   * @param requiredType Loại người dùng yêu cầu để truy cập tính năng
   * @returns true nếu người dùng có quyền truy cập, ngược lại false
   */
  const hasAccess = useCallback(
    (requiredType: AuthType): boolean => {
      if (requiredType === AuthType.NONE) return true;
      return authCommon.authType === requiredType;
    },
    [authCommon.authType]
  );

  /**
   * Kiểm tra xem admin có quyền cụ thể không
   * @param permission Quyền cần kiểm tra (ví dụ: 'users.create', 'products.delete')
   * @returns true nếu admin có quyền, ngược lại false
   */
  const hasPermission = useCallback(
    (permission: string): boolean => {
      // Kiểm tra xem có phải là admin không
      if (!isAdminAuthenticated || !authCommon.employee) {
        return false;
      }

      // Lấy danh sách quyền của admin
      const adminPermissions = authCommon.employee.permissions || [];

      // Kiểm tra xem admin có quyền cụ thể không
      return adminPermissions.includes(permission);
    },
    [isAdminAuthenticated, authCommon.employee]
  );

  /**
   * Kiểm tra xem admin có một trong các quyền được chỉ định không
   * @param permissions Danh sách quyền cần kiểm tra
   * @returns true nếu admin có ít nhất một quyền, ngược lại false
   */
  const hasAnyPermission = useCallback(
    (permissions: string[]): boolean => {
      // Kiểm tra xem có phải là admin không
      if (!isAdminAuthenticated || !authCommon.employee) {
        return false;
      }

      // Lấy danh sách quyền của admin
      const adminPermissions = authCommon.employee.permissions || [];

      // Kiểm tra xem admin có ít nhất một quyền trong danh sách không
      return permissions.some(permission => adminPermissions.includes(permission));
    },
    [isAdminAuthenticated, authCommon.employee]
  );

  /**
   * Kiểm tra xem admin có tất cả các quyền được chỉ định không
   * @param permissions Danh sách quyền cần kiểm tra
   * @returns true nếu admin có tất cả các quyền, ngược lại false
   */
  const hasAllPermissions = useCallback(
    (permissions: string[]): boolean => {
      // Kiểm tra xem có phải là admin không
      if (!isAdminAuthenticated || !authCommon.employee) {
        return false;
      }

      // Lấy danh sách quyền của admin
      const adminPermissions = authCommon.employee.permissions || [];

      // Kiểm tra xem admin có tất cả các quyền trong danh sách không
      return permissions.every(permission => adminPermissions.includes(permission));
    },
    [isAdminAuthenticated, authCommon.employee]
  );

  /**
   * Kiểm tra xem người dùng có phải là user không
   */
  const isUser = useCallback((): boolean => {
    return authCommon.authType === AuthType.USER;
  }, [authCommon.authType]);

  /**
   * Kiểm tra xem người dùng có phải là admin không
   */
  const isAdmin = useCallback((): boolean => {
    return authCommon.authType === AuthType.ADMIN;
  }, [authCommon.authType]);

  return {
    // State
    accessToken: authCommon.accessToken,
    expiresAt: authCommon.expiresAt,
    user: authCommon.user,
    employee: authCommon.employee,
    isAuthenticated: authCommon.isAuthenticated,
    isUserAuthenticated,
    isAdminAuthenticated,
    authType: authCommon.authType,

    // Verification state
    verifyToken: authCommon.verifyToken,
    verifyExpiresAt: authCommon.verifyExpiresAt,
    verifyInfo: authCommon.verifyInfo,

    // Two-factor auth state
    twoFactorVerifyToken: authCommon.twoFactorVerifyToken,
    twoFactorExpiresAt: authCommon.twoFactorExpiresAt,
    enabledMethods: authCommon.enabledMethods,

    // User actions
    setUserAuth,
    updateUserAuthToken,
    updateAuthUser,

    // Admin actions
    setAdminAuth,
    updateAdminAuthToken,
    updateAuthEmployee,

    // Common actions
    clearAuth,
    saveVerifyInfo: saveVerifyInfoCallback,
    saveTwoFactorInfo: saveTwoFactorInfoCallback,

    // Helpers
    getToken: useCallback(() => {
      // Lấy token từ Redux store
      if (authCommon.accessToken) {
        return authCommon.accessToken;
      }

      // Nếu không có token trong Redux store, lấy từ localStorage
      const authData = getLocalStorage<AuthStorageData>(AUTH_STORAGE_KEY);
      if (authData && authData.accessToken && authData.expiresAt > Date.now()) {
        return authData.accessToken;
      }

      return null;
    }, [authCommon.accessToken]),
    isTokenValid,

    // Auth type helpers
    isUser,
    isAdmin,
    hasAccess,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,

    // Convenience methods
    getCurrentUser: useCallback(
      () => (isUserAuthenticated ? authCommon.user : null),
      [isUserAuthenticated, authCommon.user]
    ),
    getCurrentAdmin: useCallback(
      () => (isAdminAuthenticated ? authCommon.employee : null),
      [isAdminAuthenticated, authCommon.employee]
    ),
  };
};
