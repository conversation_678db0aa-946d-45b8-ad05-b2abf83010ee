/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Modal,
  Typography,
  Button,
} from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ToolGrid, ToolForm } from '../components';
import {
  useAdminTools,
  useCreateAdminTool,
  useDeleteAdminTool,
  useAdminToolDetail,
  useUpdateAdminTool
} from '../hooks';
import { ToolListItem, ToolStatus, ToolSortBy, CreateToolParams, UpdateToolParams } from '../types/tool.types';

/**
 * Trang hiển thị danh sách tools dạng grid
 */
const ToolsPage: React.FC = () => {
  const { t } = useTranslation();
 

  // State cho tìm kiếm và lọc
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<ToolStatus | 'all'>('all');

  // State cho phân trang
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(12);

  // State cho xóa tool
  const [toolToDelete, setToolToDelete] = useState<ToolListItem | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // State cho xem/chỉnh sửa tool
  const [toolToView, setToolToView] = useState<ToolListItem | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // Sử dụng hook animation cho form tạo mới
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form xem/chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo(() => ({
    page: currentPage,
    limit: itemsPerPage,
    search: searchTerm || undefined,
    status: filterStatus !== 'all' ? filterStatus : undefined,
    sortBy: ToolSortBy.CREATED_AT,
    sortDirection: 'DESC' as const,
  }), [currentPage, itemsPerPage, searchTerm, filterStatus]);

  // Hooks để gọi API
  const { data: toolsData, isLoading } = useAdminTools(queryParams);
  const createToolMutation = useCreateAdminTool();
  const deleteToolMutation = useDeleteAdminTool();
  const updateToolMutation = useUpdateAdminTool();
  const { isLoading: isLoadingDetail } = useAdminToolDetail(
    toolToView?.id || ''
  );

  const createTool = createToolMutation.mutateAsync;
  const isCreating = createToolMutation.isPending || false;
  const deleteTool = deleteToolMutation.mutateAsync;
  const isDeleting = deleteToolMutation.isPending || false;
  const updateTool = updateToolMutation.mutateAsync;
  const isUpdating = updateToolMutation.isPending || false;

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý lọc theo trạng thái
  const handleFilterStatus = useCallback((status: string) => {
    setFilterStatus(status as ToolStatus | 'all');
    setCurrentPage(1); // Reset về trang 1 khi lọc
  }, []);

  // Xử lý xem chi tiết tool
  const handleViewTool = useCallback((tool: ToolListItem) => {
    setToolToView(tool);
    setIsEditMode(false);
    showEditForm();
  }, [showEditForm]);

  // Xử lý chỉnh sửa tool
  const handleEditTool = useCallback((tool: ToolListItem) => {
    setToolToView(tool);
    setIsEditMode(true);
    showEditForm();
  }, [showEditForm]);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setToolToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!toolToDelete) return;

    try {
      await deleteTool(toolToDelete.id);
      setShowDeleteConfirm(false);
      setToolToDelete(null);
    } catch (error) {
      console.error('Error deleting tool:', error);
    }
  }, [toolToDelete, deleteTool]);

  // Xử lý submit form tạo tool
  const handleSubmitCreateTool = useCallback((values: CreateToolParams | UpdateToolParams) => {
    try {
      if ('toolName' in values) {
        // Nếu là CreateToolParams
        createTool(values).then(() => {
          hideCreateForm();
        }).catch((error) => {
          console.error('Error creating tool:', error);
        });
      }
    } catch (error) {
      console.error('Error creating tool:', error);
    }
  }, [createTool, hideCreateForm]);

  // Xử lý submit form chỉnh sửa tool
  const handleSubmitEditTool = useCallback((values: CreateToolParams | UpdateToolParams) => {
    if (!toolToView) return;

    try {
      updateTool({ id: toolToView.id, data: values as UpdateToolParams }).then(() => {
        hideEditForm();
        setToolToView(null);
      }).catch((error) => {
        console.error('Error updating tool:', error);
      });
    } catch (error) {
      console.error('Error updating tool:', error);
    }
  }, [updateTool, hideEditForm, toolToView]);

  // Xử lý đóng form xem/chỉnh sửa
  const handleCloseEditForm = useCallback(() => {
    hideEditForm();
    setToolToView(null);
    setIsEditMode(false);
  }, [hideEditForm]);

  return (
    <div className="space-y-4">
      {/* Menu bar */}
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={showCreateForm}
        items={[
          {
            id: 'all',
            label: t('common.all', 'All'),
            icon: 'list',
            onClick: () => handleFilterStatus('all'),
          },
          {
            id: 'draft',
            label: t('admin.tool.status.draft', 'Draft'),
            icon: 'file',
            onClick: () => handleFilterStatus(ToolStatus.DRAFT),
          },
          {
            id: 'approved',
            label: t('admin.tool.status.approved', 'Approved'),
            icon: 'check-circle',
            onClick: () => handleFilterStatus(ToolStatus.APPROVED),
          },
          {
            id: 'deprecated',
            label: t('admin.tool.status.deprecated', 'Deprecated'),
            icon: 'archive',
            onClick: () => handleFilterStatus(ToolStatus.DEPRECATED),
          },
        ]}
      />

      {/* SlideInForm cho form thêm mới */}
      <SlideInForm isVisible={isCreateFormVisible}>
        <ToolForm
          onSubmit={handleSubmitCreateTool}
          onCancel={hideCreateForm}
          isLoading={isCreating}
        />
      </SlideInForm>

      {/* SlideInForm cho form xem/chỉnh sửa */}
      <SlideInForm isVisible={isEditFormVisible}>
        {toolToView && (
          <ToolForm
            initialValues={toolToView as any}
            onSubmit={handleSubmitEditTool}
            onCancel={handleCloseEditForm}
            isLoading={isUpdating || isLoadingDetail}
            isEdit={isEditMode}
            readOnly={!isEditMode}
          />
        )}
      </SlideInForm>

      {/* Hiển thị danh sách tool */}
      {toolsData?.items && toolsData.items.length > 0 ? (
        <ToolGrid
          tools={toolsData.items}
          onViewTool={handleViewTool}
          onEditTool={handleEditTool}
        />
      ) : (
        <Card className="p-8">
          <div className="flex flex-col items-center justify-center">
            <Typography variant="h6" className="text-gray-500 dark:text-gray-400">
              {isLoading
                ? t('common.loading', 'Loading...')
                : t('admin.tool.noTools', 'No tools found')}
            </Typography>
          </div>
        </Card>
      )}

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('admin.tool.confirmDelete', 'Confirm Delete')}
        size="sm"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancelDelete}>
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button variant="danger" onClick={handleConfirmDelete} disabled={isDeleting}>
              {isDeleting ? t('common.deleting', 'Deleting...') : t('common.delete', 'Delete')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography>
            {t('admin.tool.confirmDeleteMessage', 'Are you sure you want to delete this tool?')}
          </Typography>
          {toolToDelete && (
            <Typography variant="body2" className="mt-2 font-semibold">
              {toolToDelete.name}
            </Typography>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default ToolsPage;


