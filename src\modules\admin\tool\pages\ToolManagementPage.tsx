import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import { useAdminTools } from '../hooks';
import { ToolSortBy } from '../types/tool.types';

/**
 * Trang tổng quan quản lý Tools
 */
const ToolManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin']);

  // Lấy tổng số tools để hiển thị trên card
  const { data: toolsData } = useAdminTools({
    page: 1,
    limit: 1,
    sortBy: ToolSortBy.CREATED_AT,
    sortDirection: 'DESC' as const,
  });

  // Lấy tổng số tool groups để hiển thị trên card
  // Giả sử chúng ta sẽ thêm hook useAdminToolGroups sau
  const totalToolGroups = '0';

  // Lấy tổng số tool versions để hiển thị trên card
  // Giả sử chúng ta sẽ thêm hook useAdminToolVersions sau
  const totalToolVersions = '0';

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Tools Card */}
        <ModuleCard
          title={t('admin:tool.tools', 'Tools')}
          description={t(
            'admin:tool.toolsDescription',
            'Quản lý các tools trong hệ thống, bao gồm thêm, sửa, xóa và phê duyệt tools.'
          )}
          icon="tool"
          count={toolsData?.meta?.totalItems?.toString() || '0'}
          countLabel={t('admin:tool.totalTools', 'Tổng số tools')}
          linkTo="/admin/tools/list"
          linkText={t('admin:tool.manageTools', 'Quản lý tools')}
        />

        {/* Tool Groups Card */}
        <ModuleCard
          title={t('admin:tool.toolGroups', 'Nhóm Tools')}
          description={t(
            'admin:tool.toolGroupsDescription',
            'Quản lý các nhóm tools, phân loại và tổ chức tools theo nhóm chức năng.'
          )}
          icon="folder"
          count={totalToolGroups}
          countLabel={t('admin:tool.totalToolGroups', 'Tổng số nhóm tools')}
          linkTo="/admin/tools/groups"
          linkText={t('admin:tool.manageToolGroups', 'Quản lý nhóm tools')}
        />

        {/* Tool Versions Card */}
        <ModuleCard
          title={t('admin:tool.toolVersions', 'Phiên bản Tools')}
          description={t(
            'admin:tool.toolVersionsDescription',
            'Quản lý các phiên bản của tools, theo dõi lịch sử thay đổi và cập nhật.'
          )}
          icon="git-branch"
          count={totalToolVersions}
          countLabel={t('admin:tool.totalToolVersions', 'Tổng số phiên bản')}
          linkTo="/admin/tools/versions"
          linkText={t('admin:tool.manageToolVersions', 'Quản lý phiên bản')}
        />
      </ResponsiveGrid>
    </div>
  );
};

export default ToolManagementPage;
