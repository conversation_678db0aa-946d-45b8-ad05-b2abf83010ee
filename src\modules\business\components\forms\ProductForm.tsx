import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Textarea,
  ConditionalField,
  Divider,
  Typography,
  Chip,
} from '@/shared/components/common';
import SearchInputWithLazyLoading from '@/shared/components/common/SearchInputWithLazyLoading/SearchInputWithLazyLoading';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { createProductSchema } from '../../schemas/product.schema';
import {
  PriceTypeEnum,
  HasPriceDto,
  StringPriceDto,
  CreateProductDto
} from '../../types/product.types';
import { useCreateProduct } from '../../hooks/useProductQuery';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { CustomFieldService } from '../../services/custom-field.service';
import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';

interface ProductFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  isSubmitting: boolean;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  value: Record<string, unknown>;
}



// Interface cho form values
interface ProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    length?: string | number;
    width?: string | number;
    height?: string | number;
    weight?: string | number;
  };
  customFields?: SelectedCustomField[];
}

/**
 * Form tạo sản phẩm mới
 */
const ProductForm: React.FC<ProductFormProps> = ({ onSubmit, onCancel, isSubmitting }) => {
  const { t } = useTranslation(['business', 'common']);
  const { mutateAsync: createProduct, isPending } = useCreateProduct();

  // State cho trường tùy chỉnh đã chọn
  const [selectedCustomFields, setSelectedCustomFields] = useState<SelectedCustomField[]>([]);



  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();



  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    try {
      const formValues = values as ProductFormValues;

      // Chuẩn bị dữ liệu trường tùy chỉnh
      const customFieldsData = selectedCustomFields.map(field => ({
        fieldId: field.fieldId,
        value: field.value,
      }));



      // Chuyển đổi giá trị form thành dữ liệu API
      const productData: CreateProductDto = {
        name: formValues.name,
        typePrice: formValues.typePrice,
        price: getPriceData(formValues),
        description: formValues.description,
        tags: tempTags, // Sử dụng tempTags thay vì formValues.tags
        shipmentConfig: formValues.shipmentConfig ? {
          length: Number(formValues.shipmentConfig.length) || undefined,
          width: Number(formValues.shipmentConfig.width) || undefined,
          height: Number(formValues.shipmentConfig.height) || undefined,
          weight: Number(formValues.shipmentConfig.weight) || undefined,
        } : undefined,
        customFields: customFieldsData.length > 0 ? customFieldsData : undefined,
      };

      // Gọi API tạo sản phẩm
      await createProduct(productData);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.createSuccess'),
        duration: 3000,
      });

      // Gọi callback onSubmit
      onSubmit(values as Record<string, unknown>);
    } catch (error) {
      console.error('Error creating product:', error);
      NotificationUtil.error({
        message: t('business:product.createError'),
        duration: 3000,
      });
    }
  };

  // Hàm lấy dữ liệu giá dựa trên loại giá
  const getPriceData = (values: ProductFormValues): HasPriceDto | StringPriceDto | null => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE && values.listPrice && values.salePrice) {
      return {
        listPrice: Number(values.listPrice),
        salePrice: Number(values.salePrice),
        currency: values.currency || 'VND',
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE && values.priceDescription) {
      return {
        priceDescription: values.priceDescription,
      };
    }
    return null;
  };

  // Hàm load trường tùy chỉnh
  const loadCustomFields = async (search: string, pagination?: { page: number; limit: number }) => {
    try {
      const response = await CustomFieldService.getCustomFields({
        search,
        page: pagination?.page || 1,
        limit: pagination?.limit || 10,
      });

      return response.result.items.map(item => ({
        id: item.id,
        name: item.label,
        description: item.type,
        image: null,
      }));
    } catch (error) {
      console.error('Error loading custom fields:', error);
      return [];
    }
  };



  // Xử lý khi chọn trường tùy chỉnh
  const handleCustomFieldSelect = (id: string | number) => {
    const fieldId = Number(id);
    if (!fieldId) return;

    // Kiểm tra xem trường đã được chọn chưa
    const existingIndex = selectedCustomFields.findIndex(field => field.fieldId === fieldId);

    if (existingIndex >= 0) {
      // Nếu đã chọn, cập nhật lại
      const updatedFields = [...selectedCustomFields];
      updatedFields[existingIndex] = {
        ...updatedFields[existingIndex],
        id: Date.now(), // ID tạm thời
        fieldId,
        value: { value: '' }, // Giá trị mặc định
      };
      setSelectedCustomFields(updatedFields);
    } else {
      // Nếu chưa chọn, thêm mới
      setSelectedCustomFields([
        ...selectedCustomFields,
        {
          id: Date.now(), // ID tạm thời
          fieldId,
          value: { value: '' }, // Giá trị mặc định
        },
      ]);
    }
  };



  // Giá trị mặc định cho form
  const defaultValues = {
    name: '',
    typePrice: PriceTypeEnum.HAS_PRICE,
    listPrice: '',
    salePrice: '',
    currency: 'VND',
    priceDescription: '',
    description: '',
    tags: [],
    shipmentConfig: {
      length: '',
      width: '',
      height: '',
      weight: '',
    },
    customFields: [],
  };

  return (
    <Card title={t('business:product.form.createTitle')}>
      <Form
        ref={formRef}
        schema={createProductSchema(t)}
        onSubmit={handleSubmit}
        defaultValues={defaultValues}
        className="p-4 space-y-4"
      >
        <FormItem
          name="name"
          label={t('business:product.name')}
          required
        >
          <Input fullWidth placeholder={t('business:product.form.namePlaceholder')} />
        </FormItem>

        <FormItem
          name="typePrice"
          label={t('business:product.priceType.title')}
          required
        >
          <Select
            fullWidth
            options={[
              { value: PriceTypeEnum.HAS_PRICE, label: t('business:product.priceType.hasPrice') },
              { value: PriceTypeEnum.STRING_PRICE, label: t('business:product.priceType.stringPrice') },
              { value: PriceTypeEnum.NO_PRICE, label: t('business:product.priceType.noPrice') },
            ]}
          />
        </FormItem>

        {/* Hiển thị các trường giá dựa trên loại giá */}
        <ConditionalField
          condition={{
            field: 'typePrice',
            type: ConditionType.EQUALS,
            value: PriceTypeEnum.HAS_PRICE,
          }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem
              name="listPrice"
              label={t('business:product.listPrice')}
              required
            >
              <Input fullWidth type="number" min="0" placeholder="0" />
            </FormItem>
            <FormItem
              name="salePrice"
              label={t('business:product.salePrice')}
              required
            >
              <Input fullWidth type="number" min="0" placeholder="0" />
            </FormItem>
            <FormItem
              name="currency"
              label={t('business:product.currency')}
              required
            >
              <Input fullWidth defaultValue="VND" />
            </FormItem>
          </div>
        </ConditionalField>

        <ConditionalField
          condition={{
            field: 'typePrice',
            type: ConditionType.EQUALS,
            value: PriceTypeEnum.STRING_PRICE,
          }}
        >
          <FormItem
            name="priceDescription"
            label={t('business:product.priceDescription')}
            required
          >
            <Input fullWidth placeholder={t('business:product.form.priceDescriptionPlaceholder')} />
          </FormItem>
        </ConditionalField>

        <FormItem
          name="description"
          label={t('business:product.description')}
        >
          <Textarea
            fullWidth
            rows={4}
            placeholder={t('business:product.form.descriptionPlaceholder')}
          />
        </FormItem>

        <FormItem
          name="tags"
          label={t('business:product.tags')}
        >
          <div className="space-y-2">
            <Input
              fullWidth
              placeholder={t('business:product.form.tagsPlaceholder')}
              onKeyDown={e => {
                if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                  e.preventDefault();

                  // Lấy tag mới
                  const newTag = e.currentTarget.value.trim();

                  // Thêm tag mới nếu chưa tồn tại
                  if (!tempTags.includes(newTag)) {
                    setTempTags(prev => [...prev, newTag]);
                  }

                  e.currentTarget.value = '';
                }
              }}
            />
            <div className="flex flex-wrap gap-1 mt-2">
              {tempTags.map((tag, tagIndex) => (
                <Chip
                  key={`tag-${tagIndex}-${tag}`}
                  size="sm"
                  closable
                  onClose={() => {
                    setTempTags(prev => prev.filter(t => t !== tag));
                  }}
                >
                  {tag}
                </Chip>
              ))}
            </div>
          </div>
        </FormItem>

        {/* Phần trường tùy chỉnh */}
        <Divider className="my-4" />
        <Typography variant="h6" className="mb-4">
          {t('business:product.customFields.title')}
        </Typography>

        {/* Chọn trường tùy chỉnh */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">
            {t('business:product.customFields.selectField')}
          </label>
          <SearchInputWithLazyLoading
            loadOptions={loadCustomFields}
            onChange={(id) => {
              // Kiểm tra xem trường đã được chọn chưa
              const fieldId = Number(id);
              if (!fieldId) return;

              const existingField = selectedCustomFields.find(field => field.fieldId === fieldId);
              if (existingField) {
                // Nếu đã chọn, không làm gì cả
                return;
              }

              // Nếu chưa chọn, thêm mới
              handleCustomFieldSelect(id);
            }}
            placeholder={t('business:product.customFields.searchPlaceholder')}
            showSearchIcon={true}
            loadInitialOptions={true}
          />
        </div>

        {/* Hiển thị danh sách trường tùy chỉnh đã chọn */}
        {selectedCustomFields.length > 0 && (
          <div className="mb-4 p-4 border border-gray-200 rounded-md">
            <Typography variant="subtitle1" className="mb-2">
              {t('business:product.customFields.selectedFields')}
            </Typography>
            <div className="space-y-2">
              {selectedCustomFields.map((field) => (
                <div key={field.id} className="flex items-center justify-between">
                  <span>{field.fieldId}</span>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => {
                      setSelectedCustomFields(
                        selectedCustomFields.filter((f) => f.id !== field.id)
                      );
                    }}
                  >
                    {t('common:remove')}
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}



        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting || isPending}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            isLoading={isSubmitting || isPending}
          >
            {t('common:save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default ProductForm;
