import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Button, Table, Tooltip, Modal, IconCard } from '@/shared/components/common';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import UserToolGroupForm from '../components/UserToolGroupForm';

// Import hooks từ module tool
import {
  useUserGroupTools,
  useCreateUserGroupTool,
  useUpdateUserGroupTool,
  useDeleteUserGroupTool,
  useUserGroupToolDetail,
} from '../hooks/useUserGroupTool';
import { userGroupToolService } from '../services/user-group-tool.service';

// Import types từ module tool
import {
  UserGroupToolListItem,
  UserGroupToolDetail,
  UserGroupToolQueryParams,
  CreateUserGroupToolParams,
  UpdateUserGroupToolParams,
} from '../types';

/**
 * Trang quản lý nhóm tool
 */
const ToolGroupsPage: React.FC = () => {
  const { t } = useTranslation(['common']);
  const [toolGroups, setToolGroups] = useState<UserGroupToolListItem[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [toolGroupToDelete, setToolGroupToDelete] = useState<UserGroupToolListItem | null>(null);
  const [toolGroupToView, setToolGroupToView] = useState<UserGroupToolDetail | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [, setShowToolsManager] = useState(false);

  // State cho tìm kiếm
  const [searchTerm, setSearchTerm] = useState('');

  // State cho sắp xếp
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Sử dụng hook animation cho form tạo mới
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form xem/chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo<UserGroupToolQueryParams>(() => {
    const params: UserGroupToolQueryParams = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      sortBy: (sortBy as 'name' | 'createdAt' | 'updatedAt') || undefined,
      sortDirection: sortDirection === SortDirection.ASC ? 'ASC' : 'DESC',
    };

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection]);

  // Hooks để gọi API
  const {
    data: toolGroupsData,
    isLoading: isLoadingToolGroups,
    error: toolGroupsError,
  } = useUserGroupTools(queryParams);

  const { mutateAsync: createToolGroup, isPending: isCreating } = useCreateUserGroupTool();
  const { mutateAsync: updateToolGroup, isPending: isUpdating } = useUpdateUserGroupTool();
  const { mutateAsync: deleteToolGroup, isPending: isDeleting } = useDeleteUserGroupTool();
  // Không sử dụng hook này trong phiên bản hiện tại
  // const { mutateAsync: updateGroupTools } = useUpdateUserGroupToolTools();

  // Lấy chi tiết nhóm tool khi cần xem/chỉnh sửa
  const { data: toolGroupDetail, isLoading: isLoadingDetail } = useUserGroupToolDetail(
    toolGroupToView?.id || 0
  );

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (toolGroupsData) {
      setToolGroups(toolGroupsData.items);
      setTotalItems(toolGroupsData.meta?.totalItems || 0);
    }
  }, [toolGroupsData, toolGroupsError]);

  // Cập nhật toolGroupToView khi có dữ liệu chi tiết
  useEffect(() => {
    if (toolGroupDetail && toolGroupToView) {
      setToolGroupToView(toolGroupDetail);
    }
  }, [toolGroupDetail, toolGroupToView]);

  // Xử lý submit form tạo nhóm tool
  const handleSubmitCreateToolGroup = useCallback(
    async (values: CreateUserGroupToolParams | UpdateUserGroupToolParams) => {
      try {
        await createToolGroup(values as CreateUserGroupToolParams);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating tool group:', error);
      }
    },
    [createToolGroup, hideCreateForm]
  );

  // Xử lý submit form chỉnh sửa nhóm tool
  const handleSubmitEditToolGroup = useCallback(
    async (values: CreateUserGroupToolParams | UpdateUserGroupToolParams) => {
      if (!toolGroupToView) return;

      try {
        await updateToolGroup({
          id: toolGroupToView.id,
          data: values as UpdateUserGroupToolParams,
        });
        hideEditForm();
        setToolGroupToView(null);
      } catch (error) {
        console.error('Error updating tool group:', error);
      }
    },
    [updateToolGroup, hideEditForm, toolGroupToView]
  );

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((toolGroup: UserGroupToolListItem) => {
    setToolGroupToDelete(toolGroup);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setToolGroupToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!toolGroupToDelete) return;

    try {
      await deleteToolGroup(toolGroupToDelete.id);
      setShowDeleteConfirm(false);
      setToolGroupToDelete(null);
    } catch (error) {
      console.error('Error deleting tool group:', error);
    }
  }, [toolGroupToDelete, deleteToolGroup]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý hiển thị form xem chi tiết nhóm tool
  const handleShowViewForm = useCallback(
    (toolGroup: UserGroupToolListItem) => {
      setToolGroupToView(toolGroup as UserGroupToolDetail);
      showEditForm();
    },
    [showEditForm]
  );

  // Xử lý hiển thị form quản lý tool trong nhóm
  const handleShowToolsManager = useCallback(() => {
    setShowToolsManager(true);
  }, []);



  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'name',
        title: t('tools.name', 'Tên nhóm'),
        dataIndex: 'name',
        width: '25%',
        sortable: true,
      },
      {
        key: 'description',
        title: t('tools.description', 'Mô tả'),
        dataIndex: 'description',
        width: '30%',
        render: (value: unknown) => {
          return value ? String(value) : t('tools.noDescription', 'Không có mô tả');
        },
      },
      {
        key: 'toolCount',
        title: t('tools.toolCount', 'Số lượng tool'),
        dataIndex: 'toolCount',
        width: '10%',
        sortable: true,
      },
      {
        key: 'createdAt',
        title: t('tools.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const timestamp = Number(value);
          return isNaN(timestamp) ? 'N/A' : new Date(timestamp).toLocaleString();
        },
      },
      {
        key: 'createdBy',
        title: t('tools.createdBy', 'Người tạo'),
        dataIndex: 'createdBy',
        width: '15%',
        render: (value: unknown) => {
          const creator = value as { name: string; email?: string } | undefined;
          return (
            <div className="flex flex-col">
              <span>{creator?.name || t('tools.unknownUser', 'Không xác định')}</span>
              {creator?.email && <span className="text-xs text-gray-500">{creator.email}</span>}
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('common.actions', 'Thao tác'),
        render: (_: unknown, record: UserGroupToolListItem) => (
          <div className="flex space-x-2">
            <Tooltip content={t('common.view', 'Xem')}>
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => handleShowViewForm(record)}
              />
            </Tooltip>

            <Tooltip content={t('tools.manageTools', 'Quản lý công cụ')}>
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => {
                  // Lấy chi tiết nhóm tool trước khi hiển thị form quản lý
                  userGroupToolService
                    .getGroupToolById(record.id)
                    .then(detail => {
                      setToolGroupToView(detail);
                      handleShowToolsManager();
                    })
                    .catch(error => {
                      console.error('Error fetching group tool detail:', error);
                    });
                }}
              />
            </Tooltip>

            <Tooltip content={t('common.delete', 'Xóa')}>
              <IconCard
                icon="trash"
                variant="primary"
                size="sm"
                onClick={() => handleShowDeleteConfirm(record)}
              />
            </Tooltip>
          </div>
        ),
      },
    ];

    return allColumns;
  }, [t, handleShowDeleteConfirm, handleShowViewForm, handleShowToolsManager]);

  return (
    <div>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex-grow">
            <MenuIconBar
              onSearch={handleSearch}
              onAdd={() => showCreateForm()}
              onColumnVisibilityChange={handleColumnVisibilityChange}
              columns={visibleColumns}
              showDateFilter={false}
              showColumnFilter={true}
              items={[]}
            />
          </div>
        </div>

        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <UserToolGroupForm
            onSubmit={handleSubmitCreateToolGroup}
            onCancel={hideCreateForm}
            isLoading={isCreating}
          />
        </SlideInForm>

        {/* SlideInForm cho form xem/chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {toolGroupToView && (
            <UserToolGroupForm
              initialValues={toolGroupToView}
              onSubmit={handleSubmitEditToolGroup}
              onCancel={hideEditForm}
              isLoading={isUpdating || isLoadingDetail}
              isEdit={true}
            />
          )}
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<UserGroupToolListItem>
            columns={columns}
            data={toolGroups}
            rowKey="id"
            loading={isLoadingToolGroups}
            sortable={true}
            onSortChange={handleSortChange}
            defaultSort={{
              column: sortBy || '',
              order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        footer={
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleCancelDelete} disabled={isDeleting}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="danger" onClick={handleConfirmDelete} disabled={isDeleting}>
              {isDeleting ? t('common.processing', 'Đang xử lý...') : t('common.delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <p>
          {t(
            'tools.confirmDeleteToolGroupMessage',
            'Bạn có chắc chắn muốn xóa nhóm tool này? Hành động này không thể hoàn tác.'
          )}
        </p>
        {toolGroupToDelete && (
          <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded">
            <p className="font-medium">{toolGroupToDelete.name}</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ToolGroupsPage;
