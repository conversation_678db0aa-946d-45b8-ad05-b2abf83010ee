{"modelTraining": {"provider": {"title": "供应商集成", "list": "供应商列表", "add": "添加供应商", "edit": "编辑供应商", "delete": "删除供应商", "form": {"name": "供应商名称", "type": "供应商类型", "apiKey": "API 密钥", "namePlaceholder": "输入供应商名称", "typePlaceholder": "选择供应商类型", "apiKeyPlaceholder": "输入 API 密钥"}, "messages": {"createSuccess": "供应商创建成功", "updateSuccess": "供应商更新成功", "deleteSuccess": "供应商删除成功", "createError": "创建供应商时出错", "updateError": "更新供应商时出错", "deleteError": "删除供应商时出错", "confirmDelete": "您确定要删除此供应商吗？此操作无法撤消。"}, "empty": {"title": "尚无供应商", "description": "您尚未添加任何供应商。添加供应商以开始使用。"}}, "modelBase": {"title": "创建模型基础", "list": "模型基础列表", "add": "添加模型基础", "edit": "编辑模型基础", "delete": "删除模型基础", "form": {"name": "模型基础名称", "providerId": "提供商", "pricing": "定价", "input": "输入", "output": "输出", "train": "训练", "base": "基础", "fineTuning": "微调", "config": "配置", "topP": "Top P", "topK": "Top K", "tool": "工具", "temperature": "温度", "text": "文本", "image": "图像", "audio": "音频", "video": "视频", "toolCall": "工具调用", "namePlaceholder": "输入模型基础名称", "providerPlaceholder": "选择提供商"}, "messages": {"createSuccess": "模型基础创建成功", "updateSuccess": "模型基础更新成功", "deleteSuccess": "模型基础删除成功", "createError": "创建模型基础时出错", "updateError": "更新模型基础时出错", "deleteError": "删除模型基础时出错", "confirmDelete": "您确定要删除此模型基础吗？此操作无法撤消。"}, "empty": {"title": "尚无模型基础", "description": "您尚未添加任何模型基础。添加模型基础以开始使用。"}}, "dataset": {"title": "数据集微调模型", "list": "数据集列表", "add": "添加数据集", "edit": "编辑数据集", "delete": "删除数据集", "form": {"name": "数据集名称", "description": "描述", "trainData": "训练数据", "validationData": "验证数据", "import": "导入", "systemRole": "系统角色", "userRole": "用户角色", "assistantRole": "助手角色", "namePlaceholder": "输入数据集名称", "descriptionPlaceholder": "输入数据集描述", "messagePlaceholder": "输入消息内容...", "send": "发送", "selectRole": "选择角色"}, "messages": {"createSuccess": "数据集创建成功", "updateSuccess": "数据集更新成功", "deleteSuccess": "数据集删除成功", "createError": "创建数据集时出错", "updateError": "更新数据集时出错", "deleteError": "删除数据集时出错", "confirmDelete": "您确定要删除此数据集吗？此操作无法撤消。"}, "empty": {"title": "尚无数据集", "description": "您尚未添加任何数据集。添加数据集以开始使用。"}}}}