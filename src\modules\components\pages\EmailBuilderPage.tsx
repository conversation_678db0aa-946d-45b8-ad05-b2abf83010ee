import React, { useState } from 'react';
import { EmailBuilder } from '@/shared/components/email-builder';

// Tạo các component đ<PERSON>n gi<PERSON>n thay thế
interface CardComponent extends React.FC<{ className?: string, children: React.ReactNode }> {
  Header: React.FC<{ children: React.ReactNode }>;
  Content: React.FC<{ className?: string, children: React.ReactNode }>;
}

const CardHeader: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="p-4 border-b">
    {children}
  </div>
);

const CardContent: React.FC<{ className?: string, children: React.ReactNode }> = ({ className, children }) => (
  <div className={`p-4 ${className || ''}`}>
    {children}
  </div>
);

const Card: CardComponent = ({ className, children }) => (
  <div className={`border rounded-md shadow-sm ${className || ''}`}>
    {children}
  </div>
);

Card.Header = CardHeader;
Card.Content = CardContent;

const EmailBuilderPage: React.FC = () => {
  const [, setEmailHtml] = useState<string>('');
  const [compactMode,] = useState<boolean>(false);

  const handleContentChange = (html: string) => {
    setEmailHtml(html);
  };


  return (
    <Card.Content className="p-0">
      <div className={`${compactMode ? 'h-[500px]' : 'h-[800px]'} border rounded-md overflow-hidden`}>
        <EmailBuilder
          initialValue=""
          onContentChange={handleContentChange}
        // compactMode prop đã bị loại bỏ
        />
      </div>
    </Card.Content>
  );
};

export default EmailBuilderPage;
