import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Textarea, Select, FormItem } from '@/shared/components/common';
import {
  ToolVersion,
  CreateToolVersionParams,
  UpdateToolVersionParams,
  ToolStatus,
} from '../types/tool.types';
import { useAdminTools } from '../hooks';
import JsonEditor from './JsonEditor';

interface ToolVersionFormProps {
  initialValues?: ToolVersion;
  toolId?: string;
  onSubmit: (values: CreateToolVersionParams | UpdateToolVersionParams) => void;
  onCancel: () => void;
  readOnly?: boolean;
  isLoading?: boolean;
  isEdit?: boolean;
}

/**
 * Component form để tạo/chỉnh sửa phiên bản tool
 */
const ToolVersionForm: React.FC<ToolVersionFormProps> = ({
  initialValues,
  toolId,
  onSubmit,
  onCancel,
  readOnly = false,
  isLoading = false,
  isEdit = false,
}) => {
  const { t } = useTranslation();

  // State cho form
  const [toolName, setToolName] = useState(initialValues?.toolName || '');
  const [toolDescription, setToolDescription] = useState(initialValues?.toolDescription || '');
  const [parameters, setParameters] = useState<Record<string, unknown>>(
    initialValues?.parameters || {}
  );
  const [changeDescription, setChangeDescription] = useState(
    initialValues?.changeDescription || ''
  );
  const [status, setStatus] = useState<ToolStatus>(initialValues?.status || ToolStatus.DRAFT);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedToolId, setSelectedToolId] = useState<string>(toolId || '');

  // Lấy danh sách tools để hiển thị trong dropdown nếu đang tạo mới
  const { data: toolsData } = useAdminTools({
    page: 1,
    limit: 100, // Lấy tối đa 100 tools
  });

  // Cập nhật state khi có initialValues
  useEffect(() => {
    if (initialValues) {
      setToolName(initialValues.toolName || '');
      setToolDescription(initialValues.toolDescription || '');
      setParameters(initialValues.parameters || {});
      setChangeDescription(initialValues.changeDescription || '');
      setStatus(initialValues.status || ToolStatus.DRAFT);
    }
  }, [initialValues]);

  // Cập nhật toolId khi có thay đổi
  useEffect(() => {
    if (toolId) {
      setSelectedToolId(toolId);
    }
  }, [toolId]);

  // Xử lý thay đổi parameters
  const handleParametersChange = (parsedParams: Record<string, unknown>) => {
    setParameters(parsedParams);
    if (errors.parameters) {
      setErrors(prev => ({ ...prev, parameters: '' }));
    }
  };

  // Xử lý lỗi JSON
  const handleJsonError = (errorMessage: string | null) => {
    if (errorMessage) {
      setErrors(prev => ({
        ...prev,
        parameters: t('admin.tool.validation.invalidJson', 'Invalid JSON format'),
      }));
    }
  };

  // Xử lý submit form
  const handleSubmit = () => {
    // Validate form
    const newErrors: Record<string, string> = {};

    if (!toolName.trim()) {
      newErrors.toolName = t(
        'admin.tool.validation.toolNameRequired',
        'Tên hiển thị tool là bắt buộc'
      );
    }

    if (!selectedToolId && !isEdit) {
      newErrors.toolId = t('admin.tool.validation.toolRequired', 'Vui lòng chọn tool');
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Chuẩn bị dữ liệu để submit
    const formData: CreateToolVersionParams | UpdateToolVersionParams = {
      toolName,
      toolDescription: toolDescription || undefined,
      parameters,
      changeDescription: changeDescription || undefined,
      status,
    };

    onSubmit(formData);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <Typography variant="h5">
          {isEdit
            ? t('admin.tool.editToolVersion', 'Chỉnh sửa phiên bản tool')
            : t('admin.tool.createToolVersion', 'Tạo phiên bản tool mới')}
        </Typography>
      </div>

      <div className="space-y-4">
        {!isEdit && !toolId && (
          <FormItem
            label={t('admin.tool.selectTool', 'Chọn Tool')}
            helpText={errors.toolId}
            required
          >
            <Select
              value={selectedToolId}
              onChange={value => setSelectedToolId(value as string)}
              options={
                toolsData?.items?.map(tool => ({
                  value: tool.id,
                  label: tool.name,
                })) || []
              }
              disabled={readOnly || isLoading || !!toolId}
              placeholder={t('admin.tool.selectToolPlaceholder', 'Chọn tool để tạo phiên bản')}
            />
          </FormItem>
        )}

        <FormItem
          label={t('admin.tool.toolName', 'Tên hiển thị')}
          helpText={errors.toolName}
          required
        >
          <Input
            value={toolName}
            onChange={e => {
              setToolName(e.target.value);
              if (errors.toolName) setErrors(prev => ({ ...prev, toolName: '' }));
            }}
            placeholder={t('admin.tool.toolNamePlaceholder', 'Nhập tên hiển thị của tool')}
            disabled={readOnly || isLoading}
          />
        </FormItem>

        <FormItem label={t('admin.tool.toolDescription', 'Mô tả hiển thị')}>
          <Textarea
            value={toolDescription || ''}
            onChange={e => setToolDescription(e.target.value)}
            placeholder={t('admin.tool.toolDescriptionPlaceholder', 'Nhập mô tả hiển thị của tool')}
            disabled={readOnly || isLoading}
            rows={3}
          />
        </FormItem>

        <FormItem
          label={t('admin.tool.parameters', 'Tham số')}
          helpText={errors.parameters}
          required
        >
          <JsonEditor
            value={parameters}
            onChange={handleParametersChange}
            onError={handleJsonError}
            placeholder={t('admin.tool.parametersPlaceholder', 'Nhập tham số của tool dạng JSON')}
            disabled={readOnly || isLoading}
            minHeight={200}
            className="font-mono text-sm"
          />
        </FormItem>

        <FormItem label={t('admin.tool.changeDescription', 'Mô tả thay đổi')}>
          <Textarea
            value={changeDescription || ''}
            onChange={e => setChangeDescription(e.target.value)}
            placeholder={t(
              'admin.tool.changeDescriptionPlaceholder',
              'Mô tả những thay đổi trong phiên bản này'
            )}
            disabled={readOnly || isLoading}
            rows={3}
          />
        </FormItem>

        <FormItem label={t('admin.tool.status', 'Trạng thái')}>
          <Select
            value={status}
            onChange={val => setStatus(val as ToolStatus)}
            options={[
              { value: ToolStatus.DRAFT, label: t('admin.tool.status.draft', 'Bản nháp') },
              { value: ToolStatus.APPROVED, label: t('admin.tool.status.approved', 'Đã duyệt') },
              {
                value: ToolStatus.DEPRECATED,
                label: t('admin.tool.status.deprecated', 'Không dùng nữa'),
              },
            ]}
            disabled={readOnly || isLoading}
          />
        </FormItem>
      </div>

      {!readOnly && (
        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button variant="primary" onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? t('common.processing', 'Đang xử lý...') : t('common.save', 'Lưu')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default ToolVersionForm;
