import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Table,
  Badge,
  Chip,
  Tooltip,
  Modal,
  IconCard,
} from '@/shared/components/common';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';

// Import hooks từ module tool
import { useUserTools, useUserToolDetail } from '../hooks/useTool';
import {
  useUserToolVersionDetail,
  useSetDefaultUserToolVersion,
  useDeleteUserToolVersion,
} from '../hooks/useUserToolVersion';

// Import types từ module tool
import { UserToolVersion } from '../types';
import { ToolStatus } from '../types/common.types';
import { ToolQueryParams, ToolListItem } from '../types/tool.types';

/**
 * Trang quản lý phiên bản tool
 */
const ToolVersionsPage: React.FC = () => {
  const { t } = useTranslation(['common']);
  const [toolVersions, setToolVersions] = useState<UserToolVersion[]>([]);
  const [selectedTool, setSelectedTool] = useState<string>('');
  const [versionToDelete, setVersionToDelete] = useState<UserToolVersion | null>(null);
  const [versionToView, setVersionToView] = useState<UserToolVersion | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showSetDefaultConfirm, setShowSetDefaultConfirm] = useState(false);
  const [showVersions, setShowVersions] = useState(false);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Sử dụng hook animation cho form xem/chỉnh sửa
  const { isVisible: isEditFormVisible, showForm: showEditForm } = useSlideForm();

  // Nội dung form xem chi tiết phiên bản
  const renderVersionDetailContent = () => {
    if (!versionToView) return null;

    return (
      <div className="p-4">
        <Typography variant="h6" className="mb-4">
          {t('tools.versionDetail', 'Chi tiết phiên bản')}
        </Typography>

        <div className="space-y-4">
          <div>
            <Typography variant="subtitle1" className="text-gray-500">
              {t('tools.toolName', 'Tên hiển thị')}
            </Typography>
            <Typography>{versionToView.toolName}</Typography>
          </div>

          <div>
            <Typography variant="subtitle1" className="text-gray-500">
              {t('tools.version', 'Phiên bản')}
            </Typography>
            <Typography>v{versionToView.versionNumber}</Typography>
          </div>

          <div>
            <Typography variant="subtitle1" className="text-gray-500">
              {t('tools.status', 'Trạng thái')}
            </Typography>
            <Chip
              size="sm"
              variant={
                versionToView.status === ToolStatus.APPROVED
                  ? 'success'
                  : versionToView.status === ToolStatus.DRAFT
                    ? 'warning'
                    : 'danger'
              }
            >
              {t(`tools.status.${versionToView.status.toLowerCase()}`, versionToView.status)}
            </Chip>
          </div>

          <div>
            <Typography variant="subtitle1" className="text-gray-500">
              {t('tools.createdAt', 'Ngày tạo')}
            </Typography>
            <Typography>{formatDate(versionToView.createdAt)}</Typography>
          </div>
        </div>
      </div>
    );
  };

  // Tạo query params cho API lấy danh sách tools
  const toolsQueryParams = useMemo<ToolQueryParams>(() => {
    return {
      page: 1,
      limit: 100, // Lấy tối đa 100 tools
    };
  }, []);

  // Hooks để gọi API
  const { data: toolsData, isLoading: isLoadingTools } = useUserTools(toolsQueryParams);

  // Hook useUserToolDetail đã có enabled: !!id trong định nghĩa
  const { data: toolDetail, isLoading: isLoadingToolDetail } = useUserToolDetail(selectedTool);

  // Hook useUserToolVersionDetail đã có enabled: !!toolId && !!versionId trong định nghĩa
  const { data: versionDetail } = useUserToolVersionDetail(selectedTool, versionToView?.id || '');

  const { mutateAsync: deleteVersion, isPending: isDeleting } = useDeleteUserToolVersion();
  const { mutateAsync: setDefaultVersion, isPending: isSettingDefault } =
    useSetDefaultUserToolVersion();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (toolDetail?.versions) {
      // Sắp xếp phiên bản theo số phiên bản giảm dần
      const sortedVersions = [...toolDetail.versions].sort(
        (a, b) => b.versionNumber - a.versionNumber
      );
      setToolVersions(sortedVersions);
    } else {
      setToolVersions([]);
    }
  }, [toolDetail]);

  // Cập nhật versionToView khi có dữ liệu chi tiết
  useEffect(() => {
    if (versionDetail && versionToView) {
      setVersionToView(versionDetail);
    }
  }, [versionDetail, versionToView]);

  // Xử lý khi người dùng click vào một tool
  const handleToolClick = useCallback((tool: ToolListItem) => {
    setSelectedTool(tool.id);
    setShowVersions(true);
  }, []);

  // Xử lý quay lại danh sách tool
  const handleBackToTools = useCallback(() => {
    setSelectedTool('');
    setShowVersions(false);
    setVersionToView(null);
    setVersionToDelete(null);
    setShowDeleteConfirm(false);
    setShowSetDefaultConfirm(false);
  }, []);

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((version: UserToolVersion) => {
    setVersionToDelete(version);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setVersionToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!selectedTool || !versionToDelete) return;

    try {
      await deleteVersion({ toolId: selectedTool, versionId: versionToDelete.id });
      setShowDeleteConfirm(false);
      setVersionToDelete(null);
    } catch (error) {
      console.error('Error deleting tool version:', error);
    }
  }, [versionToDelete, deleteVersion, selectedTool]);

  // Xử lý hiển thị popup xác nhận đặt làm mặc định
  const handleShowSetDefaultConfirm = useCallback((version: UserToolVersion) => {
    setVersionToView(version);
    setShowSetDefaultConfirm(true);
  }, []);

  // Xử lý hủy đặt làm mặc định
  const handleCancelSetDefault = useCallback(() => {
    setShowSetDefaultConfirm(false);
    setVersionToView(null);
  }, []);

  // Xử lý xác nhận đặt làm mặc định
  const handleConfirmSetDefault = useCallback(async () => {
    if (!selectedTool || !versionToView) return;

    try {
      await setDefaultVersion({ toolId: selectedTool, versionId: versionToView.id });
      setShowSetDefaultConfirm(false);
      setVersionToView(null);
    } catch (error) {
      console.error('Error setting default version:', error);
    }
  }, [versionToView, setDefaultVersion, selectedTool]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý hiển thị form xem chi tiết phiên bản
  const handleShowViewForm = useCallback(
    (version: UserToolVersion) => {
      setVersionToView(version);
      showEditForm();
    },
    [showEditForm]
  );

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Định nghĩa các cột cho bảng tool
  const toolColumns = useMemo(() => {
    return [
      {
        key: 'name',
        title: t('tools.name', 'Tên Tool'),
        dataIndex: 'name',
        width: '25%',
      },
      {
        key: 'description',
        title: t('tools.description', 'Mô tả'),
        dataIndex: 'description',
        width: '40%',
      },
      {
        key: 'createdAt',
        title: t('tools.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        render: (value: unknown) => formatDate(value as number),
      },
      {
        key: 'actions',
        title: t('common.actions', 'Thao tác'),
        width: '20%',
        render: (_: unknown, record: ToolListItem) => (
          <div className="flex space-x-2">
            <Tooltip content={t('tools.viewVersions', 'Xem phiên bản')}>
              <IconCard
                icon="list"
                variant="primary"
                size="sm"
                onClick={() => handleToolClick(record)}
              />
            </Tooltip>
          </div>
        ),
      },
    ];
  }, [t, handleToolClick]);

  // Định nghĩa các cột cho bảng phiên bản
  const versionColumns = useMemo(() => {
    return [
      {
        key: 'versionNumber',
        title: t('tools.version', 'Phiên bản'),
        dataIndex: 'versionNumber',
        width: '10%',
        render: (value: unknown, record: UserToolVersion) => (
          <div className="flex items-center">
            <span className="font-medium">v{String(value)}</span>
            {record.isFromAdmin && (
              <Badge className="ml-2" variant="primary">
                {t('tools.fromAdmin', 'Admin')}
              </Badge>
            )}
          </div>
        ),
      },
      {
        key: 'toolName',
        title: t('tools.toolName', 'Tên hiển thị'),
        dataIndex: 'toolName',
        width: '20%',
      },
      {
        key: 'status',
        title: t('tools.status', 'Trạng thái'),
        dataIndex: 'status',
        width: '15%',
        render: (value: unknown) => {
          const status = value as ToolStatus;
          let variant: 'default' | 'primary' | 'success' | 'warning' | 'danger' = 'default';

          switch (status) {
            case ToolStatus.APPROVED:
              variant = 'success';
              break;
            case ToolStatus.DRAFT:
              variant = 'warning';
              break;
            case ToolStatus.DEPRECATED:
              variant = 'danger';
              break;
          }

          return (
            <Chip size="sm" variant={variant}>
              {t(`tools.status.${status.toLowerCase()}`, status)}
            </Chip>
          );
        },
      },
      {
        key: 'isDefault',
        title: t('tools.isDefault', 'Mặc định'),
        width: '10%',
        render: (_: unknown, record: UserToolVersion) => {
          const isDefault = toolDetail?.defaultVersion?.id === record.id;
          return isDefault ? (
            <Chip size="sm" variant="primary">
              {t('tools.default', 'Mặc định')}
            </Chip>
          ) : null;
        },
      },
      {
        key: 'createdAt',
        title: t('tools.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        render: (value: unknown) => formatDate(value as number),
      },
      {
        key: 'actions',
        title: t('common.actions', 'Thao tác'),
        render: (_: unknown, record: UserToolVersion) => (
          <div className="flex space-x-2">
            <Tooltip content={t('common.view', 'Xem')}>
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => handleShowViewForm(record)}
              />
            </Tooltip>

            {toolDetail?.defaultVersion?.id !== record.id && (
              <Tooltip content={t('tools.setDefault', 'Đặt làm mặc định')}>
                <IconCard
                  icon="star"
                  variant="primary"
                  size="sm"
                  onClick={() => handleShowSetDefaultConfirm(record)}
                />
              </Tooltip>
            )}

            <Tooltip content={t('common.delete', 'Xóa')}>
              <IconCard
                icon="trash"
                variant="primary"
                size="sm"
                onClick={() => handleShowDeleteConfirm(record)}
              />
            </Tooltip>
          </div>
        ),
      },
    ];
  }, [t, handleShowDeleteConfirm, handleShowViewForm, handleShowSetDefaultConfirm, toolDetail]);

  return (
    <div>
      <div className="space-y-4">
        {showVersions && selectedTool ? (
          // Hiển thị danh sách phiên bản của tool đã chọn
          <>
            <div className="flex justify-between items-center">
              <Typography variant="h5">
                {t('tools.versions', 'Phiên bản')}: {toolDetail?.name}
              </Typography>
              <Button variant="outline" onClick={handleBackToTools}>
                {t('common.back', 'Quay lại')}
              </Button>
            </div>

            <Card className="overflow-hidden">
              {isLoadingToolDetail ? (
                <div className="p-8 text-center">
                  <p className="text-gray-500">{t('common.loading', 'Đang tải...')}</p>
                </div>
              ) : toolVersions && toolVersions.length > 0 ? (
                <Table<UserToolVersion>
                  columns={versionColumns}
                  data={toolVersions}
                  rowKey="id"
                  loading={isLoadingToolDetail}
                  sortable={false}
                  pagination={false}
                />
              ) : (
                <div className="p-8 text-center">
                  <p className="text-gray-500">{t('tools.noVersions', 'Không có phiên bản nào')}</p>
                </div>
              )}
            </Card>
          </>
        ) : (
          // Hiển thị danh sách tool
          <>
            <div className="flex justify-between items-center">
              <div className="flex-grow">
                <MenuIconBar
                  onColumnVisibilityChange={handleColumnVisibilityChange}
                  columns={visibleColumns}
                  showDateFilter={true}
                  showColumnFilter={true}
                  onSearch={() => {}}
                  items={[]}
                />
              </div>
            </div>

            <Card className="overflow-hidden">
              {isLoadingTools ? (
                <div className="p-8 text-center">
                  <p className="text-gray-500">{t('common.loading', 'Đang tải...')}</p>
                </div>
              ) : toolsData?.items && toolsData.items.length > 0 ? (
                <Table
                  columns={toolColumns}
                  data={toolsData.items}
                  rowKey="id"
                  loading={isLoadingTools}
                  sortable={false}
                  pagination={false}
                />
              ) : (
                <div className="p-8 text-center">
                  <p className="text-gray-500">{t('tools.noTools', 'Không có tool nào')}</p>
                </div>
              )}
            </Card>
          </>
        )}
      </div>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        footer={
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleCancelDelete} disabled={isDeleting}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="danger" onClick={handleConfirmDelete} disabled={isDeleting}>
              {isDeleting ? t('common.processing', 'Đang xử lý...') : t('common.delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <p>
          {t(
            'tools.confirmDeleteVersionMessage',
            'Bạn có chắc chắn muốn xóa phiên bản tool này? Hành động này không thể hoàn tác.'
          )}
        </p>
        {versionToDelete && (
          <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded">
            <p className="font-medium">
              {versionToDelete.toolName} (v{versionToDelete.versionNumber})
            </p>
          </div>
        )}
      </Modal>

      {/* Modal xác nhận đặt làm mặc định */}
      <Modal
        isOpen={showSetDefaultConfirm}
        onClose={handleCancelSetDefault}
        title={t('tools.confirmSetDefault', 'Xác nhận đặt làm mặc định')}
        footer={
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleCancelSetDefault} disabled={isSettingDefault}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="primary" onClick={handleConfirmSetDefault} disabled={isSettingDefault}>
              {isSettingDefault
                ? t('common.processing', 'Đang xử lý...')
                : t('common.confirm', 'Xác nhận')}
            </Button>
          </div>
        }
      >
        <p>
          {t(
            'tools.confirmSetDefaultMessage',
            'Bạn có chắc chắn muốn đặt phiên bản này làm mặc định cho tool?'
          )}
        </p>
        {versionToView && (
          <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded">
            <p className="font-medium">
              {versionToView.toolName} (v{versionToView.versionNumber})
            </p>
          </div>
        )}
      </Modal>

      {/* SlideInForm để xem chi tiết phiên bản */}
      <SlideInForm isVisible={isEditFormVisible} className="w-full max-w-lg">
        {renderVersionDetailContent()}
      </SlideInForm>
    </div>
  );
};

export default ToolVersionsPage;
