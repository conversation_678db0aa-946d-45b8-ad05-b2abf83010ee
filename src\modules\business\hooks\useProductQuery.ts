import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import ProductService from '../services/product.service';
import {
  ProductQueryParams,
  CreateProductDto,
  UpdateProductDto,
} from '../types/product.types';

// Định nghĩa các query key
export const PRODUCT_QUERY_KEYS = {
  all: ['products'] as const,
  lists: () => [...PRODUCT_QUERY_KEYS.all, 'list'] as const,
  list: (filters: ProductQueryParams) => [...PRODUCT_QUERY_KEYS.lists(), filters] as const,
  details: () => [...PRODUCT_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...PRODUCT_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách sản phẩm
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useProducts = (params?: ProductQueryParams) => {
  return useQuery({
    queryKey: PRODUCT_QUERY_KEYS.list(params || {}),
    queryFn: () => ProductService.getProducts(params),
    select: (data) => {
      return {
        items: data.items,
        meta: {
          totalItems: data.meta.totalItems,
          itemCount: data.meta.itemCount,
          itemsPerPage: data.meta.itemsPerPage,
          totalPages: data.meta.totalPages,
          currentPage: data.meta.currentPage,
        },
      };
    },
  });
};

/**
 * Hook để lấy chi tiết sản phẩm
 * @param id ID của sản phẩm
 * @returns Query object
 */
export const useProduct = (id?: number) => {
  return useQuery({
    queryKey: PRODUCT_QUERY_KEYS.detail(id || 0),
    queryFn: () => ProductService.getProductById(id || 0),
    enabled: !!id, // Chỉ gọi API khi có ID
  });
};

/**
 * Hook để tạo sản phẩm mới
 * @returns Mutation object
 */
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductDto) => ProductService.createProduct(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách sản phẩm
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để cập nhật sản phẩm
 * @returns Mutation object
 */
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateProductDto }) =>
      ProductService.updateProduct(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết sản phẩm và danh sách sản phẩm
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa sản phẩm
 * @returns Mutation object
 */
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => ProductService.deleteProduct(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách sản phẩm
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa nhiều sản phẩm
 * @returns Mutation object
 */
export const useDeleteMultipleProducts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: number[]) => ProductService.deleteMultipleProducts(ids),
    onSuccess: () => {
      // Invalidate và refetch danh sách sản phẩm
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để lấy URL presigned cho upload hình ảnh
 * @returns Mutation object
 */
export const useGetPresignedUrl = () => {
  return useMutation({
    mutationFn: (mimeType: string) => ProductService.getPresignedUrl(mimeType),
  });
};

/**
 * Hook tổng hợp cho quản lý dữ liệu sản phẩm
 * @returns Các hooks liên quan đến sản phẩm
 */
export const useProductData = () => {
  return {
    useProducts,
    useProduct,
    useCreateProduct,
    useUpdateProduct,
    useDeleteProduct,
    useDeleteMultipleProducts,
    useGetPresignedUrl,
  };
};

export default useProductData;
