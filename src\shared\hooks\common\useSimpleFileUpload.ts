/**
 * Hook đơn giản để upload file lên URL tạm thời mà không phụ thuộc vào TaskQueueContext
 */
import { useCallback } from 'react';

/**
 * Tham số cho hàm uploadToUrl
 */
export interface UploadToUrlParams {
  /**
   * URL tạm thời để upload file
   */
  presignedUrl: string;

  /**
   * File cần upload
   */
  file: File;

  /**
   * Callback khi upload thành công
   */
  onSuccess?: (result: unknown) => void;

  /**
   * Callback khi upload thất bại
   */
  onError?: (error: Error) => void;

  /**
   * Callback khi tiến trình upload thay đổi
   */
  onProgress?: (progress: number) => void;
}

/**
 * Hook đơn giản để upload file lên URL tạm thời mà không phụ thuộc vào TaskQueueContext
 * @returns Hàm để upload file
 */
export function useSimpleFileUpload() {
  /**
   * Upload file lên URL tạm thời
   * @param params Tham số cho việc upload
   * @returns Promise với kết quả upload
   */
  const uploadToUrl = useCallback(
    async (params: UploadToUrlParams): Promise<unknown> => {
      const { presignedUrl, file, onSuccess, onError, onProgress } = params;

      try {
        // Tạo XMLHttpRequest để theo dõi tiến trình
        const xhr = new XMLHttpRequest();

        // Tạo Promise để xử lý kết quả
        const uploadPromise = new Promise<unknown>((resolve, reject) => {
          // Xử lý sự kiện khi upload hoàn thành
          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              const result = {
                success: true,
                status: xhr.status,
                url: presignedUrl,
                fileName: file.name,
                fileSize: file.size,
              };
              
              if (onSuccess) {
                onSuccess(result);
              }
              
              resolve(result);
            } else {
              const error = new Error(
                `Upload thất bại với mã lỗi ${xhr.status}: ${xhr.statusText || 'Không có thông tin lỗi'}`
              );
              
              if (onError) {
                onError(error);
              }
              
              reject(error);
            }
          };

          // Xử lý sự kiện khi upload thất bại
          xhr.onerror = () => {
            const error = new Error('Lỗi kết nối khi upload file');
            
            if (onError) {
              onError(error);
            }
            
            reject(error);
          };

          // Xử lý sự kiện khi upload bị hủy
          xhr.onabort = () => {
            const error = new Error('Upload đã bị hủy');
            
            if (onError) {
              onError(error);
            }
            
            reject(error);
          };

          // Xử lý sự kiện khi tiến trình upload thay đổi
          xhr.upload.onprogress = (event) => {
            if (event.lengthComputable && onProgress) {
              const progress = Math.round((event.loaded / event.total) * 100);
              onProgress(progress);
            }
          };
        });

        // Mở kết nối
        xhr.open('PUT', presignedUrl, true);

        // Gửi file
        xhr.send(file);

        // Trả về kết quả
        return await uploadPromise;
      } catch (error) {
        console.error('Lỗi khi upload file:', error);
        
        if (onError && error instanceof Error) {
          onError(error);
        }
        
        throw error;
      }
    },
    []
  );

  return {
    uploadToUrl,
  };
}

export default useSimpleFileUpload;
