import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getProviderModels,
  getProviderModelById,
  createProviderModel,
  updateProviderModel,
  deleteProviderModel,
} from '../services/provider-model.service';
import { ProviderModelQueryKeys } from '../constants/provider-model-query-key';
import {
  CreateProviderModelDto,
  UpdateProviderModelDto,
  ProviderModelQueryParams,
} from '../types/provider-model.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

/**
 * Interface cho lỗi API
 */
interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
  message?: string;
}

/**
 * Hook để lấy danh sách provider model
 */
export const useProviderModels = (params: ProviderModelQueryParams = {}) => {
  return useQuery({
    queryKey: ProviderModelQueryKeys.list(params),
    queryFn: () => getProviderModels(params),
  });
};

/**
 * Hook để lấy thông tin chi tiết của provider model
 */
export const useProviderModelDetail = (id: string) => {
  return useQuery({
    queryKey: ProviderModelQueryKeys.detail(id),
    queryFn: () => getProviderModelById(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo provider model mới
 */
export const useCreateProviderModel = () => {
  const queryClient = useQueryClient();
  const notification = useSmartNotification();

  return useMutation({
    mutationFn: (data: CreateProviderModelDto) => createProviderModel(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ProviderModelQueryKeys.lists() });
      notification.success({ message: 'Tạo provider model thành công' });
    },
    onError: (error: ApiError) => {
      notification.error({
        message: error.response?.data?.message || 'Đã xảy ra lỗi khi tạo provider model'
      });
    },
  });
};

/**
 * Hook để cập nhật thông tin provider model
 */
export const useUpdateProviderModel = (id: string) => {
  const queryClient = useQueryClient();
  const notification = useSmartNotification();

  return useMutation({
    mutationFn: (data: UpdateProviderModelDto) => updateProviderModel(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ProviderModelQueryKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: ProviderModelQueryKeys.lists() });
      notification.success({ message: 'Cập nhật provider model thành công' });
    },
    onError: (error: ApiError) => {
      notification.error({
        message: error.response?.data?.message || 'Đã xảy ra lỗi khi cập nhật provider model'
      });
    },
  });
};

/**
 * Hook để xóa provider model
 */
export const useDeleteProviderModel = () => {
  const queryClient = useQueryClient();
  const notification = useSmartNotification();

  return useMutation({
    mutationFn: (id: string) => deleteProviderModel(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ProviderModelQueryKeys.lists() });
      notification.success({ message: 'Xóa provider model thành công' });
    },
    onError: (error: ApiError) => {
      notification.error({
        message: error.response?.data?.message || 'Đã xảy ra lỗi khi xóa provider model'
      });
    },
  });
};
