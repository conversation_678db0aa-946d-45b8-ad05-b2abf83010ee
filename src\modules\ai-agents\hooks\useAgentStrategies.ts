import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Strategy } from '../types/agent.types';
import { AGENT_QUERY_KEYS } from './useAgentDetail';

/**
 * <PERSON><PERSON>u dữ liệu cho tham số chiến lược
 */
type StrategyParameterValue = string | number | boolean;

/**
 * Đối tượng tham số chiến lược
 */
interface StrategyParameters {
  [key: string]: StrategyParameterValue;
}

// Mock data service - sẽ được thay thế bằng API thực tế
const StrategyService = {
  getStrategies: async (): Promise<{ result: Strategy[] }> => {
    // Gi<PERSON> lập API call
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          result: [
            {
              id: '1',
              name: 'Sales Strategy',
              description: 'Optimize for sales conversion',
              parameters: [
                {
                  id: '1',
                  name: 'aggressiveness',
                  type: 'number',
                  value: 7,
                  min: 1,
                  max: 10,
                  step: 1,
                },
                {
                  id: '2',
                  name: 'followUpFrequency',
                  type: 'select',
                  value: 'medium',
                  options: [
                    { label: 'Low', value: 'low' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'High', value: 'high' },
                  ],
                },
              ],
            },
            {
              id: '2',
              name: 'Support Strategy',
              description: 'Optimize for customer support',
              parameters: [
                {
                  id: '1',
                  name: 'responseTime',
                  type: 'number',
                  value: 5,
                  min: 1,
                  max: 10,
                  step: 1,
                },
                {
                  id: '2',
                  name: 'supportLevel',
                  type: 'select',
                  value: 'standard',
                  options: [
                    { label: 'Basic', value: 'basic' },
                    { label: 'Standard', value: 'standard' },
                    { label: 'Premium', value: 'premium' },
                  ],
                },
              ],
            },
            {
              id: '3',
              name: 'Marketing Strategy',
              description: 'Optimize for marketing campaigns',
              parameters: [
                {
                  id: '1',
                  name: 'targetAudience',
                  type: 'select',
                  value: 'general',
                  options: [
                    { label: 'General', value: 'general' },
                    { label: 'Young Adults', value: 'young_adults' },
                    { label: 'Professionals', value: 'professionals' },
                    { label: 'Seniors', value: 'seniors' },
                  ],
                },
                {
                  id: '2',
                  name: 'campaignIntensity',
                  type: 'number',
                  value: 6,
                  min: 1,
                  max: 10,
                  step: 1,
                },
              ],
            },
          ],
        });
      }, 500);
    });
  },
  updateAgentStrategy: async (
    _agentId: string,
    strategyId: string,
    parameters: StrategyParameters
  ): Promise<{ result: Strategy }> => {
    // Giả lập API call
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          result: {
            id: strategyId,
            name: 'Updated Strategy',
            description: 'Updated strategy description',
            parameters: Object.entries(parameters).map(([key, value], index) => ({
              id: String(index + 1),
              name: key,
              type: typeof value === 'number' ? 'number' : 'select',
              value,
            })),
          },
        });
      }, 500);
    });
  },
};

// Query keys
export const STRATEGY_QUERY_KEYS = {
  all: ['strategies'],
};

/**
 * Hook để lấy danh sách chiến lược
 */
export const useStrategies = () => {
  return useQuery({
    queryKey: STRATEGY_QUERY_KEYS.all,
    queryFn: () => StrategyService.getStrategies(),
    select: data => data.result,
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook để cập nhật chiến lược của Agent
 */
export const useUpdateAgentStrategy = (agentId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      strategyId,
      parameters,
    }: {
      strategyId: string;
      parameters: StrategyParameters;
    }) => StrategyService.updateAgentStrategy(agentId, strategyId, parameters),
    onSuccess: () => {
      // Invalidate và refetch
      queryClient.invalidateQueries({ queryKey: AGENT_QUERY_KEYS.detail(agentId) });
    },
  });
};
