import { z } from 'zod';
import { ProviderType } from '../types/provider.types';

/**
 * Schema cho phản hồi API
 */
export const ApiResponseSchema = <T extends z.ZodTypeAny>(resultSchema: T) =>
  z.object({
    code: z.number(),
    message: z.string(),
    result: resultSchema,
  });

/**
 * Schema cho thông tin nhà cung cấp
 */
export const ProviderSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Tên nhà cung cấp không được để trống'),
  type: z.nativeEnum(ProviderType),
  apiKey: z.string().min(1, 'API key không được để trống'),
  createdAt: z.number().optional(),
  updatedAt: z.number().optional(),
});

/**
 * Schema cho tạo nhà cung cấp mới
 */
export const CreateProviderSchema = z.object({
  name: z.string().min(1, 'Tên nhà cung cấp không được để trống'),
  type: z.nativeEnum(ProviderType),
  apiKey: z.string().min(1, 'API key không được để trống'),
});

/**
 * Schema cho cập nhật nhà cung cấp
 */
export const UpdateProviderSchema = z.object({
  name: z.string().min(1, 'Tên nhà cung cấp không được để trống').optional(),
  apiKey: z.string().min(1, 'API key không được để trống').optional(),
});

/**
 * Schema cho danh sách nhà cung cấp
 */
export const ProviderListSchema = z.array(ProviderSchema);

/**
 * Schema cho kết quả phân trang
 */
export const PaginatedProviderSchema = z.object({
  items: z.array(ProviderSchema),
  meta: z.object({
    totalItems: z.number(),
    itemCount: z.number(),
    itemsPerPage: z.number(),
    totalPages: z.number(),
    currentPage: z.number(),
  }),
});
