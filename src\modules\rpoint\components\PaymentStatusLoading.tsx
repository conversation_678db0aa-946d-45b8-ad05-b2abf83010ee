import React from 'react';
import { Typography } from '@/shared/components/common';

interface PaymentStatusLoadingProps {
  /**
   * Text hiển thị bên cạnh loading
   */
  text?: string;
  
  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị trạng thái loading cho thanh toán
 * với hiệu ứng đẹp mắt hơn
 */
const PaymentStatusLoading: React.FC<PaymentStatusLoadingProps> = ({ 
  text = 'Đang kiểm tra thanh toán', 
  className = '' 
}) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Hiệu ứng loading với 3 chấm */}
      <div className="flex items-center">
        <div className="flex space-x-1">
          <div className="h-2 w-2 rounded-full bg-primary animate-pulse"></div>
          <div className="h-2 w-2 rounded-full bg-primary animate-pulse delay-150"></div>
          <div className="h-2 w-2 rounded-full bg-primary animate-pulse delay-300"></div>
        </div>
      </div>
      
      {/* Text */}
      <Typography variant="body2" className="font-semibold text-warning">
        {text}
      </Typography>
    </div>
  );
};

export default PaymentStatusLoading;
