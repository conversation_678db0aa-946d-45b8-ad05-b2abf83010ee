import React from 'react';
import { Card, IconCard, ProgressBar, <PERSON>lt<PERSON>, Chip } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AIAgent } from '../data/agents';

interface AgentCardProps {
  agent: AIAgent;
}

/**
 * Component hiển thị thông tin của một AI Agent
 */
const AgentCard: React.FC<AgentCardProps> = ({ agent }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleViewDetails = () => {
    navigate(`/ai-agents/${agent.id}`);
  };

  const handleEditAgent = () => {
    navigate(`/ai-agents/${agent.id}/edit`);
  };

  const [isActive, setIsActive] = React.useState(agent.isActive);

  const handleToggleActive = () => {
    setIsActive(!isActive);
    console.log(`Toggle active state for agent: ${agent.id}, new state: ${!isActive}`);
    // Xử lý logic bật/tắt agent
  };

  // Tính toán phần trăm kinh nghiệm
  const experiencePercent = Math.round((agent.experience / agent.experienceToNextLevel) * 100);

  // Xác định variant cho model chip dựa trên nhà cung cấp
  const getModelVariant = (
    model: string
  ): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
    const modelLower = model.toLowerCase();

    // OpenAI models
    if (modelLower.includes('gpt')) return 'danger';

    // Anthropic models
    if (modelLower.includes('claude')) return 'success';

    // Google models
    if (modelLower.includes('gemini')) return 'info';

    // DeepSeek models
    if (modelLower.includes('deepseek')) return 'warning';

    // Mistral models
    if (modelLower.includes('mistral')) return 'primary';

    // Llama models
    if (modelLower.includes('llama')) return 'info';

    // Default for other models
    return 'primary';
  };

  // Không cần xác định layout vì đã cố định

  return (
    <Card
      className="h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
      variant="elevated"
    >
      <div className="p-4">
        <div className="flex flex-col space-y-4">
          {/* Hàng 1: Avatar, tên, loại agent và model */}
          <div className="flex items-center gap-3 overflow-hidden">
            {/* Avatar và khung level */}
            <div className="relative w-16 h-16 sm:w-20 sm:h-20 flex-shrink-0">
              <div className="w-full h-full relative">
                <img
                  src="/assets/images/frame-level-agents.png"
                  alt="Level frame"
                  className="absolute inset-0 w-full h-full object-contain z-10"
                />
                <div className="absolute inset-0 flex items-center justify-center z-0">
                  <img
                    src={agent.avatar}
                    alt={agent.name}
                    className="w-[75%] h-[75%] rounded-full"
                  />
                </div>
                {/* Chỉ báo trạng thái active */}
                <div
                  className={`absolute bottom-1 right-1 w-3 h-3 rounded-full z-20 ${
                    isActive ? 'bg-green-500 dark:bg-green-400' : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                />
              </div>
            </div>

            {/* Thông tin agent: tên, loại và model */}
            <div className="flex flex-col min-w-0 flex-grow">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
                <div className="min-w-0">
                  <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                    {agent.name}
                  </h3>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {agent.type === 'assistant' ? 'AI Assistant' : 'AI Agent'}
                  </div>
                </div>
                <div className="flex-shrink-0 mt-1 sm:mt-0">
                  <Chip
                    variant={getModelVariant(agent.model)}
                    size="sm"
                    className="font-normal max-w-full truncate"
                  >
                    {agent.model}
                  </Chip>
                </div>
              </div>
            </div>
          </div>

          {/* Hàng 2: Level/exp và các nút chức năng */}
          <div className="flex flex-col">
            {/* Thông tin level và exp */}
            <div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  Cấp độ: {agent.level}
                </span>
                <Chip variant="danger" size="sm" className="font-medium text-xs">
                  {experiencePercent}%
                </Chip>
              </div>
              <ProgressBar
                value={experiencePercent}
                size="sm"
                color="primary"
                gradient
                rounded
                className="mb-3"
              />
            </div>

            {/* Các nút chức năng */}
            <div className="flex justify-end space-x-6">
              <Tooltip
                content={isActive ? t('common.deactivate') : t('common.activate')}
                position="top"
              >
                <IconCard
                  icon="power"
                  variant={isActive ? 'primary' : 'default'}
                  size="md"
                  onClick={handleToggleActive}
                  className={isActive ? 'text-green-500' : 'text-gray-400'}
                />
              </Tooltip>
              <Tooltip content={t('common.edit')} position="top">
                <IconCard icon="edit" variant="default" size="md" onClick={handleEditAgent} />
              </Tooltip>
              <Tooltip content={t('common.details')} position="top">
                <IconCard icon="eye" variant="default" size="md" onClick={handleViewDetails} />
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default AgentCard;
