import { useCallback } from 'react';
import { useChatPanel } from '@/shared/contexts/chat-panel';
import useChatNotification from './useChatNotification';
import { NotificationUtil } from '@/shared/utils/notification';
import { NotificationType } from '@/shared/components/common/Notification/Notification';

interface NotificationOptions {
  title?: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * Hook thông minh để hiển thị thông báo
 * - Nếu chat panel đang mở: Sử dụng useChatNotification để hiển thị thông báo trong chat panel
 * - Nếu chat panel đang đóng: Sử dụng NotificationUtil để hiển thị thông báo popup
 * 
 * @returns Các phương thức để hiển thị thông báo: success, error, warning, info
 * 
 * @example
 * const { success, error, warning, info } = useSmartNotification();
 * 
 * // Hiển thị thông báo thành công
 * success({ title: 'Thành công', message: '<PERSON><PERSON> tác đã được thực hiện thành công' });
 * 
 * // Hiển thị thông báo lỗi
 * error({ title: 'Lỗi', message: 'Đã xảy ra lỗi khi thực hiện thao tác' });
 */
const useSmartNotification = () => {
  const { isChatPanelOpen } = useChatPanel();
  const chatNotification = useChatNotification();

  /**
   * Hiển thị thông báo dựa vào trạng thái của chat panel
   * @param type Loại thông báo
   * @param options Tùy chọn thông báo
   * @returns ID của thông báo (nếu sử dụng chat notification)
   */
  const showNotification = useCallback(
    (type: NotificationType, options: NotificationOptions) => {
      const { title, message} = options;

      if (isChatPanelOpen) {
        // Nếu chat panel đang mở, sử dụng chat notification
        return chatNotification.showNotification(type, title ? `${title}: ${message}` : message);
      } else {
        // Nếu chat panel đang đóng, sử dụng notification util
        NotificationUtil[type](options);
        return null; // NotificationUtil không trả về ID
      }
    },
    [isChatPanelOpen, chatNotification]
  );

  /**
   * Hiển thị thông báo thành công
   * @param options Tùy chọn thông báo
   * @returns ID của thông báo (nếu sử dụng chat notification)
   */
  const success = useCallback(
    (options: NotificationOptions) => {
      return showNotification('success', options);
    },
    [showNotification]
  );

  /**
   * Hiển thị thông báo lỗi
   * @param options Tùy chọn thông báo
   * @returns ID của thông báo (nếu sử dụng chat notification)
   */
  const error = useCallback(
    (options: NotificationOptions) => {
      return showNotification('error', options);
    },
    [showNotification]
  );

  /**
   * Hiển thị thông báo cảnh báo
   * @param options Tùy chọn thông báo
   * @returns ID của thông báo (nếu sử dụng chat notification)
   */
  const warning = useCallback(
    (options: NotificationOptions) => {
      return showNotification('warning', options);
    },
    [showNotification]
  );

  /**
   * Hiển thị thông báo thông tin
   * @param options Tùy chọn thông báo
   * @returns ID của thông báo (nếu sử dụng chat notification)
   */
  const info = useCallback(
    (options: NotificationOptions) => {
      return showNotification('info', options);
    },
    [showNotification]
  );

  return {
    success,
    error,
    warning,
    info,
  };
};

export default useSmartNotification;
