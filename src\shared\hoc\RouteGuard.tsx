import React, { ComponentType } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthCommon } from '@/shared/hooks/useAuthCommon';
import { RouteGuardType } from './types';

/**
 * Props cho component RouteGuard
 */
interface RouteGuardProps {
  /**
   * Component cần bảo vệ
   */
  component: ComponentType<Record<string, unknown>>;

  /**
   * Loại bảo vệ route
   * - PROTECT: Y<PERSON><PERSON> cầu người dùng đã đăng nhập
   * - ADMIN: Yêu cầu admin đã đăng nhập
   * - PUBLIC: Không yêu cầu đăng nhập
   */
  type: RouteGuardType;

  /**
   * Đường dẫn chuyển hướng khi không có quyền truy cập
   */
  redirectTo?: string;
}

/**
 * Component bảo vệ route dựa trên loại người dùng
 * @param props Props của component
 * @returns JSX.Element
 */
export const RouteGuard: React.FC<RouteGuardProps> = ({
  component: Component,
  type,
  redirectTo
}) => {
  const {
    isUserAuthenticated,
    isAdminAuthenticated
  } = useAuthCommon();
  const location = useLocation();

  // Kiểm tra quyền truy cập
  const hasAccess = (): boolean => {
    switch (type) {
      case 'PROTECT':
        return isUserAuthenticated;
      case 'ADMIN':
        return isAdminAuthenticated;
      case 'PUBLIC':
        return true;
      default:
        return false;
    }
  };

  // Nếu có quyền truy cập, hiển thị component
  if (hasAccess()) {
    return <Component />;
  }

  // Xác định đường dẫn chuyển hướng mặc định nếu không được cung cấp
  const defaultRedirectPath = type === 'ADMIN'
    ? '/admin/auth/login'
    : '/auth/login';

  // Chuyển hướng đến đường dẫn đã xác định hoặc mặc định
  return <Navigate to={redirectTo || defaultRedirectPath} replace state={{ from: location }} />;
};

// withRouteGuard đã được chuyển sang file routeGuardUtils.ts

export default RouteGuard;
