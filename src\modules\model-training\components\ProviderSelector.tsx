import React from 'react';
import { Card, Typography } from '@/shared/components/common';
import { ProviderType } from '../types/provider.types';

interface ProviderOption {
  type: ProviderType;
  name: string;
}

interface ProviderSelectorProps {
  selectedProvider: ProviderType | null;
  onSelectProvider: (provider: ProviderType) => void;
}

/**
 * Component cho phép chọn nhà cung cấp AI
 */
const ProviderSelector: React.FC<ProviderSelectorProps> = ({
  selectedProvider,
  onSelectProvider,
}) => {
  const providers: (ProviderOption & { color: string })[] = [
    { type: ProviderType.OPENAI, name: 'OpenAI', color: 'bg-gradient-to-r from-green-400 to-blue-500' },
    { type: ProviderType.GOOGLE, name: 'Google', color: 'bg-gradient-to-r from-blue-400 to-purple-500' },
    { type: ProviderType.XAI, name: 'XAI', color: 'bg-gradient-to-r from-purple-400 to-pink-500' },
    { type: ProviderType.META, name: 'Meta', color: 'bg-gradient-to-r from-blue-500 to-indigo-600' },
    { type: ProviderType.DEEPSEEK, name: 'DEEPSEEK', color: 'bg-gradient-to-r from-yellow-400 to-orange-500' },
    { type: ProviderType.ANTHROPIC, name: 'Anthropic', color: 'bg-gradient-to-r from-pink-400 to-red-500' },
  ];

  return (
    <div>
      <div className="overflow-x-auto -mx-2 px-2">
        <div className="flex space-x-6 min-w-max py-2">
          {providers.map((provider) => (
            <div
              key={provider.type}
              className={`relative group cursor-pointer transition-all transform hover:scale-105 ${
                selectedProvider === provider.type ? 'scale-105' : ''
              }`}
              onClick={() => onSelectProvider(provider.type)}
            >
              <div
                className={`absolute inset-0 rounded-xl ${provider.color} opacity-20 group-hover:opacity-30 transition-opacity ${
                  selectedProvider === provider.type ? 'opacity-30' : ''
                }`}
              />
              <Card
                className={`relative text-center min-w-[120px] shadow-sm hover:shadow-md transition-shadow ${
                  selectedProvider === provider.type
                    ? 'border-primary-500 border-2 shadow-md'
                    : 'border border-gray-200 hover:border-gray-300'
                }`}
              >
                <Typography variant="body1" className="font-medium">
                  {provider.name}
                </Typography>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProviderSelector;
