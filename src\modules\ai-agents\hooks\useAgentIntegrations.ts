import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { FacebookPage, Website } from '../types/agent.types';
import { AGENT_QUERY_KEYS } from './useAgentDetail';

// Mock data service - sẽ được thay thế bằng API thực tế
const IntegrationService = {
  // Facebook Pages
  getFacebookPages: async (search?: string): Promise<{ result: FacebookPage[] }> => {
    // Giả lập API call
    return new Promise(resolve => {
      setTimeout(() => {
        const pages: FacebookPage[] = [
          {
            id: '1',
            name: 'Business Page',
            imageUrl: 'https://randomuser.me/api/portraits/men/2.jpg',
            isConnected: true,
          },
          {
            id: '2',
            name: 'Product Showcase',
            imageUrl: 'https://randomuser.me/api/portraits/women/2.jpg',
            isConnected: false,
          },
          {
            id: '3',
            name: 'Customer Support',
            imageUrl: 'https://randomuser.me/api/portraits/men/3.jpg',
            isConnected: false,
          },
          {
            id: '4',
            name: 'Marketing Campaigns',
            imageUrl: 'https://randomuser.me/api/portraits/women/4.jpg',
            isConnected: false,
          },
          {
            id: '5',
            name: 'Event Promotions',
            imageUrl: 'https://randomuser.me/api/portraits/men/5.jpg',
            isConnected: false,
          },
        ];

        if (search) {
          const filteredPages = pages.filter(page =>
            page.name.toLowerCase().includes(search.toLowerCase())
          );
          resolve({ result: filteredPages });
        } else {
          resolve({ result: pages });
        }
      }, 300);
    });
  },
  connectFacebookPages: async (
    _agentId: string,
    pageIds: string[]
  ): Promise<{ result: FacebookPage[] }> => {
    // Giả lập API call
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          result: pageIds.map(id => ({
            id,
            name: `Page ${id}`,
            imageUrl: `https://randomuser.me/api/portraits/men/${Number(id) % 10}.jpg`,
            isConnected: true,
          })),
        });
      }, 500);
    });
  },
  disconnectFacebookPage: async (
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _agentId: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _pageId: string
  ): Promise<{ result: { success: boolean } }> => {
    // Giả lập API call
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({ result: { success: true } });
      }, 500);
    });
  },

  // Websites
  getWebsites: async (search?: string): Promise<{ result: Website[] }> => {
    // Giả lập API call
    return new Promise(resolve => {
      setTimeout(() => {
        const websites: Website[] = [
          {
            id: '1',
            url: 'https://example.com',
            name: 'Company Website',
            favicon: 'https://example.com/favicon.ico',
            isConnected: true,
          },
          {
            id: '2',
            url: 'https://blog.example.com',
            name: 'Company Blog',
            favicon: 'https://blog.example.com/favicon.ico',
            isConnected: false,
          },
          {
            id: '3',
            url: 'https://shop.example.com',
            name: 'Online Shop',
            favicon: 'https://shop.example.com/favicon.ico',
            isConnected: false,
          },
          {
            id: '4',
            url: 'https://support.example.com',
            name: 'Support Portal',
            favicon: 'https://support.example.com/favicon.ico',
            isConnected: false,
          },
          {
            id: '5',
            url: 'https://docs.example.com',
            name: 'Documentation',
            favicon: 'https://docs.example.com/favicon.ico',
            isConnected: false,
          },
        ];

        if (search) {
          const filteredWebsites = websites.filter(
            website =>
              website.name.toLowerCase().includes(search.toLowerCase()) ||
              website.url.toLowerCase().includes(search.toLowerCase())
          );
          resolve({ result: filteredWebsites });
        } else {
          resolve({ result: websites });
        }
      }, 300);
    });
  },
  connectWebsites: async (
    _agentId: string,
    websiteIds: string[]
  ): Promise<{ result: Website[] }> => {
    // Giả lập API call
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          result: websiteIds.map(id => ({
            id,
            url: `https://example${id}.com`,
            name: `Website ${id}`,
            favicon: `https://example${id}.com/favicon.ico`,
            isConnected: true,
          })),
        });
      }, 500);
    });
  },
  disconnectWebsite: async (
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _agentId: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _websiteId: string
  ): Promise<{ result: { success: boolean } }> => {
    // Giả lập API call
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({ result: { success: true } });
      }, 500);
    });
  },
};

// Query keys
export const INTEGRATION_QUERY_KEYS = {
  facebookPages: (search?: string) => ['facebook-pages', search],
  websites: (search?: string) => ['websites', search],
};

/**
 * Hook để lấy danh sách Facebook Pages
 */
export const useFacebookPages = (search?: string) => {
  return useQuery({
    queryKey: INTEGRATION_QUERY_KEYS.facebookPages(search),
    queryFn: () => IntegrationService.getFacebookPages(search),
    select: data => data.result,
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook để kết nối Facebook Pages với Agent
 */
export const useConnectFacebookPages = (agentId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (pageIds: string[]) => IntegrationService.connectFacebookPages(agentId, pageIds),
    onSuccess: () => {
      // Invalidate và refetch
      queryClient.invalidateQueries({ queryKey: AGENT_QUERY_KEYS.detail(agentId) });
    },
  });
};

/**
 * Hook để ngắt kết nối Facebook Page khỏi Agent
 */
export const useDisconnectFacebookPage = (agentId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (pageId: string) => IntegrationService.disconnectFacebookPage(agentId, pageId),
    onSuccess: () => {
      // Invalidate và refetch
      queryClient.invalidateQueries({ queryKey: AGENT_QUERY_KEYS.detail(agentId) });
    },
  });
};

/**
 * Hook để lấy danh sách Websites
 */
export const useWebsites = (search?: string) => {
  return useQuery({
    queryKey: INTEGRATION_QUERY_KEYS.websites(search),
    queryFn: () => IntegrationService.getWebsites(search),
    select: data => data.result,
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook để kết nối Websites với Agent
 */
export const useConnectWebsites = (agentId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (websiteIds: string[]) => IntegrationService.connectWebsites(agentId, websiteIds),
    onSuccess: () => {
      // Invalidate và refetch
      queryClient.invalidateQueries({ queryKey: AGENT_QUERY_KEYS.detail(agentId) });
    },
  });
};

/**
 * Hook để ngắt kết nối Website khỏi Agent
 */
export const useDisconnectWebsite = (agentId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (websiteId: string) => IntegrationService.disconnectWebsite(agentId, websiteId),
    onSuccess: () => {
      // Invalidate và refetch
      queryClient.invalidateQueries({ queryKey: AGENT_QUERY_KEYS.detail(agentId) });
    },
  });
};
