import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiCreatedResponse,
  ApiOkResponse,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { UserFolderService } from '@modules/business/user/services';
import {
  CreateFolderDto,
  UpdateFolderDto,
  FolderResponseDto,
  FolderDetailResponseDto,
  QueryFolderDto,
} from '../dto/folder';
import { ApiResponseDto } from '@/common/response';
import { SwaggerApiTag } from '@common/swagger';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ErrorCode } from '@common/exceptions';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';

/**
 * Controller xử lý các endpoint liên quan đến thư mục cho người dùng
 */
@Controller('user/folders')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SwaggerApiTag.USER_WAREHOUSE_VIRTUAL_FOLDERS)
export class UserFolderController {
  constructor(private readonly userFolderService: UserFolderService) {}

  /**
   * Lấy danh sách thư mục với phân trang và lọc
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách thư mục với phân trang và lọc' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách thư mục với phân trang',
    schema: ApiResponseDto.getPaginatedSchema(FolderResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FOLDER_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getFolders(@Query() queryDto: QueryFolderDto, @CurrentUser() userId: JwtPayload) {
    const folders = await this.userFolderService.getFolders(queryDto, userId);
    return ApiResponseDto.success(folders, 'Lấy danh sách thư mục thành công');
  }

  /**
   * Lấy danh sách thư mục gốc (không có thư mục cha)
   */
  @Get('root')
  @ApiOperation({ summary: 'Lấy danh sách thư mục gốc (không có thư mục cha)' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách thư mục gốc',
    schema: ApiResponseDto.getSchema([FolderResponseDto]),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FOLDER_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getRootFolders(@CurrentUser() userId: JwtPayload) {
    const folders = await this.userFolderService.getRootFolders(userId);
    return ApiResponseDto.success(folders, 'Lấy danh sách thư mục gốc thành công');
  }

  /**
   * Lấy danh sách thư mục con của một thư mục
   */
  @Get('children/:parentId')
  @ApiOperation({ summary: 'Lấy danh sách thư mục con của một thư mục' })
  @ApiParam({ name: 'parentId', description: 'ID của thư mục cha', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách thư mục con',
    schema: ApiResponseDto.getSchema([FolderResponseDto]),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.FOLDER_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getChildFolders(@Param('parentId', ParseIntPipe) parentId: number,@CurrentUser() userId: JwtPayload) {
    const folders = await this.userFolderService.getChildFolders(parentId, userId);
    return ApiResponseDto.success(folders, 'Lấy danh sách thư mục con thành công');
  }

  /**
   * Lấy thông tin thư mục theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin thư mục theo ID' })
  @ApiParam({ name: 'id', description: 'ID của thư mục', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết của thư mục',
    schema: ApiResponseDto.getSchema(FolderDetailResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.FOLDER_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getFolderById(@Param('id', ParseIntPipe) id: number, @CurrentUser() userId: JwtPayload) {
    const folder = await this.userFolderService.getFolderById(id, userId);
    return ApiResponseDto.success(folder, 'Lấy thông tin thư mục thành công');
  }

  /**
   * Tạo mới thư mục
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Tạo mới thư mục' })
  @ApiCreatedResponse({
    description: 'Tạo mới thư mục thành công',
    type: () => ApiResponseDto.getSchema(FolderResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.FOLDER_CREATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @ApiBody({ type: CreateFolderDto })
  async createFolder(@Body() createDto: CreateFolderDto, @CurrentUser() userId: JwtPayload) {
    const folder = await this.userFolderService.createFolder(createDto, userId);
    return ApiResponseDto.created<FolderResponseDto>(folder, 'Tạo mới thư mục thành công');
  }

  /**
   * Cập nhật thư mục
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật thư mục' })
  @ApiParam({ name: 'id', description: 'ID của thư mục', type: 'number' })
  @ApiOkResponse({
    description: 'Cập nhật thư mục thành công',
    schema: ApiResponseDto.getSchema(FolderResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.FOLDER_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @ApiBody({ type: UpdateFolderDto })
  async updateFolder(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateFolderDto,
    @CurrentUser() userId: JwtPayload
  ) {
    const folder = await this.userFolderService.updateFolder(id, updateDto, userId);
    return ApiResponseDto.success<FolderResponseDto>(folder, 'Cập nhật thư mục thành công');
  }

  /**
   * Xóa thư mục
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa thư mục' })
  @ApiParam({ name: 'id', description: 'ID của thư mục', type: 'number' })
  @ApiOkResponse({
    description: 'Xóa thư mục thành công',
    schema: ApiResponseDto.getSchema(Object),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.FOLDER_DELETE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async deleteFolder(@Param('id', ParseIntPipe) id: number, @CurrentUser() userId: JwtPayload) {
    await this.userFolderService.deleteFolder(id, userId);
    return ApiResponseDto.success(null, 'Xóa thư mục thành công');
  }
}
