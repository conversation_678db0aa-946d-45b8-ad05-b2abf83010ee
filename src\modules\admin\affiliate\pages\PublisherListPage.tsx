import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { PublisherStatus, mapApiStatusToPublisherStatus } from '../types/affiliate.types';
import {
  AffiliateAccountDto,
  AffiliateAccountQueryDto,
  AffiliateAccountStatus,
} from '../types/api.types';

// Sử dụng AffiliateAccountDto thay cho PublisherDto
type PublisherDto = AffiliateAccountDto;
import { usePublisherData } from '../hooks/useAffiliateData';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';

/**
 * Trang quản lý danh sách publisher
 */
const PublisherListPage: React.FC = () => {
  const { t } = useTranslation(['affiliate', 'common']);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<PublisherDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'userName',
        title: t('affiliate:publisher.table.userName'),
        dataIndex: 'userName',
        width: '15%',
        sortable: true,
        render: (_, record: PublisherDto) => {
          return record.user?.fullName || '';
        },
      },
      {
        key: 'userEmail',
        title: t('affiliate:publisher.table.userEmail'),
        dataIndex: 'userEmail',
        width: '15%',
        sortable: true,
        render: (_, record: PublisherDto) => {
          return record.user?.email || '';
        },
      },
      {
        key: 'referralCode',
        title: t('affiliate:publisher.table.referralCode'),
        dataIndex: 'referralCode',
        width: '10%',
        sortable: true,
      },
      {
        key: 'performance',
        title: t('affiliate:publisher.table.totalReferrals'),
        dataIndex: 'performance',
        width: '10%',
        sortable: true,
      },
      {
        key: 'totalEarnings',
        title: t('affiliate:publisher.table.totalCommission'),
        dataIndex: 'totalEarnings',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          return (
            <div className="text-right">
              {new Intl.NumberFormat('vi-VN').format(value as number)} đ
            </div>
          );
        },
      },
      {
        key: 'status',
        title: t('affiliate:publisher.table.status'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = mapApiStatusToPublisherStatus(value as AffiliateAccountStatus);
          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
                status === PublisherStatus.ACTIVE
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : status === PublisherStatus.PENDING
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    : status === PublisherStatus.SUSPENDED
                      ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
              }`}
            >
              {status === PublisherStatus.ACTIVE
                ? t('common:active')
                : status === PublisherStatus.PENDING
                  ? t('common:pending')
                  : status === PublisherStatus.SUSPENDED
                    ? t('common:suspended')
                    : t('common:inactive')}
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('affiliate:publisher.table.createdAt'),
        dataIndex: 'createdAt',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          // Xử lý cả trường hợp timestamp là chuỗi hoặc số
          let timestamp: number;

          if (typeof value === 'string') {
            // Nếu là chuỗi, chuyển đổi thành số
            timestamp = parseInt(value, 10);
          } else {
            // Nếu là số, sử dụng trực tiếp
            timestamp = value as number;
          }

          // Kiểm tra xem timestamp có phải là milliseconds hay seconds
          // Nếu timestamp > 10^12, giả định đó là milliseconds
          // Nếu không, giả định đó là seconds và nhân với 1000
          const date = timestamp > 10000000000
            ? new Date(timestamp)
            : new Date(timestamp * 1000);

          return date.toLocaleDateString('vi-VN');
        },
      },
    ],
    [t]
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'active', label: t('common:active'), icon: 'check', value: 'ACTIVE' },
      { id: 'pending', label: t('common:pending'), icon: 'clock', value: 'PENDING' },
      { id: 'inactive', label: t('common:inactive'), icon: 'eye-off', value: 'INACTIVE' },
      { id: 'suspended', label: t('common:suspended'), icon: 'alert-triangle', value: 'BLOCKED' },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): AffiliateAccountQueryDto => {
    const queryParams: AffiliateAccountQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue as AffiliateAccountStatus;
    }

    return queryParams;
  };

  // Thêm cột actions vào columns
  const columnsWithActions = useMemo(() => {
    return [
      ...columns,
      {
        key: 'actions',
        title: t('common:actions'),
        width: '15%',
        render: (_: unknown, record: PublisherDto) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view'),
              icon: 'eye',
              onClick: () => console.log('View', record.id),
            },
            {
              id: 'edit',
              label: t('common:edit'),
              icon: 'edit',
              onClick: () => console.log('Edit', record.id),
            },
            {
              id: 'change-status',
              label: t('common:changeStatus', 'Thay đổi trạng thái'),
              icon: 'refresh-cw',
              onClick: () => console.log('Change status', record.id),
            },
          ];

          return (
            <div className="flex justify-center">
              <ActionMenu
                items={actionItems}
                menuTooltip={t('common:moreActions', 'Thêm hành động')}
                iconSize="sm"
                iconVariant="default"
                placement="bottom"
                menuWidth="180px"
                menuIcon="more-horizontal"
                showAllInMenu={true}
                preferRight={true}
                preferTop={true}
              />
            </div>
          );
        },
      },
    ];
  }, [columns, t]);

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<PublisherDto, AffiliateAccountQueryDto>({
      columns: columnsWithActions,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Lấy hook từ usePublisherData
  const { usePublishers } = usePublisherData();

  // Gọi API lấy danh sách publisher với queryParams từ dataTable
  const { data: publisherData, isLoading } = usePublishers(dataTable.queryParams);

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      ACTIVE: t('common:active'),
      PENDING: t('common:pending'),
      INACTIVE: t('common:inactive'),
      BLOCKED: t('common:suspended'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={publisherData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: publisherData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: publisherData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default PublisherListPage;
