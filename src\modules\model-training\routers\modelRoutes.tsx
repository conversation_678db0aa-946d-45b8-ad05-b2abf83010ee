import { RouteObject } from 'react-router-dom';
import { ProviderIntegrationPage, CreateModelBasePage, CreateDatasetPage } from '../pages';
import MainLayout from '@/shared/layouts/MainLayout';
import { Suspense } from 'react';
import { Loading } from '@/shared/components/common/Loading';

/**
 * Routes cho module model-training
 */
export const modelTrainingRoutes: RouteObject[] = [
    {
        path: 'model-training',
        children: [
            {
                index: true,
                element: (
                    <MainLayout title="Model Training" key="model-training-layout">
                        <Suspense fallback={<Loading />} key="model-training">
                            <ProviderIntegrationPage />
                        </Suspense>
                    </MainLayout>
                ),
            },
            {
                path: 'create-model-base',
                element: (
                    <MainLayout title="Tạo Model Base" key="create-model-base-layout">
                        <Suspense fallback={<Loading />} key="create-model-base">
                            <CreateModelBasePage />
                        </Suspense>
                    </MainLayout>
                ),
            },
            {
                path: 'create-dataset',
                element: (
                    <MainLayout title="Dataset Fine-tuning Model" key="create-dataset-layout">
                        <Suspense fallback={<Loading />} key="create-dataset">
                            <CreateDatasetPage />
                        </Suspense>
                    </MainLayout>
                ),
            },
        ],
    },
];
