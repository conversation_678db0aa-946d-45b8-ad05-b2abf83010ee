import { useCallback } from 'react';
import { useTaskQueueContext } from '@/shared/contexts/taskQueueContext.hooks';
import { useCrawlUrl } from '@/modules/data/url/hooks/useUrlQuery';
import { CrawlDto } from '@/modules/data/url/types/url.types';
import { NotificationUtil } from '@/shared/utils/notification';
import { useTranslation } from 'react-i18next';

/**
 * Hook để crawl URL với TaskQueue
 */
export const useCrawlUrlWithQueue = () => {
  const { t } = useTranslation();

  // Lấy context của TaskQueue
  const taskQueue = useTaskQueueContext();

  // Mutation để crawl URL
  const { mutateAsync: crawlUrl, isPending: isCrawling } = useCrawlUrl();

  /**
   * Crawl URL với TaskQueue
   */
  const crawlUrlWithQueue = useCallback(
    async (crawlDto: CrawlDto) => {
      try {
        // Tạo tiêu đề và mô tả cho task
        const title = `Crawl URL: ${crawlDto.url}`;
        const description = `Độ sâu: ${crawlDto.depth}, Số lượng tối đa: ${crawlDto.maxUrls || 'không giới hạn'}`;

        // Thêm task vào queue
        return new Promise((resolve, reject) => {
          taskQueue.addCustomTask({
            title,
            description,
            execute: async (onProgress) => {
              try {
                // Cập nhật tiến trình bắt đầu
                onProgress(10);

                // Gọi API để crawl URL
                const result = await crawlUrl(crawlDto);

                // Cập nhật tiến trình hoàn thành
                onProgress(100);

                // Hiển thị thông báo thành công
                NotificationUtil.success({
                  message: result.message || t('data:url.crawlSuccess', 'Bắt đầu crawl URL thành công'),
                  duration: 5000,
                });

                resolve(result);
                return result;
              } catch (error) {
                console.error('Error crawling URL:', error);

                // Hiển thị thông báo lỗi
                NotificationUtil.error({
                  message: error instanceof Error
                    ? `${t('data:url.crawlError', 'Lỗi khi crawl URL')}: ${error.message}`
                    : t('data:url.crawlError', 'Lỗi khi crawl URL'),
                  duration: 5000,
                });

                reject(error);
                throw error;
              }
            },
            onSuccess: (result) => {
              resolve(result);
            },
            onError: (error) => {
              reject(error);
            },
          });
        });
      } catch (error) {
        console.error('Error adding crawl task to queue:', error);
        throw error;
      }
    },
    [crawlUrl, taskQueue, t]
  );

  return {
    crawlUrlWithQueue,
    isCrawling,
  };
};

export default useCrawlUrlWithQueue;
