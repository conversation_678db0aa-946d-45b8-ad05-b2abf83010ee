import React, { useState, useEffect } from 'react';
import { INITIAL_EMAIL } from './constants';
import { EmailBuilderProps, EmailElement, HistoryState, EmailTemplate, Asset } from './types';
import { generateHTML, extractCssFromHtml } from './utils';
import { createGrapesJSProject, saveGrapesJSProject } from './utils/export-utils';
import {
  EmailElementsPanel,
  EmailEditorPanel,
  EmailCanvas,
  EmailToolbar,
  EmailPreview,
  CodeViewer,
  AssetUploadDialog,
  LayersPanel,
  AssetsPanel,
} from './components';
import useNotification from '@/shared/hooks/common/useNotification';

const EmailBuilder: React.FC<EmailBuilderProps> = ({
  initialValue,
  onContentChange,
  // compactMode = false
}) => {
  const { addNotification } = useNotification();
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [selectedElement, setSelectedElement] = useState<EmailElement | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const [emailData, setEmailData] = useState(INITIAL_EMAIL);
  const [emailElements, setEmailElements] = useState<EmailElement[]>([]);
  const [showPreview, setShowPreview] = useState(false);

  // State variables for enhanced functionality
  const [history, setHistory] = useState<HistoryState[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [leftPanelVisible, setLeftPanelVisible] = useState(true);
  const [rightPanelVisible, setRightPanelVisible] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  // const [draggedElement, setDraggedElement] = useState<EmailElement | null>(null);
  const [showHtmlCode, setShowHtmlCode] = useState(false);
  const [cssCode, setCssCode] = useState<string>('');
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [showAssetUploadDialog, setShowAssetUploadDialog] = useState(false);
  const [leftPanelTab, setLeftPanelTab] = useState<'elements' | 'layers' | 'assets'>('elements');

  // Assets state variables
  const [assets, setAssets] = useState<Asset[]>([
    {
      id: 'asset-1',
      type: 'image',
      src: 'https://via.placeholder.com/600x300?text=Sample+Image+1',
      alt: 'Sample Image 1',
      name: 'Sample Image 1',
      category: 'sample'
    },
    {
      id: 'asset-2',
      type: 'image',
      src: 'https://via.placeholder.com/600x300?text=Sample+Image+2',
      alt: 'Sample Image 2',
      name: 'Sample Image 2',
      category: 'sample'
    }
  ]);

  // Refs
  // const canvasRef = useRef<HTMLDivElement>(null);

  // Nạp dữ liệu từ initialValue nếu có
  useEffect(() => {
    if (initialValue && initialValue.trim() !== '' && emailElements.length === 0) {
      // Nếu có initialValue và không có phần tử nào, tạo một phần tử text mặc định
      const newElements = [{
        id: `content-${Date.now()}`,
        type: 'text',
        content: initialValue,
        style: {
          color: '#333333',
          textAlign: 'left',
          fontSize: 16,
          lineHeight: 1.5,
          padding: 16
        }
      }];

      setEmailElements(newElements);
      // Khởi tạo history
      setHistory([{ emailElements: newElements, selectedIndex: null }]);
      setHistoryIndex(0);
    }
  }, [initialValue, emailElements.length]);

  // Cập nhật nội dung HTML khi emailElements thay đổi
  useEffect(() => {
    if (onContentChange && emailElements.length > 0) {
      // Tạo HTML từ các phần tử
      const html = generateHTML(emailElements, emailData);
      // Gọi callback
      onContentChange(html);
    }
  }, [emailElements, emailData, onContentChange]);

  // Thêm vào history khi có thay đổi
  const addToHistory = (elements: EmailElement[], index: number | null) => {
    // Cắt bỏ history sau vị trí hiện tại nếu đang ở giữa
    const newHistory = history.slice(0, historyIndex + 1);
    // Thêm trạng thái mới
    newHistory.push({ emailElements: JSON.parse(JSON.stringify(elements)), selectedIndex: index });
    // Cập nhật history và index
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  // Hiển thị/ẩn mã HTML
  const toggleHtmlCode = () => {
    // Nếu đang mở code viewer, đóng lại
    if (showHtmlCode) {
      setShowHtmlCode(false);
      return;
    }

    // Nếu đang đóng, mở lên và tạo CSS code
    const html = generateHTML(emailElements, emailData);
    const css = extractCssFromHtml(html);
    setCssCode(css);
    setShowHtmlCode(true);
  };

  // Thêm phần tử mới vào email
  const addNewElement = (type: string) => {
    const id = `element-${Date.now()}`;
    const newElement: EmailElement = {
      id,
      type,
      draggable: true,
      removable: true,
      editable: true,
      selectable: true,
      hoverable: true,
      copyable: true,
      style: {
        padding: 8,
        margin: 4,
      }
    };

    // Cấu hình mặc định cho từng loại phần tử
    switch (type) {
      case 'text':
        newElement.content = 'Nhấp đôi để chỉnh sửa văn bản này';
        newElement.style = {
          ...newElement.style,
          color: '#333333',
          fontSize: 16,
          textAlign: 'left',
          paddingTop: 8,
          paddingBottom: 8,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.5,
        };
        break;

      case 'heading':
        newElement.content = 'Tiêu đề mẫu';
        newElement.headingType = 'h2';
        newElement.style = {
          ...newElement.style,
          color: '#111111',
          fontSize: 24,
          fontWeight: 'bold',
          textAlign: 'center',
          paddingTop: 16,
          paddingBottom: 16,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.2,
          marginBottom: 16,
          fontFamily: 'Arial, sans-serif',
        };
        break;

      case 'image':
        newElement.src = 'https://via.placeholder.com/600x200?text=Hình+ảnh+mẫu';
        newElement.alt = 'Hình ảnh mẫu';
        newElement.style = {
          ...newElement.style,
          width: '100%',
          paddingTop: 8,
          paddingBottom: 8,
          display: 'block',
        };
        break;

      case 'button':
        newElement.text = 'Nút nhấn';
        newElement.url = '#';
        newElement.style = {
          ...newElement.style,
          backgroundColor: '#0070f3',
          color: '#ffffff',
          padding: 12,
          borderRadius: 4,
          textAlign: 'center',
          fontWeight: 'bold',
          width: '200px',
          margin: '16px auto',
        };
        break;

      // Thêm các loại phần tử khác nếu cần
    }

    const newElements = [...emailElements, newElement];
    setEmailElements(newElements);

    // Chọn phần tử mới thêm
    setSelectedElement(newElement);
    setSelectedIndex(newElements.length - 1);

    // Thêm vào history
    addToHistory(newElements, newElements.length - 1);
  };

  // Chọn phần tử để chỉnh sửa
  const handleSelectElement = (element: EmailElement, index: number) => {
    // Nếu đang chọn phần tử khác, lưu trạng thái hiện tại vào history
    if (selectedIndex !== index && selectedIndex !== null) {
      addToHistory(emailElements, selectedIndex);
    }

    setSelectedElement(element);
    setSelectedIndex(index);
  };

  // Xử lý khi thả phần tử vào canvas
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const elementType = e.dataTransfer.getData('text/plain');
    if (elementType) {
      addNewElement(elementType);
    }
  };

  // Cập nhật thuộc tính của phần tử được chọn
  const updateSelectedElement = (property: string, value: unknown) => {
    if (selectedElement && selectedIndex !== null) {
      const updatedElement = { ...selectedElement };

      if (property.includes('.')) {
        const [parentProp, child] = property.split('.');
        if (parentProp) {
          if (!updatedElement[parentProp as keyof typeof updatedElement]) {
            updatedElement[parentProp as keyof typeof updatedElement] = {};
          } else {
            // Sử dụng type assertion để đảm bảo có thể spread object
            const currentValue = updatedElement[parentProp as keyof typeof updatedElement];
            updatedElement[parentProp as keyof typeof updatedElement] = {
              ...(currentValue as Record<string, unknown>)
            };
          }

          // Safely update the nested property
          const parentObj = updatedElement[parentProp as keyof typeof updatedElement] as Record<string, unknown>;
          if (parentObj && child) {
            parentObj[child] = value;
          }
        }
      } else {
        // Sử dụng type assertion với EmailElement có thuộc tính động
        (updatedElement)[property] = value;
      }

      // Cập nhật phần tử trong emailElements
      const newElements = [...emailElements];
      newElements[selectedIndex] = updatedElement;
      setEmailElements(newElements);
      setSelectedElement(updatedElement);
    }
  };

  // Xóa phần tử đã chọn
  const deleteSelectedElement = () => {
    if (selectedIndex !== null) {
      // Lưu trạng thái trước khi xóa
      addToHistory(emailElements, selectedIndex);

      const newElements = [...emailElements];
      newElements.splice(selectedIndex, 1);
      setEmailElements(newElements);
      setSelectedElement(null);
      setSelectedIndex(null);
    }
  };

  // Di chuyển phần tử lên trên
  const moveElementUp = () => {
    if (selectedIndex !== null && selectedIndex > 0) {
      // Lưu trạng thái trước khi di chuyển
      addToHistory(emailElements, selectedIndex);

      const newElements = [...emailElements];
      const temp = newElements[selectedIndex];
      newElements[selectedIndex] = newElements[selectedIndex - 1] as EmailElement;
      newElements[selectedIndex - 1] = temp as EmailElement;

      setEmailElements(newElements);
      setSelectedIndex(selectedIndex - 1);
      setSelectedElement(temp as EmailElement);
    }
  };

  // Di chuyển phần tử xuống dưới
  const moveElementDown = () => {
    if (selectedIndex !== null && selectedIndex < emailElements.length - 1) {
      // Lưu trạng thái trước khi di chuyển
      addToHistory(emailElements, selectedIndex);

      const newElements = [...emailElements];
      const temp = newElements[selectedIndex];
      newElements[selectedIndex] = newElements[selectedIndex + 1] as EmailElement;
      newElements[selectedIndex + 1] = temp as EmailElement;

      setEmailElements(newElements);
      setSelectedIndex(selectedIndex + 1);
      setSelectedElement(temp as EmailElement);
    }
  };

  // Hoàn tác (undo)
  const handleUndo = () => {
    if (historyIndex > 0) {
      const prevState = history[historyIndex - 1];
      setEmailElements(prevState.emailElements);
      setSelectedIndex(prevState.selectedIndex);
      setSelectedElement(prevState.selectedIndex !== null ? prevState.emailElements[prevState.selectedIndex] : null);
      setHistoryIndex(historyIndex - 1);
    }
  };

  // Làm lại (redo)
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      const nextState = history[historyIndex + 1];
      setEmailElements(nextState.emailElements);
      setSelectedIndex(nextState.selectedIndex);
      setSelectedElement(nextState.selectedIndex !== null ? nextState.emailElements[nextState.selectedIndex] : null);
      setHistoryIndex(historyIndex + 1);
    }
  };

  // Áp dụng template email
  const applyEmailTemplate = (template: EmailTemplate) => {
    // Hiển thị xác nhận nếu đã có nội dung
    if (emailElements.length > 0) {
      if (!window.confirm('Bạn có chắc muốn áp dụng template này? Điều này sẽ thay thế tất cả nội dung hiện tại.')) {
        return;
      }
    }

    // Áp dụng template
    setEmailElements([...template.elements]);
    setSelectedElement(null);
    setSelectedIndex(null);

    // Cập nhật thông tin email
    setEmailData({
      ...emailData,
      name: template.name,
      subject: template.name
    });

    // Thêm vào history
    addToHistory([...template.elements], null);

    // Hiển thị thông báo
    addNotification('success', `Template "${template.name}" đã được áp dụng thành công.`, 3000);
  };

  // Tạo HTML và CSS
  const generateEmailContent = () => {
    const html = generateHTML(emailElements, emailData);
    const css = extractCssFromHtml(html);
    return { html, css };
  };

  // Lưu email (chỉ cập nhật nội dung)
  const handleSave = async () => {
    try {
      const { html } = generateEmailContent();

      // Hiển thị thông báo
      addNotification('success', "Email đã được lưu thành công.", 3000);

      // Gọi callback nếu có
      if (onContentChange) {
        onContentChange(html);
      }
    } catch (error) {
      console.error('Lỗi khi lưu email:', error);
      addNotification('error', "Có lỗi xảy ra khi lưu email.", 3000);
    }
  };

  // Lưu mã HTML
  const handleSaveHTML = async () => {
    try {
      const { html } = generateEmailContent();

      // Tạo một blob và tạo URL để download
      const blob = new Blob([html], { type: 'text/html' });
      const url = URL.createObjectURL(blob);

      // Tạo một element a tạm thời để download
      const a = document.createElement('a');
      a.href = url;
      a.download = `${emailData.name || 'email-template'}-html.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      // Hiển thị thông báo
      addNotification('success', "Mã HTML đã được lưu thành công.", 3000);
    } catch (error) {
      console.error('Lỗi khi lưu mã HTML:', error);
      addNotification('error', "Có lỗi xảy ra khi lưu mã HTML.", 3000);
    }
  };

  // Lưu HTML với CSS nhúng
  const handleSaveHTMLWithCSS = async () => {
    try {
      const { html, css } = generateEmailContent();

      // Log nội dung HTML và CSS
      console.log('HTML Content:', html);
      console.log('CSS Content:', css);

      // Tạo HTML đầy đủ với CSS nhúng (không sử dụng trực tiếp nhưng giữ lại để tham khảo)
      const fullHTML = `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${emailData.name || 'email-template'}</title>
  <style>
${css}
  </style>
</head>
<body>
${html}
</body>
</html>`;

      // Log full HTML
      console.log('Full HTML with CSS:', fullHTML);

      // Gọi callback nếu có để cập nhật nội dung
      if (onContentChange) {
        // Thay vì chỉ truyền HTML, truyền cả HTML đầy đủ với CSS nhúng
        onContentChange(fullHTML);
      }

      // Hiển thị thông báo
      addNotification('success', "Nội dung đã được lưu thành công.", 3000);

      // Đóng EmailBuilder nếu đang ở trong BlogCreatePage
      if (window.parent && window.parent.document) {
        // Tìm nút đóng EmailBuilder và kích hoạt sự kiện click
        const closeButton = window.parent.document.querySelector('.email-builder-container button[title="Lưu và đóng"]');
        if (closeButton) {
          (closeButton as HTMLButtonElement).click();
        }
      }
    } catch (error) {
      console.error('Lỗi khi lưu HTML với CSS:', error);
      addNotification('error', "Có lỗi xảy ra khi lưu nội dung.", 3000);
    }
  };

  // Lưu file GrapesJS
  const handleSaveGrapesJS = async () => {
    try {
      // const { html } = generateEmailContent();

      // Tạo dự án GrapesJS
      const project = createGrapesJSProject(emailElements, emailData, assets);

      // Lưu dự án GrapesJS
      await saveGrapesJSProject(project);

      // Hiển thị thông báo
      addNotification('success', "File GrapesJS đã được lưu thành công.", 3000);
    } catch (error) {
      console.error('Lỗi khi lưu file GrapesJS:', error);
      addNotification('error', "Có lỗi xảy ra khi lưu file GrapesJS.", 3000);
    }
  };

  // Xử lý khi tải lên asset mới
  const handleAssetUpload = (asset: Asset) => {
    setAssets([...assets, asset]);

    addNotification('success', "Tài nguyên đã được tải lên thành công.", 3000);
  };



  // Toggle fullscreen
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <div className={`email-builder flex flex-col h-full ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}>
      <EmailToolbar
        emailData={emailData}
        setEmailData={setEmailData}
        showPreview={showPreview}
        setShowPreview={setShowPreview}
        viewMode={viewMode}
        setViewMode={setViewMode}
        canUndo={historyIndex > 0}
        canRedo={historyIndex < history.length - 1}
        onUndo={handleUndo}
        onRedo={handleRedo}
        onSave={handleSave}
        onSaveHTML={handleSaveHTML}
        onSaveHTMLWithCSS={handleSaveHTMLWithCSS}
        onSaveGrapesJS={handleSaveGrapesJS}
        onShowCode={toggleHtmlCode}
        isFullscreen={isFullscreen}
        toggleFullscreen={toggleFullscreen}
        leftPanelVisible={leftPanelVisible}
        toggleLeftPanel={() => setLeftPanelVisible(!leftPanelVisible)}
        rightPanelVisible={rightPanelVisible}
        toggleRightPanel={() => setRightPanelVisible(!rightPanelVisible)}
        onShowAssetUpload={() => setShowAssetUploadDialog(true)}
      />

      <div className="flex-1 flex overflow-hidden">
        {leftPanelVisible && (
          <div className="w-64 border-r overflow-hidden flex-shrink-0">
            <div className="border-b">
              <div className="flex">
                <button
                  className={`flex-1 px-3 py-2 text-sm ${leftPanelTab === 'elements' ? 'bg-gray-100 border-b-2 border-blue-500' : 'hover:bg-gray-50'}`}
                  onClick={() => setLeftPanelTab('elements')}
                >
                  Elements
                </button>
                <button
                  className={`flex-1 px-3 py-2 text-sm ${leftPanelTab === 'layers' ? 'bg-gray-100 border-b-2 border-blue-500' : 'hover:bg-gray-50'}`}
                  onClick={() => setLeftPanelTab('layers')}
                >
                  Layers
                </button>
                <button
                  className={`flex-1 px-3 py-2 text-sm ${leftPanelTab === 'assets' ? 'bg-gray-100 border-b-2 border-blue-500' : 'hover:bg-gray-50'}`}
                  onClick={() => setLeftPanelTab('assets')}
                >
                  Assets
                </button>
              </div>
            </div>

            {leftPanelTab === 'elements' && (
              <EmailElementsPanel
                onAddElement={addNewElement}
                onApplyTemplate={applyEmailTemplate}
              />
            )}

            {leftPanelTab === 'layers' && (
              <LayersPanel
                emailElements={emailElements}
                selectedElement={selectedElement}
                selectedIndex={selectedIndex}
                onSelectElement={handleSelectElement}
                onDeleteElement={deleteSelectedElement}
                onDuplicateElement={(index: number) => {
                  const elementToDuplicate = emailElements[index];
                  if (elementToDuplicate) {
                    const duplicatedElement = {
                      ...JSON.parse(JSON.stringify(elementToDuplicate)),
                      id: `element-${Date.now()}`
                    };
                    const newElements = [...emailElements, duplicatedElement];
                    setEmailElements(newElements);
                    setSelectedElement(duplicatedElement);
                    setSelectedIndex(newElements.length - 1);
                    addToHistory(newElements, newElements.length - 1);
                  }
                }}
                onToggleVisibility={(index: number) => {
                  const element = emailElements[index];
                  if (element) {
                    const updatedElement = { ...element };
                    const isVisible = element.style?.display !== 'none';

                    if (!updatedElement.style) updatedElement.style = {};
                    updatedElement.style.display = isVisible ? 'none' : 'block';

                    const newElements = [...emailElements];
                    newElements[index] = updatedElement;
                    setEmailElements(newElements);

                    if (selectedIndex === index) {
                      setSelectedElement(updatedElement);
                    }
                  }
                }}
              />
            )}

            {leftPanelTab === 'assets' && (
              <AssetsPanel
                assets={assets}
                onAddAsset={handleAssetUpload}
                onDeleteAsset={(assetId: string) => {
                  setAssets(assets.filter(asset => asset.id !== assetId));
                }}
                onSelectAsset={(asset: Asset) => {
                  if (asset.type === 'image') {
                    addNewElement('image');
                    // Cập nhật phần tử hình ảnh vừa thêm với src từ asset
                    const lastIndex = emailElements.length;
                    if (lastIndex > 0) {
                      const lastElement = emailElements[lastIndex - 1];
                      if (lastElement && lastElement.type === 'image') {
                        updateSelectedElement('src', asset.src);
                        updateSelectedElement('alt', asset.alt || asset.name || '');
                      }
                    }
                  }
                }}
              />
            )}
          </div>
        )}

        <EmailCanvas
          emailElements={emailElements}
          selectedElement={selectedElement}
          selectedIndex={selectedIndex}
          viewMode={viewMode}
          onSelectElement={(element, index) => element && handleSelectElement(element, index)}
          onDeleteElement={deleteSelectedElement}
          onMoveElementUp={moveElementUp}
          onMoveElementDown={moveElementDown}
          onDrop={handleDrop}
          showPreview={showPreview}
        />

        {rightPanelVisible && (
          <div className="w-72 border-l overflow-hidden flex-shrink-0">
            <EmailEditorPanel
              selectedElement={selectedElement}
              updateSelectedElement={updateSelectedElement}
              selectedIndex={selectedIndex}
              emailElements={emailElements}
            />
          </div>
        )}
      </div>

      {/* Dialogs */}
      <EmailPreview
        open={showPreviewDialog}
        onClose={() => setShowPreviewDialog(false)}
        emailElements={emailElements}
        emailData={emailData}
      />

      <CodeViewer
        open={showHtmlCode}
        onClose={() => setShowHtmlCode(false)}
        htmlCode={generateHTML(emailElements, emailData)}
        cssCode={cssCode}
      />

      <AssetUploadDialog
        open={showAssetUploadDialog}
        onClose={() => setShowAssetUploadDialog(false)}
        onUpload={handleAssetUpload}
      />
    </div>
  );
};

export default EmailBuilder;

