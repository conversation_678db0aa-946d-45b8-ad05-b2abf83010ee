import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { ADMIN_WAREHOUSE_ERROR_CODES } from '../exceptions/warehouse.exception';
import {
  WarehouseRepository,
  VirtualWarehouseRepository,
  WarehouseCustomFieldRepository
} from '@modules/business/repositories';
import {
  QueryVirtualWarehouseDto,
  VirtualWarehouseResponseDto,
  VirtualWarehouseDetailResponseDto
} from '../dto/warehouse';
import { PaginatedResult } from '@common/response';
import { plainToInstance } from 'class-transformer';
import { WarehouseValidationHelper } from '../helpers/warehouse-validation.helper';
import { WarehouseTypeEnum } from '@modules/business/enums';

/**
 * Service xử lý logic nghiệp vụ cho kho ảo
 */
@Injectable()
export class AdminVirtualWarehouseService {
  private readonly logger = new Logger(AdminVirtualWarehouseService.name);

  constructor(
    private readonly warehouseRepository: WarehouseRepository,
    private readonly virtualWarehouseRepository: VirtualWarehouseRepository,
    private readonly warehouseCustomFieldRepository: WarehouseCustomFieldRepository,
    private readonly warehouseValidationHelper: WarehouseValidationHelper
  ) {}

  /**
   * Lấy danh sách kho ảo với phân trang
   * @param queryDto DTO truy vấn
   * @returns Danh sách kho ảo với phân trang
   */
  async findAll(queryDto: QueryVirtualWarehouseDto): Promise<PaginatedResult<VirtualWarehouseResponseDto>> {
    this.logger.log(`Lấy danh sách kho ảo với phân trang: ${JSON.stringify(queryDto)}`);

    try {
      // Lấy danh sách kho ảo từ repository
      const [virtualWarehouses, total] = await this.virtualWarehouseRepository.findAllWithPagination(queryDto);

      // Chuyển đổi dữ liệu thành DTO sử dụng constructor
      const items = virtualWarehouses.map(warehouse => new VirtualWarehouseResponseDto(warehouse));

      // Trả về kết quả với phân trang
      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách kho ảo: ${error.message}`, error.stack);
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_FIND_FAILED,
        'Lỗi khi lấy danh sách kho ảo'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết kho ảo theo ID
   * @param warehouseId ID của kho
   * @returns Thông tin chi tiết kho ảo
   */
  async findOne(warehouseId: number): Promise<VirtualWarehouseDetailResponseDto> {
    this.logger.log(`Lấy thông tin chi tiết kho ảo với ID: ${warehouseId}`);

    // Lấy thông tin kho
    const warehouse = await this.warehouseRepository.findByWarehouseId_admin(warehouseId);
    this.warehouseValidationHelper.validateWarehouseExists(warehouse);

    // Kiểm tra loại kho
    this.warehouseValidationHelper.validateWarehouseType(warehouse, WarehouseTypeEnum.VIRTUAL);

    // Lấy thông tin kho ảo
    const virtualWarehouse = await this.virtualWarehouseRepository.findByWarehouseIdWithWarehouse(warehouseId);
    this.warehouseValidationHelper.validateVirtualWarehouseExists(virtualWarehouse);

    try {
      // Lấy danh sách trường tùy chỉnh của kho
      const customFields = await this.warehouseCustomFieldRepository.findByWarehouseId_admin(warehouseId);

      // Log dữ liệu để debug
      this.logger.log(`Dữ liệu kho ảo: ${JSON.stringify(virtualWarehouse)}`);

      // Kiểm tra các trường của virtualWarehouse
      this.logger.log(`warehouseId: ${virtualWarehouse.warehouseId}`);
      this.logger.log(`associatedSystem: ${virtualWarehouse.associatedSystem}`);
      this.logger.log(`purpose: ${virtualWarehouse.purpose}`);
      this.logger.log(`warehouse: ${JSON.stringify(virtualWarehouse.warehouse)}`);

      // Tạo response sử dụng constructor
      // Đảm bảo các trường associatedSystem và purpose được đưa vào cấp cao nhất của response
      const response = new VirtualWarehouseDetailResponseDto({
        warehouseId: virtualWarehouse.warehouseId,
        associatedSystem: virtualWarehouse.associatedSystem,
        purpose: virtualWarehouse.purpose,
        warehouse: virtualWarehouse.warehouse,
        customFields: customFields || []
      });

      // Log response để debug
      this.logger.log(`Response: ${JSON.stringify(response)}`);

      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin chi tiết kho ảo: ${error.message}`, error.stack);
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_FIND_FAILED,
        'Lỗi khi lấy thông tin chi tiết kho ảo'
      );
    }
  }
}
