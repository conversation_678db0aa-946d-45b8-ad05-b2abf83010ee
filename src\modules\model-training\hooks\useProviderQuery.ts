import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getProviders,
  getProviderById,
  createProvider,
  updateProvider,
  deleteProvider,
} from '../services/provider.service';
import { ProviderQueryKeys } from '../constants/provider-query-key';
import {
  CreateProviderDto,
  UpdateProviderDto,
  ProviderQueryParams,
} from '../types/provider.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

/**
 * Interface cho lỗi API
 */
interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
  message?: string;
}

/**
 * Hook để lấy danh sách nhà cung cấp
 */
export const useProviders = (params: ProviderQueryParams = {}) => {
  return useQuery({
    queryKey: ProviderQueryKeys.list(params),
    queryFn: () => getProviders(params),
  });
};

/**
 * Hook để lấy thông tin chi tiết của nhà cung cấp
 */
export const useProviderDetail = (id: string) => {
  return useQuery({
    queryKey: ProviderQueryKeys.detail(id),
    queryFn: () => getProviderById(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo nhà cung cấp mới
 */
export const useCreateProvider = () => {
  const queryClient = useQueryClient();
  const notification = useSmartNotification();

  return useMutation({
    mutationFn: (data: CreateProviderDto) => createProvider(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ProviderQueryKeys.lists() });
      notification.success({ message: 'Tạo nhà cung cấp thành công' });
    },
    onError: (error: ApiError) => {
      notification.error({
        message: error.response?.data?.message || 'Đã xảy ra lỗi khi tạo nhà cung cấp'
      });
    },
  });
};

/**
 * Hook để cập nhật thông tin nhà cung cấp
 */
export const useUpdateProvider = (id: string) => {
  const queryClient = useQueryClient();
  const notification = useSmartNotification();

  return useMutation({
    mutationFn: (data: UpdateProviderDto) => updateProvider(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ProviderQueryKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: ProviderQueryKeys.lists() });
      notification.success({ message: 'Cập nhật nhà cung cấp thành công' });
    },
    onError: (error: ApiError) => {
      notification.error({
        message: error.response?.data?.message || 'Đã xảy ra lỗi khi cập nhật nhà cung cấp'
      });
    },
  });
};

/**
 * Hook để xóa nhà cung cấp
 */
export const useDeleteProvider = () => {
  const queryClient = useQueryClient();
  const notification = useSmartNotification();

  return useMutation({
    mutationFn: (id: string) => deleteProvider(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ProviderQueryKeys.lists() });
      notification.success({ message: 'Xóa nhà cung cấp thành công' });
    },
    onError: (error: ApiError) => {
      notification.error({
        message: error.response?.data?.message || 'Đã xảy ra lỗi khi xóa nhà cung cấp'
      });
    },
  });
};
