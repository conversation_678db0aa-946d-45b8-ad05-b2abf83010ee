import { z } from 'zod';

/**
 * Schema cho URL
 */
export const urlSchema = z.object({
  id: z.string(),
  url: z.string().url({ message: 'URL không hợp lệ' }),
  title: z.string().min(1, { message: 'Tiêu đề không được để trống' }),
  content: z.string().min(1, { message: 'Nội dung không được để trống' }),
  type: z.string().optional(),
  tags: z.array(z.string()).optional(),
  ownedBy: z.number(),
  createdAt: z.number(),
  updatedAt: z.number(),
  isActive: z.boolean(),
});

/**
 * Schema cho tạo URL mới
 */
export const createUrlSchema = z.object({
  url: z.string().url({ message: 'URL không hợp lệ' }),
  title: z.string().min(1, { message: 'Tiêu đề không được để trống' }),
  content: z.string().min(1, { message: 'Nội dung không được để trống' }),
  type: z.string().optional(),
  tags: z.array(z.string()).optional(),
  ownedBy: z.number(),
  isActive: z.boolean().optional().default(true),
});

/**
 * Schema cho cập nhật URL
 */
export const updateUrlSchema = z.object({
  url: z.string().url({ message: 'URL không hợp lệ' }).optional(),
  title: z.string().min(1, { message: 'Tiêu đề không được để trống' }).optional(),
  content: z.string().min(1, { message: 'Nội dung không được để trống' }).optional(),
  type: z.string().optional(),
  tags: z.array(z.string()).optional(),
  ownedBy: z.number().optional(),
  isActive: z.boolean().optional(),
});

/**
 * Schema cho tìm kiếm URL
 */
export const searchUrlSchema = z.object({
  page: z.number().optional().default(1),
  limit: z.number().optional().default(10),
  sortBy: z.string().optional().default('createdAt'),
  sortDirection: z.enum(['ASC', 'DESC']).optional().default('DESC'),
  keyword: z.string().optional(),
  type: z.string().optional(),
  tags: z.array(z.string()).optional(),
  userId: z.number().optional(),
  isActive: z.boolean().optional(),
});

/**
 * Schema cho crawl URL
 */
export const crawlUrlSchema = z.object({
  url: z.string().url({ message: 'URL không hợp lệ' }),
  depth: z.number().min(1).max(5).default(1),
  ignoreRobotsTxt: z.boolean().optional().default(false),
  maxUrls: z.number().min(1).max(100).optional().default(20),
  ownedBy: z.number().optional(),
});
