import React, { useState } from 'react';
import { Input, Typography, Textarea, Button } from '@/shared/components/common';
import { EmailElement } from '../types';
import { StyleEditor, LayoutEditor, BorderEditor } from './';
import { Settings, Type, LayoutGrid, Square } from 'lucide-react';

interface EmailEditorPanelProps {
  selectedElement: EmailElement | null;
  updateSelectedElement: (property: string, value: unknown) => void;
  selectedIndex: number | null;
  emailElements: EmailElement[];
}

const EmailEditorPanel: React.FC<EmailEditorPanelProps> = ({
  selectedElement,
  updateSelectedElement,
  selectedIndex
}) => {
  const [activeTab, setActiveTab] = useState<'properties' | 'style' | 'layout' | 'border' | 'css'>('properties');

  // Kích thước icon
  const iconSize = 14;

  if (!selectedElement) {
    return (
      <div className="p-4 text-center">
        <Typography variant="body1">Ch<PERSON><PERSON> một phần tử để chỉnh sửa</Typography>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      <div className="border-b">
        <div className="flex overflow-x-auto">
          <Button
            variant="ghost"
            className={`px-4 rounded-none ${activeTab === 'properties' ? 'border-b-2 border-blue-500 text-blue-500' : ''}`}
            onClick={() => setActiveTab('properties')}
          >
            <Settings size={iconSize} className="mr-2" />
            Thuộc tính
          </Button>
          <Button
            variant="ghost"
            className={`px-4 py-2 rounded-none ${activeTab === 'style' ? 'border-b-2 border-blue-500 text-blue-500' : ''}`}
            onClick={() => setActiveTab('style')}
          >
            <Type size={iconSize} className="mr-2" />
            Style
          </Button>
          <Button
            variant="ghost"
            className={`px-4 py-2 rounded-none ${activeTab === 'layout' ? 'border-b-2 border-blue-500 text-blue-500' : ''}`}
            onClick={() => setActiveTab('layout')}
          >
            <LayoutGrid size={iconSize} className="mr-2" />
            Layout
          </Button>
          <Button
            variant="ghost"
            className={`px-4 py-2 rounded-none ${activeTab === 'border' ? 'border-b-2 border-blue-500 text-blue-500' : ''}`}
            onClick={() => setActiveTab('border')}
          >
            <Square size={iconSize} className="mr-2" />
            Border
          </Button>
        </div>
      </div>

      {activeTab === 'properties' ? (
        <div className="p-4">
          <div className="mb-4">
            <Typography variant="h4" className="text-lg font-medium mb-2">
              Chỉnh sửa {selectedElement.type === 'text' ? 'văn bản' :
                selectedElement.type === 'heading' ? 'tiêu đề' :
                  selectedElement.type === 'image' ? 'hình ảnh' :
                    selectedElement.type === 'button' ? 'nút nhấn' :
                      selectedElement.type}
            </Typography>
            <Typography variant="body2" className="text-sm text-gray-500">
              {selectedIndex === -1 ? 'Phần tử trong cột' : `Phần tử #${selectedIndex !== null ? selectedIndex + 1 : ''}`}
            </Typography>
          </div>

          {/* Nội dung chỉnh sửa đơn giản */}
          <div className="space-y-4">
            {selectedElement.type === 'text' && (
              <div className="space-y-4">
                <div className="p-2 rounded-md">
                  <Typography variant="body1" className="font-medium mb-2">Nội dung</Typography>
                  <Textarea
                    placeholder="Nhập nội dung văn bản"
                    value={selectedElement.content || ''}
                    onChange={(e) => updateSelectedElement('content', e.target.value)}
                    className="min-h-[60px] w-full"
                  />
                </div>

                <div className="p-2 rounded-md">
                  <Typography variant="body1" className="font-medium mb-2">Màu chữ</Typography>
                  <div className="flex items-center gap-2">
                    <input
                      type="color"
                      value={selectedElement.style?.color || '#333333'}
                      onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                      className="w-8 h-8 rounded"
                    />
                    <Input
                      value={selectedElement.style?.color || '#333333'}
                      onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                      className="flex-1"
                    />
                  </div>
                </div>

                <div className="p-2 rounded-md">
                  <Typography variant="body1" className="font-medium mb-2">Màu nền</Typography>
                  <div className="flex items-center gap-2">
                    <input
                      type="color"
                      value={selectedElement.style?.backgroundColor || '#ffffff'}
                      onChange={(e) => updateSelectedElement('style.backgroundColor', e.target.value)}
                      className="w-8 h-8 rounded"
                    />
                    <Input
                      value={selectedElement.style?.backgroundColor || '#ffffff'}
                      onChange={(e) => updateSelectedElement('style.backgroundColor', e.target.value)}
                      className="flex-1"
                    />
                  </div>
                </div>
              </div>
            )}

            {selectedElement.type === 'image' && (
              <div className="space-y-4">
                <div className="p-2 rounded-md">
                  <Typography variant="body1" className="font-medium mb-2">URL hình ảnh</Typography>
                  <Input
                    placeholder="https://example.com/image.jpg"
                    value={selectedElement.src || ''}
                    onChange={(e) => updateSelectedElement('src', e.target.value)}
                  />
                </div>

                <div className="p-2 rounded-md">
                  <Typography variant="body1" className="font-medium mb-2">Văn bản thay thế (alt)</Typography>
                  <Input
                    placeholder="Mô tả hình ảnh"
                    value={selectedElement.alt || ''}
                    onChange={(e) => updateSelectedElement('alt', e.target.value)}
                  />
                </div>
              </div>
            )}

            {selectedElement.type === 'button' && (
              <div className="space-y-4">
                <div className="p-2 rounded-md">
                  <Typography variant="body1" className="font-medium mb-2">Văn bản nút</Typography>
                  <Input
                    placeholder="Nhấn vào đây"
                    value={selectedElement.text || ''}
                    onChange={(e) => updateSelectedElement('text', e.target.value)}
                  />
                </div>

                <div className="p-2 rounded-md">
                  <Typography variant="body1" className="font-medium mb-2">Liên kết URL</Typography>
                  <Input
                    placeholder="https://example.com"
                    value={selectedElement.url || ''}
                    onChange={(e) => updateSelectedElement('url', e.target.value)}
                  />
                </div>

                <div className="p-2 rounded-md">
                  <Typography variant="body1" className="font-medium mb-2">Màu nền</Typography>
                  <div className="flex items-center gap-2">
                    <input
                      type="color"
                      value={selectedElement.style?.backgroundColor || '#0070f3'}
                      onChange={(e) => updateSelectedElement('style.backgroundColor', e.target.value)}
                      className="w-8 h-8 rounded"
                    />
                    <Input
                      value={selectedElement.style?.backgroundColor || '#0070f3'}
                      onChange={(e) => updateSelectedElement('style.backgroundColor', e.target.value)}
                      className="flex-1"
                    />
                  </div>
                </div>

                <div className="p-2 rounded-md">
                  <Typography variant="body1" className="font-medium mb-2">Màu chữ</Typography>
                  <div className="flex items-center gap-2">
                    <input
                      type="color"
                      value={selectedElement.style?.color || '#ffffff'}
                      onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                      className="w-8 h-8 rounded"
                    />
                    <Input
                      value={selectedElement.style?.color || '#ffffff'}
                      onChange={(e) => updateSelectedElement('style.color', e.target.value)}
                      className="flex-1"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      ) : activeTab === 'style' ? (
        <StyleEditor
          selectedElement={selectedElement}
          updateSelectedElement={updateSelectedElement}
        />
      ) : activeTab === 'layout' ? (
        <LayoutEditor
          selectedElement={selectedElement}
          updateSelectedElement={updateSelectedElement}
        />
      ) : activeTab === 'border' ? (
        <BorderEditor
          selectedElement={selectedElement}
          updateSelectedElement={updateSelectedElement}
        />
      ) : (
        <div className="p-2 text-center">
          <Typography variant="body1">Chức năng đang được phát triển</Typography>
        </div>
      )}
    </div>
  );
};

export default EmailEditorPanel;
