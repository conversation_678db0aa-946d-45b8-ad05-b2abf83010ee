import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Interface cho nhóm trường tùy chỉnh
 */
export interface CustomGroupForm {
  id: number;
  label: string;
  productId: number | null;
  userId: number | null;
  createAt: number;
}

/**
 * Interface cho danh sách nhóm trường tùy chỉnh
 */
export interface CustomGroupFormListItem {
  id: number;
  label: string;
  productId: number | null;
  createAt: number;
  fieldCount: number;
}

/**
 * Interface cho cấu hình grid
 */
export interface GridConfig {
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
}

/**
 * Interface cho cấu hình trường
 */
export interface FieldConfig {
  id: string;
  label: string;
  type: string;
  required: boolean;
  validation?: Record<string, unknown>;
  placeholder?: string;
  defaultValue?: unknown;
  [key: string]: unknown;
}

/**
 * Interface cho giá trị trường
 */
export interface FieldValue {
  value: unknown;
  [key: string]: unknown;
}

/**
 * Interface cho trường trong nhóm
 */
export interface CustomFieldInGroup {
  id: number;
  component: string;
  config: FieldConfig;
  grid?: GridConfig;
  value?: FieldValue;
}

/**
 * Interface cho chi tiết nhóm trường tùy chỉnh
 */
export interface CustomGroupFormDetail {
  id: number;
  label: string;
  productId: number | null;
  createAt: number;
  fields: CustomFieldInGroup[];
}

/**
 * Interface cho tham số truy vấn nhóm trường tùy chỉnh
 */
export interface CustomGroupFormQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  productId?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho dữ liệu tạo nhóm trường tùy chỉnh
 */
export interface CreateCustomGroupFormData {
  label: string;
  productId?: number;
}

/**
 * Interface cho dữ liệu cập nhật nhóm trường tùy chỉnh
 */
export interface UpdateCustomGroupFormData {
  label?: string;
}

/**
 * Service xử lý API liên quan đến nhóm trường tùy chỉnh
 */
export const CustomGroupFormService = {
  /**
   * Lấy danh sách nhóm trường tùy chỉnh
   * @param params Tham số truy vấn
   * @returns Danh sách nhóm trường tùy chỉnh với phân trang
   */
  getCustomGroupForms: async (params?: CustomGroupFormQueryParams): Promise<ApiResponseDto<PaginatedResult<CustomGroupFormListItem>>> => {
    return apiRequest.get('/user/custom-group-forms', { params });
  },

  /**
   * Lấy chi tiết nhóm trường tùy chỉnh theo ID
   * @param id ID của nhóm trường tùy chỉnh
   * @returns Chi tiết nhóm trường tùy chỉnh
   */
  getCustomGroupFormById: async (id: number): Promise<ApiResponseDto<CustomGroupFormDetail>> => {
    return apiRequest.get(`/user/custom-group-forms/${id}`);
  },

  /**
   * Tạo nhóm trường tùy chỉnh mới
   * @param data Dữ liệu tạo nhóm trường tùy chỉnh
   * @returns Thông tin nhóm trường tùy chỉnh đã tạo
   */
  createCustomGroupForm: async (data: CreateCustomGroupFormData): Promise<ApiResponseDto<CustomGroupForm>> => {
    return apiRequest.post('/user/custom-group-forms', data);
  },

  /**
   * Cập nhật nhóm trường tùy chỉnh
   * @param id ID của nhóm trường tùy chỉnh
   * @param data Dữ liệu cập nhật nhóm trường tùy chỉnh
   * @returns Thông tin nhóm trường tùy chỉnh đã cập nhật
   */
  updateCustomGroupForm: async (id: number, data: UpdateCustomGroupFormData): Promise<ApiResponseDto<CustomGroupForm>> => {
    return apiRequest.put(`/user/custom-group-forms/${id}`, data);
  },

  /**
   * Xóa nhóm trường tùy chỉnh
   * @param id ID của nhóm trường tùy chỉnh
   * @returns Thông báo xóa thành công
   */
  deleteCustomGroupForm: async (id: number): Promise<ApiResponseDto<null>> => {
    return apiRequest.delete(`/user/custom-group-forms/${id}`);
  },
};
