import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiOkResponse,
  ApiCreatedResponse,
} from '@nestjs/swagger';
import { UserInventoryService } from '@modules/business/user/services';
import { JwtUserGuard } from '@modules/auth/guards';
import { ApiResponseDto } from '@common/response';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ErrorCode } from '@common/exceptions';
import {
  CreateInventoryDto,
  UpdateInventoryDto,
  QueryInventoryDto,
  InventoryResponseDto,
} from '../dto/inventory';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';

/**
 * <PERSON> x<PERSON> lý các request liên quan đến tồn kho của người dùng
 */
@ApiTags('User Inventory')
@Controller('user/inventory')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserInventoryController {
  constructor(private readonly userInventoryService: UserInventoryService) {}

  /**
   * Tạo mới bản ghi tồn kho
   * @param createDto DTO chứa thông tin tạo tồn kho mới
   * @returns Thông tin tồn kho đã tạo
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo mới bản ghi tồn kho',
    description: 'Tạo mới bản ghi tồn kho. Số lượng hiện tại (currentQuantity) và tổng số lượng (totalQuantity) sẽ được tự động tính toán từ các số lượng thành phần (availableQuantity, reservedQuantity, defectiveQuantity). Không cần gửi các trường currentQuantity và totalQuantity trong request.'
  })
  @ApiCreatedResponse({
    description: 'Tạo mới tồn kho thành công',
    type: () => ApiResponseDto.getSchema(InventoryResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
    BUSINESS_ERROR_CODES.INVENTORY_VALIDATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  @ApiBody({ type: CreateInventoryDto })
  async createInventory(
    @Body() createDto: CreateInventoryDto,
    @CurrentUser() user: JwtPayload
  ) {
    createDto.userId = user.id;
    const inventory =
      await this.userInventoryService.createInventory(createDto);
    return ApiResponseDto.created<InventoryResponseDto>(
      inventory,
      'Tạo mới tồn kho thành công',
    );
  }

  /**
   * Lấy thông tin tồn kho theo ID
   * @param id ID của tồn kho
   * @returns Thông tin chi tiết của tồn kho
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin tồn kho theo ID' })
  @ApiParam({ name: 'id', description: 'ID của tồn kho', type: 'number' })
  @ApiOkResponse({
    description: 'Lấy thông tin tồn kho thành công',
    type: () => ApiResponseDto.getSchema(InventoryResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.INVENTORY_NOT_FOUND,
    BUSINESS_ERROR_CODES.INVENTORY_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async getInventoryById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload
  ) {
    const inventory = await this.userInventoryService.getInventoryById(id, user.id);
    return ApiResponseDto.success<InventoryResponseDto>(
      inventory,
      'Lấy thông tin tồn kho thành công',
    );
  }

  /**
   * Lấy danh sách tồn kho theo các điều kiện lọc
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách tồn kho với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tồn kho' })
  @ApiOkResponse({
    description: 'Lấy danh sách tồn kho thành công',
    schema: ApiResponseDto.getPaginatedSchema(InventoryResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.INVENTORY_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async getInventories(
    @Query() queryDto: QueryInventoryDto,
    @CurrentUser() user: JwtPayload
  ) {
    queryDto.userId = user.id;
    const result = await this.userInventoryService.getInventories(queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách tồn kho thành công');
  }

  /**
   * Cập nhật thông tin tồn kho
   * @param id ID của tồn kho
   * @param updateDto DTO chứa thông tin cập nhật
   * @returns Thông tin tồn kho đã cập nhật
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật thông tin tồn kho',
    description: 'Cập nhật thông tin tồn kho. Số lượng hiện tại (currentQuantity) và tổng số lượng (totalQuantity) sẽ được tự động tính toán từ các số lượng thành phần (availableQuantity, reservedQuantity, defectiveQuantity). Không cần gửi các trường currentQuantity và totalQuantity trong request.'
  })
  @ApiParam({ name: 'id', description: 'ID của tồn kho', type: 'number' })
  @ApiOkResponse({
    description: 'Cập nhật tồn kho thành công',
    type: () => ApiResponseDto.getSchema(InventoryResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.INVENTORY_NOT_FOUND,
    BUSINESS_ERROR_CODES.INVENTORY_UPDATE_FAILED,
    BUSINESS_ERROR_CODES.INVENTORY_VALIDATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  @ApiBody({ type: UpdateInventoryDto })
  async updateInventory(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateInventoryDto,
    @CurrentUser() user: JwtPayload
  ) {
    const inventory = await this.userInventoryService.updateInventory(
      id,
      updateDto,
      user.id
    );
    return ApiResponseDto.success<InventoryResponseDto>(
      inventory,
      'Cập nhật tồn kho thành công',
    );
  }

  /**
   * Xóa tồn kho
   * @param id ID của tồn kho
   * @returns Thông báo kết quả xóa
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa tồn kho' })
  @ApiParam({ name: 'id', description: 'ID của tồn kho', type: 'number' })
  @ApiOkResponse({
    description: 'Xóa tồn kho thành công',
    schema: ApiResponseDto.getSchema(Object),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.INVENTORY_NOT_FOUND,
    BUSINESS_ERROR_CODES.INVENTORY_DELETE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async deleteInventory(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload
  ) {
    const result = await this.userInventoryService.deleteInventory(id, user.id);
    return ApiResponseDto.success(result, result.message);
  }
}
