import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import { useUserTools } from '../hooks/useTool';
import { useUserGroupTools } from '../hooks/useUserGroupTool';
import { ToolSortBy } from '../types/common.types';

/**
 * Trang tổng quan quản lý Tools của người dùng
 */
const ToolManagementPage: React.FC = () => {
  const { t } = useTranslation(['tools']);

  // Lấy tổng số tools để hiển thị trên card
  const { data: toolsData } = useUserTools({
    page: 1,
    limit: 1,
    sortBy: ToolSortBy.CREATED_AT,
    sortDirection: 'DESC' as const,
  });

  // L<PERSON>y tổng số tool groups để hiển thị trên card
  const { data: groupsData } = useUserGroupTools({
    page: 1,
    limit: 1,
    search: '',
  });

  // Lấy tổng số tool có cập nhật để hiển thị trên card
  const { data: updatableToolsData } = useUserTools({
    page: 1,
    limit: 1,
    hasUpdate: true,
    sortBy: ToolSortBy.CREATED_AT,
    sortDirection: 'DESC' as const,
  });

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Tools Card */}
        <ModuleCard
          title={t('tools:tools', 'Công cụ')}
          description={t(
            'tools:toolsDescription',
            'Quản lý các công cụ của bạn, bao gồm xem, sao chép và tùy chỉnh công cụ.'
          )}
          icon="tool"
          count={toolsData?.meta?.totalItems?.toString() || '0'}
          countLabel={t('tools:totalTools', 'Tổng số công cụ')}
          linkTo="/tools/list"
          linkText={t('tools:manageTools', 'Quản lý công cụ')}
        />

        {/* Tool Groups Card */}
        <ModuleCard
          title={t('tools:toolGroups', 'Nhóm công cụ')}
          description={t(
            'tools:toolGroupsDescription',
            'Quản lý các nhóm công cụ, phân loại và tổ chức công cụ theo nhóm chức năng.'
          )}
          icon="folder"
          count={groupsData?.meta?.totalItems?.toString() || '0'}
          countLabel={t('tools:totalToolGroups', 'Tổng số nhóm công cụ')}
          linkTo="/tools/groups"
          linkText={t('tools:manageToolGroups', 'Quản lý nhóm công cụ')}
        />

        {/* Updatable Tools Card */}
        <ModuleCard
          title={t('tools:updatableTools', 'Công cụ có cập nhật')}
          description={t(
            'tools:updatableToolsDescription',
            'Xem và cập nhật các công cụ có phiên bản mới từ Admin.'
          )}
          icon="refresh-cw"
          count={updatableToolsData?.meta?.totalItems?.toString() || '0'}
          countLabel={t('tools:totalUpdatableTools', 'Tổng số công cụ có cập nhật')}
          linkTo="/tools/versions"
          linkText={t('tools:viewUpdatableTools', 'Xem công cụ có cập nhật')}
        />
      </ResponsiveGrid>
    </div>
  );
};

export default ToolManagementPage;
