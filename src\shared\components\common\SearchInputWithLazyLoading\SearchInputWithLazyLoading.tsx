import Icon from '@/shared/components/common/Icon';
import { useTheme } from '@/shared/contexts/theme';
import { SearchItem } from '@/shared/types/search-input-with-image.types';
import { debounce } from 'lodash';
import React, { forwardRef, useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Interface cho tham số phân trang
 */
export interface PaginationParams {
  page: number;
  limit: number;
}

/**
 * Interface cho kết quả phân trang
 */
export interface PaginatedResult<T> {
  items: T[];
  meta?: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

export interface SearchInputWithLazyLoadingProps {
  /**
   * Giá trị đã chọn
   */
  value?: string | number;

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: string | number, item: SearchItem) => void;

  /**
   * Hàm load dữ liệu từ API
   * C<PERSON> thể nhận thêm tham số phân trang
   */
  loadOptions: (
    inputValue: string,
    pagination?: PaginationParams
  ) => Promise<SearchItem[] | PaginatedResult<SearchItem>>;

  /**
   * Thời gian debounce (ms)
   */
  debounceTime?: number;

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Thông báo lỗi
   */
  error?: string;

  /**
   * Text hỗ trợ
   */
  helperText?: string;

  /**
   * Kích thước
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Chiều rộng 100%
   */
  fullWidth?: boolean;

  /**
   * Callback khi blur
   */
  onBlur?: () => void;

  /**
   * Callback khi focus
   */
  onFocus?: () => void;

  /**
   * Callback khi nhập text
   */
  onInputChange?: (value: string) => void;

  /**
   * Kích thước hình ảnh
   */
  imageSize?: 'sm' | 'md' | 'lg';

  /**
   * Hiển thị icon tìm kiếm
   */
  showSearchIcon?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Hiển thị nút xóa
   */
  showClearButton?: boolean;

  /**
   * Text hiển thị khi không có kết quả
   */
  noResultsText?: string;

  /**
   * Text hiển thị khi đang tải
   */
  loadingText?: string;

  /**
   * Tải mặc định 10 bản ghi đầu tiên khi focus
   */
  loadInitialOptions?: boolean;

  /**
   * Số lượng bản ghi mỗi trang
   */
  pageSize?: number;
}

/**
 * Component SearchInputWithLazyLoading - Input tìm kiếm với lazy loading từ API
 */
const SearchInputWithLazyLoading = forwardRef<HTMLInputElement, SearchInputWithLazyLoadingProps>(
  (
    {
      value,
      onChange,
      loadOptions,
      debounceTime = 300,
      placeholder = '',
      label,
      disabled = false,
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      onBlur,
      onFocus,
      onInputChange,
      imageSize = 'md',
      showSearchIcon = true,
      className = '',
      showClearButton = true,
      noResultsText,
      loadingText,
      loadInitialOptions = true,
      pageSize = 10,
    },
    forwardedRef
  ) => {
    const { t } = useTranslation();
    useTheme(); // Sử dụng hook theme
    const [isOpen, setIsOpen] = useState(false);
    const [inputValue, setInputValue] = useState('');
    const [items, setItems] = useState<SearchItem[]>([]);
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [highlightedIndex, setHighlightedIndex] = useState(-1);
    const [selectedItem, setSelectedItem] = useState<SearchItem | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMore, setHasMore] = useState(false);

    const inputRef = useRef<HTMLInputElement>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const searchTermRef = useRef<string>('');

    // Kết hợp ref được chuyển tiếp với inputRef
    useEffect(() => {
      if (forwardedRef && inputRef.current) {
        if (typeof forwardedRef === 'function') {
          forwardedRef(inputRef.current);
        } else {
          forwardedRef.current = inputRef.current;
        }
      }
    }, [forwardedRef]);

    // Xác định kích thước dựa trên prop size
    const sizeClasses = {
      sm: 'py-1 px-2 text-sm',
      md: 'py-2 px-3 text-base',
      lg: 'py-3 px-4 text-lg',
    }[size];

    // Xác định kích thước hình ảnh
    const imageSizeClasses = {
      sm: 'w-6 h-6',
      md: 'w-8 h-8',
      lg: 'w-10 h-10',
    }[imageSize];

    // Tìm item đã chọn từ value
    useEffect(() => {
      if (value !== undefined && selectedItem && selectedItem.id === value) {
        setInputValue(selectedItem.name);
      } else if (value === undefined || value === null || value === '') {
        setInputValue('');
        setSelectedItem(null);
      }
    }, [value, selectedItem]);

    // Hàm xử lý kết quả từ API
    const processResults = useCallback(
      (result: SearchItem[] | PaginatedResult<SearchItem>, isLoadMore = false) => {
        let newItems: SearchItem[] = [];
        let meta = { currentPage: 1, totalPages: 1 };

        // Kiểm tra xem kết quả có phải là mảng hay đối tượng phân trang
        if (Array.isArray(result)) {
          newItems = result;
        } else {
          newItems = result.items;
          if (result.meta) {
            meta = {
              currentPage: result.meta.currentPage,
              totalPages: result.meta.totalPages,
            };
            setHasMore(meta.currentPage < meta.totalPages);
          }
        }

        // Cập nhật danh sách items
        if (isLoadMore) {
          setItems(prevItems => [...prevItems, ...newItems]);
        } else {
          setItems(newItems);
        }

        return { items: newItems, meta };
      },
      []
    );

    // Hàm tải dữ liệu ban đầu
    const loadInitialData = useCallback(
      async (input: string) => {
        if (!input && !loadInitialOptions) {
          setItems([]);
          setLoading(false);
          return;
        }

        try {
          setLoading(true);
          searchTermRef.current = input;
          setCurrentPage(1);

          const result = await loadOptions(input, { page: 1, limit: pageSize });
          processResults(result);
        } catch (error) {
          console.error('Error loading options:', error);
          setItems([]);
        } finally {
          setLoading(false);
        }
      },
      [loadOptions, loadInitialOptions, pageSize, processResults]
    );

    // Hàm tải thêm dữ liệu khi scroll
    const loadMoreData = useCallback(async () => {
      if (loadingMore || !hasMore) return;

      try {
        setLoadingMore(true);
        const nextPage = currentPage + 1;

        const result = await loadOptions(searchTermRef.current, {
          page: nextPage,
          limit: pageSize,
        });

        const { meta } = processResults(result, true);
        setCurrentPage(nextPage);
        setHasMore(meta.currentPage < meta.totalPages);
      } catch (error) {
        console.error('Error loading more options:', error);
      } finally {
        setLoadingMore(false);
      }
    }, [currentPage, hasMore, loadOptions, loadingMore, pageSize, processResults]);

    // Debounced function để load options
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const debouncedLoadOptions = useCallback(debounce(loadInitialData, debounceTime), [
      loadInitialData,
      debounceTime,
    ]);

    // Xử lý khi input thay đổi
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setInputValue(value);

      if (onInputChange) {
        onInputChange(value);
      }

      if (value.trim()) {
        setLoading(true);
        debouncedLoadOptions(value);
        setIsOpen(true);
      } else {
        setItems([]);
        setIsOpen(false);
      }
    };

    // Xử lý khi chọn item
    const handleSelectItem = (item: SearchItem) => {
      if (item.disabled) return;

      setSelectedItem(item);
      setInputValue(item.name);
      setIsOpen(false);

      if (onChange) {
        onChange(item.id, item);
      }
    };

    // Xử lý khi focus vào input
    const handleFocus = () => {
      if (onFocus) {
        onFocus();
      }

      // Nếu đã có giá trị nhập vào, tìm kiếm với giá trị đó
      if (inputValue.trim()) {
        setLoading(true);
        debouncedLoadOptions(inputValue);
        setIsOpen(true);
      }
      // Nếu không có giá trị nhập vào và loadInitialOptions = true, tải 10 bản ghi đầu tiên
      else if (loadInitialOptions) {
        setLoading(true);
        loadInitialData('');
        setIsOpen(true);
      }
    };

    // Xử lý khi scroll trong dropdown
    const handleDropdownScroll = useCallback(() => {
      if (!dropdownRef.current) return;

      const { scrollTop, scrollHeight, clientHeight } = dropdownRef.current;
      // Nếu scroll gần đến cuối danh sách (còn 20px) và có thêm dữ liệu để tải
      if (scrollHeight - scrollTop - clientHeight < 20 && hasMore && !loadingMore) {
        loadMoreData();
      }
    }, [hasMore, loadMoreData, loadingMore]);

    // Xử lý khi blur khỏi input
    const handleBlur = () => {
      // Đợi một chút để kiểm tra xem click có phải là vào dropdown không
      setTimeout(() => {
        if (dropdownRef.current && dropdownRef.current.contains(document.activeElement as Node)) {
          return;
        }

        setIsOpen(false);

        if (onBlur) {
          onBlur();
        }

        // Nếu không có item nào được chọn, reset input
        if (!selectedItem && inputValue.trim()) {
          setInputValue('');
        } else if (selectedItem) {
          setInputValue(selectedItem.name);
        }
      }, 100);
    };

    // Xử lý khi nhấn phím
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setHighlightedIndex(prev => (prev < items.length - 1 ? prev + 1 : 0));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setHighlightedIndex(prev => (prev > 0 ? prev - 1 : items.length - 1));
          break;
        case 'Enter':
          e.preventDefault();
          if (highlightedIndex >= 0 && highlightedIndex < items.length) {
            handleSelectItem(items[highlightedIndex]);
          }
          break;
        case 'Escape':
          e.preventDefault();
          setIsOpen(false);
          break;
        default:
          break;
      }
    };

    // Xử lý khi click vào nút xóa
    const handleClear = (e: React.MouseEvent) => {
      e.stopPropagation();
      setInputValue('');
      setSelectedItem(null);
      setItems([]);

      if (onChange) {
        onChange('', {} as SearchItem);
      }

      if (inputRef.current) {
        inputRef.current.focus();
      }
    };

    return (
      <div className={`flex flex-col ${className}`}>
        {/* Label */}
        {label && (
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {label}
          </label>
        )}

        {/* Container */}
        <div ref={containerRef} className="relative">
          {/* Input */}
          <div
            className={`
              flex items-center
              rounded-md
              ${sizeClasses}
              ${disabled ? 'opacity-60 cursor-not-allowed' : ''}
              ${error ? 'border-error' : 'border-none'}
              ${isOpen ? 'ring-2 ring-primary/30' : ''}
              ${fullWidth ? 'w-full' : ''}
            `}
          >
            {showSearchIcon && (
              <div className="flex-shrink-0 ml-2 mr-2 text-muted">
                <Icon name="search" size="sm" />
              </div>
            )}

            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onFocus={handleFocus}
              onBlur={handleBlur}
              placeholder={placeholder}
              disabled={disabled}
              className="w-full bg-transparent border-none focus:outline-none focus:ring-0 text-foreground"
            />

            {showClearButton && inputValue && (
              <button
                type="button"
                onClick={handleClear}
                className="flex-shrink-0 ml-2 mr-2 text-muted hover:text-foreground"
              >
                <Icon name="x" size="sm" />
              </button>
            )}
          </div>

          {/* Dropdown - ModernMenu style */}
          {isOpen && (
            <div
              ref={dropdownRef}
              className="absolute z-10 w-full mt-1 bg-white dark:bg-dark-light rounded-lg shadow-xl overflow-hidden animate-slide-in"
              onScroll={handleDropdownScroll}
            >
              {/* Menu header with gradient */}
              <div className="bg-menu-gradient p-2 text-white">
                <h3 className="font-medium text-sm">
                  {placeholder || t('common:search', 'Tìm kiếm')}
                </h3>
              </div>
              <div className="p-2 max-h-60 overflow-auto">
                {loading ? (
                  <div className="flex items-center justify-center p-4 text-muted">
                    <div className="animate-spin mr-2">
                      <Icon name="loader" size="sm" />
                    </div>
                    <span>{loadingText || t('common:loading', 'Đang tải...')}</span>
                  </div>
                ) : items.length === 0 ? (
                  <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                    {noResultsText || t('common:noResults', 'Không có kết quả')}
                  </div>
                ) : (
                  <>
                    <div className="space-y-1">
                      {items.map((item, index) => (
                        <div
                          key={item.id}
                          className={`
                            group hover:bg-gray-50 dark:hover:bg-dark-lighter transition-all duration-200 rounded-lg overflow-hidden
                            animate-menu-item fill-forwards
                            ${highlightedIndex === index ? 'bg-gray-50 dark:bg-dark-lighter' : ''}
                            ${item.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                          `}
                          style={{ animationDelay: `${index * 50}ms` }}
                          onClick={() => !item.disabled && handleSelectItem(item)}
                        >
                          <div className="flex items-center p-2 relative">
                            {/* Gradient hover effect */}
                            <div className="absolute inset-0 bg-menu-gradient opacity-0 group-hover:opacity-10 transition-all duration-300"></div>

                            {/* Icon container */}
                            <div
                              className={`flex-shrink-0 ${imageSizeClasses} rounded-lg bg-primary/10 dark:bg-primary/20 flex items-center justify-center mr-3 group-hover:bg-primary/20 dark:group-hover:bg-primary/30 transition-colors relative z-10`}
                            >
                              {item.imageUrl ? (
                                <img
                                  src={item.imageUrl}
                                  alt={item.name}
                                  className="w-full h-full object-cover rounded-lg"
                                  onError={e => {
                                    // Hiển thị icon khi lỗi hình ảnh
                                    e.currentTarget.style.display = 'none';
                                    const parent = e.currentTarget.parentElement;
                                    if (parent) {
                                      parent.innerHTML =
                                        '<div class="flex items-center justify-center w-full h-full"><svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg></div>';
                                    }
                                  }}
                                />
                              ) : item.icon ? (
                                item.icon
                              ) : (
                                <Icon
                                  name="building"
                                  size="sm"
                                  className="text-primary transition-transform duration-300 group-hover:scale-110"
                                />
                              )}
                            </div>

                            {/* Content */}
                            <div className="flex-grow min-w-0 relative z-10">
                              <div className="flex items-center justify-between">
                                <span className="font-medium text-gray-800 dark:text-white group-hover:text-primary transition-colors truncate">
                                  {item.name}
                                </span>
                              </div>
                              {item.description && (
                                <div className="text-xs text-gray-500 dark:text-gray-400 group-hover:text-primary/70 transition-colors truncate">
                                  {item.description}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Loading more indicator */}
                    {loadingMore && (
                      <div className="flex items-center justify-center p-2 text-gray-500 dark:text-gray-400 text-sm mt-1">
                        <div className="animate-spin mr-2">
                          <Icon name="loader" size="xs" />
                        </div>
                        <span>{t('common:loadingMore', 'Đang tải thêm...')}</span>
                      </div>
                    )}

                    {/* End of results message */}
                    {!hasMore && items.length > 0 && currentPage > 1 && (
                      <div className="p-2 text-gray-500 dark:text-gray-400 text-center text-xs mt-1">
                        {t('common:endOfResults', 'Đã hiển thị tất cả kết quả')}
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Error message or helper text */}
        {(error || helperText) && (
          <div className="mt-1 text-sm">
            {error ? (
              <p className="text-red-500">{error}</p>
            ) : helperText ? (
              <p className="text-gray-500 dark:text-gray-400">{helperText}</p>
            ) : null}
          </div>
        )}
      </div>
    );
  }
);

SearchInputWithLazyLoading.displayName = 'SearchInputWithLazyLoading';

export default SearchInputWithLazyLoading;
