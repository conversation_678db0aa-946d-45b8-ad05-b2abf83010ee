import React, { useState, useRef } from 'react';
import { Button, Typography } from '@/shared/components/common';
import { EmailElement, EmailData, ExportFormat } from '../types';
import { generateHTML, exportEmail } from '../utils';
import { Download, Upload, FileJson, FileText, Image as ImageIcon, FileType2 } from 'lucide-react';

interface ImportExportPanelProps {
  emailElements: EmailElement[];
  emailData: EmailData;
  onImport: (elements: EmailElement[], data: EmailData) => void;
}

const ImportExportPanel: React.FC<ImportExportPanelProps> = ({
  emailElements,
  emailData,
  onImport
}) => {
  const [exportFormat, setExportFormat] = useState<ExportFormat>('html');
  const [importError, setImportError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // <PERSON><PERSON>ch thước icon
  const iconSize = 16;

  // Xử lý khi xuất template
  const handleExport = () => {
    const html = generateHTML(emailElements, emailData);
    const exportData = exportEmail(exportFormat, html, emailData);

    // Tạo file để download
    let blob: Blob;
    let fileName: string;

    if (typeof exportData === 'string') {
      blob = new Blob([exportData], { type: 'text/plain' });
      fileName = `email-template.${exportFormat}`;
    } else {
      blob = exportData;
      fileName = `email-template.${exportFormat}`;
    }

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Xử lý khi nhập template
  const handleImport = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const content = event.target?.result as string;
        const data = JSON.parse(content);

        // Kiểm tra dữ liệu hợp lệ
        if (!data.elements || !Array.isArray(data.elements)) {
          setImportError('File không chứa dữ liệu template hợp lệ');
          return;
        }

        // Import template
        onImport(
          data.elements,
          {
            name: data.name || 'Imported Template',
            subject: data.subject || 'Imported Template',
            preheader: data.preheader || ''
          }
        );

        // Reset input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }

        setImportError(null);
      } catch {
        setImportError('Không thể đọc file. Vui lòng kiểm tra định dạng JSON.');
      }
    };

    reader.readAsText(file);
  };

  // Lấy icon cho định dạng xuất
  const getFormatIcon = (format: ExportFormat) => {
    switch (format) {
      case 'html':
        return <FileText size={iconSize} />;
      case 'json':
        return <FileJson size={iconSize} />;
      case 'image':
        return <ImageIcon size={iconSize} />;
      case 'pdf':
        return <FileType2 size={iconSize} />;
      default:
        return <FileText size={iconSize} />;
    }
  };

  return (
    <div className="h-full overflow-y-auto p-4">
      <div className="mb-6">
        <Typography variant="h3" className="text-lg font-medium mb-2">
          Xuất Template
        </Typography>
        <Typography variant="body2" className="text-sm text-gray-500 mb-4">
          Xuất template hiện tại để sử dụng sau này
        </Typography>

        <div className="space-y-4">
          <div className="flex flex-wrap gap-2">
            {(['html', 'json', 'image', 'pdf'] as ExportFormat[]).map((format) => (
              <Button
                key={format}
                variant={exportFormat === format ? 'primary' : 'outline'}
                onClick={() => setExportFormat(format)}
                className="flex items-center"
              >
                <span className="mr-2">{getFormatIcon(format)}</span>
                {format.toUpperCase()}
              </Button>
            ))}
          </div>

          <Button
            onClick={handleExport}
            className="w-full flex items-center justify-center"
            disabled={emailElements.length === 0}
          >
            <Download size={iconSize} className="mr-2" />
            Xuất Template
          </Button>
        </div>
      </div>

      <div className="mb-6">
        <Typography variant="h3" className="text-lg font-medium mb-2">
          Nhập Template
        </Typography>
        <Typography variant="body2" className="text-sm text-gray-500 mb-4">
          Nhập template từ file JSON
        </Typography>

        <div className="space-y-4">
          <div
            className="border-2 border-dashed rounded-md p-6 cursor-pointer hover:bg-gray-50 flex flex-col items-center justify-center"
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload size={24} className="mb-2 text-gray-400" />
            <Typography variant="body2" className="text-center text-gray-500">
              Nhấp để chọn file hoặc kéo thả file vào đây
            </Typography>
            <Typography variant="body2" className="text-center text-gray-400 text-xs mt-1">
              Chỉ hỗ trợ file JSON
            </Typography>
            <input
              ref={fileInputRef}
              type="file"
              accept=".json"
              className="hidden"
              onChange={handleImport}
            />
          </div>

          {importError && (
            <div className="text-red-500 text-sm">
              {importError}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImportExportPanel;
