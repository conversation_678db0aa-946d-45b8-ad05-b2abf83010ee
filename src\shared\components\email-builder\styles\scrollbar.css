/* Custom Scrollbar Styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* For horizontal scrollbar */
.custom-scrollbar::-webkit-scrollbar-corner {
  background: #f1f1f1;
}

/* Modal Styles */
:global(.max-w-xl) {
  max-width: 80vw !important;
}

/* Code Viewer Styles */
code {
  font-size: 14px !important;
  padding: 0 !important;
  background: transparent !important;
}
