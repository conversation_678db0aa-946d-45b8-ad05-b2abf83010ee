import { apiClient } from '@/shared/api/axios';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  Provider,
  CreateProviderDto,
  UpdateProviderDto,
  ProviderQueryParams,
} from '../types/provider.types';

const API_BASE_URL = '/user/model-training/providers';

/**
 * Lấy danh sách nhà cung cấp
 * @param params Tham số truy vấn
 * @returns Danh sách nhà cung cấp đã phân trang
 */
export const getProviders = async (
  params: ProviderQueryParams = {}
): Promise<PaginatedResult<Provider>> => {
  const response = await apiClient.get<PaginatedResult<Provider>>(API_BASE_URL, { params });
  return response.result;
};

/**
 * Lấy thông tin nhà cung cấp theo ID
 * @param id ID của nhà cung cấp
 * @returns Thông tin chi tiết của nhà cung cấp
 */
export const getProviderById = async (id: string): Promise<Provider> => {
  const response = await apiClient.get<Provider>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * Tạo nhà cung cấp mới
 * @param data Thông tin nhà cung cấp cần tạo
 * @returns Thông tin nhà cung cấp đã tạo
 */
export const createProvider = async (data: CreateProviderDto): Promise<Provider> => {
  const response = await apiClient.post<Provider>(API_BASE_URL, data);
  return response.result;
};

/**
 * Cập nhật thông tin nhà cung cấp
 * @param id ID của nhà cung cấp
 * @param data Thông tin cần cập nhật
 * @returns Thông tin nhà cung cấp đã cập nhật
 */
export const updateProvider = async (id: string, data: UpdateProviderDto): Promise<Provider> => {
  const response = await apiClient.put<Provider>(`${API_BASE_URL}/${id}`, data);
  return response.result;
};

/**
 * Xóa nhà cung cấp
 * @param id ID của nhà cung cấp cần xóa
 * @returns Kết quả xóa
 */
export const deleteProvider = async (id: string): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(`${API_BASE_URL}/${id}`);
  return response.result;
};
