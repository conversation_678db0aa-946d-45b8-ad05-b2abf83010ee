/**
 * Enum định nghĩa các loại nhà cung cấp AI
 */
export enum ProviderType {
  OPENAI = 'OPENAI',
  GOOGLE = 'GOOGLE',
  XAI = 'XAI',
  META = 'META',
  DEEPSEEK = 'DEEPSEEK',
  ANTHROPIC = 'ANTHROPIC',
}

/**
 * Interface cho thông tin nhà cung cấp AI
 */
export interface Provider {
  /**
   * ID của nhà cung cấp
   */
  id?: string;

  /**
   * Tên của nhà cung cấp
   */
  name: string;

  /**
   * Loại nhà cung cấp
   */
  type: ProviderType;

  /**
   * API key của nhà cung cấp
   */
  apiKey: string;

  /**
   * Thời gian tạo (unix timestamp)
   */
  createdAt?: number;

  /**
   * Thời gian cập nhật (unix timestamp)
   */
  updatedAt?: number;
}

/**
 * Interface cho request tạo nhà cung cấp mới
 */
export interface CreateProviderDto {
  /**
   * Tên của nhà cung cấp
   */
  name: string;

  /**
   * Loại nhà cung cấp
   */
  type: ProviderType;

  /**
   * API key của nhà cung cấp
   */
  apiKey: string;
}

/**
 * Interface cho request cập nhật nhà cung cấp
 */
export interface UpdateProviderDto {
  /**
   * Tên của nhà cung cấp
   */
  name?: string;

  /**
   * API key của nhà cung cấp
   */
  apiKey?: string;
}

/**
 * Interface cho tham số truy vấn danh sách nhà cung cấp
 */
export interface ProviderQueryParams {
  /**
   * Trang hiện tại
   */
  page?: number;

  /**
   * Số lượng kết quả trên một trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm
   */
  search?: string;

  /**
   * Trường sắp xếp
   */
  sortBy?: string;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC';

  /**
   * Lọc theo loại nhà cung cấp
   */
  type?: ProviderType;
}
