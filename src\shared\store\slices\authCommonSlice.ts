import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User } from '@/modules/auth/types/auth.types';
import { Employee } from '@/modules/admin/auth/types/admin-auth.types';

/**
 * Enum xác định loại người dùng đang đăng nhập
 */
export enum AuthType {
  USER = 'user',
  ADMIN = 'admin',
  NONE = 'none',
}

/**
 * Trạng thái xác thực chung
 */
export interface AuthCommonState {
  // Thông tin chung
  authType: AuthType;
  accessToken: string | null;
  /**
   * @deprecated Sử dụng expiresAt thay thế. Giữ lại để tương thích với code cũ.
   */
  expiresIn: number | null;
  expiresAt: number | null;
  isAuthenticated: boolean;

  // Thông tin người dùng
  user: User | null;
  employee: Employee | null;

  // Thông tin xác thực email (cho user)
  verifyToken: string | null;
  /**
   * @deprecated Sử dụng verifyExpiresAt thay thế. Giữ lại để tương thích với code cũ.
   */
  verifyExpiresIn: number | null;
  verifyExpiresAt: number | null;
  verifyInfo: unknown[] | null;

  // Thông tin xác thực 2FA (cho user)
  twoFactorVerifyToken: string | null;
  /**
   * @deprecated Sử dụng twoFactorExpiresAt thay thế. Giữ lại để tương thích với code cũ.
   */
  twoFactorExpiresIn: number | null;
  twoFactorExpiresAt: number | null;
  enabledMethods: Array<{ type: string; value: string }> | null;
}

/**
 * Trạng thái xác thực ban đầu
 */
const initialState: AuthCommonState = {
  // Thông tin chung
  authType: AuthType.NONE,
  accessToken: null,
  expiresIn: null,
  expiresAt: null,
  isAuthenticated: false,

  // Thông tin người dùng
  user: null,
  employee: null,

  // Thông tin xác thực email (cho user)
  verifyToken: null,
  verifyExpiresIn: null,
  verifyExpiresAt: null,
  verifyInfo: null,

  // Thông tin xác thực 2FA (cho user)
  twoFactorVerifyToken: null,
  twoFactorExpiresIn: null,
  twoFactorExpiresAt: null,
  enabledMethods: null,
};

/**
 * Auth Common slice
 */
const authCommonSlice = createSlice({
  name: 'authCommon',
  initialState,
  reducers: {
    /**
     * Đăng nhập thành công cho user
     */
    userLoginSuccess: (
      state,
      action: PayloadAction<{
        accessToken: string;
        expiresIn?: number; // Đánh dấu là optional
        expiresAt?: number; // Ưu tiên sử dụng expiresAt từ API
        user?: User;
      }>
    ) => {
      // Xóa thông tin admin nếu có
      state.employee = null;

      // Ưu tiên sử dụng expiresAt từ API, nếu không có thì tính toán từ expiresIn
      const expiresAt = action.payload.expiresAt || (action.payload.expiresIn ? Date.now() + action.payload.expiresIn * 1000 : 0);

      // Cập nhật thông tin user
      state.authType = AuthType.USER;
      state.accessToken = action.payload.accessToken;
      // Giữ lại expiresIn để tương thích với code cũ
      state.expiresIn = action.payload.expiresIn || Math.floor((expiresAt - Date.now()) / 1000);
      state.expiresAt = expiresAt;
      state.user = action.payload.user || null;
      state.isAuthenticated = true;

      // Log thông tin để debug
      console.log('User login success - token saved to authCommon slice:', {
        authType: state.authType,
        tokenLength: state.accessToken.length,
        expiresAt: state.expiresAt,
        formattedExpiresAt: new Date(state.expiresAt).toLocaleString(),
        hasUser: !!state.user,
        fromAPI: !!action.payload.expiresAt
      });
    },

    /**
     * Đăng nhập thành công cho admin
     */
    adminLoginSuccess: (
      state,
      action: PayloadAction<{
        accessToken: string;
        expiresIn?: number; // Đánh dấu là optional
        expiresAt?: number; // Ưu tiên sử dụng expiresAt từ API
        employee?: Employee;
      }>
    ) => {
      // Xóa thông tin user nếu có
      state.user = null;
      
      // Ưu tiên sử dụng expiresAt từ API, nếu không có thì tính toán từ expiresIn
      const expiresAt = action.payload.expiresAt || (action.payload.expiresIn ? Date.now() + action.payload.expiresIn * 1000 : 0);

      // Cập nhật thông tin admin
      state.authType = AuthType.ADMIN;
      state.accessToken = action.payload.accessToken;
      // Giữ lại expiresIn để tương thích với code cũ
      state.expiresIn = action.payload.expiresIn || Math.floor((expiresAt - Date.now()) / 1000);
      state.expiresAt = expiresAt;
      state.employee = action.payload.employee || null;
      state.isAuthenticated = true;

      // Log thông tin để debug
      console.log('Admin login success - token saved to authCommon slice:', {
        authType: state.authType,
        tokenLength: state.accessToken.length,
        expiresAt: state.expiresAt,
        formattedExpiresAt: new Date(state.expiresAt).toLocaleString(),
        hasEmployee: !!state.employee
      });
    },

    /**
     * Cập nhật token cho user
     */
    updateUserToken: (
      state,
      action: PayloadAction<{
        accessToken: string;
        refreshToken?: string;
        expiresIn: number;
      }>
    ) => {
      if (state.authType === AuthType.USER) {
        // Tính toán thời điểm hết hạn
        const expiresAt = Date.now() + action.payload.expiresIn * 1000;

        // Cập nhật thông tin token
        state.accessToken = action.payload.accessToken;
        state.expiresIn = action.payload.expiresIn;
        state.expiresAt = expiresAt;

        // Log thông tin để debug
        console.log('User token updated in authCommon slice:', {
          tokenLength: state.accessToken.length,
          expiresIn: state.expiresIn,
          expiresAt: state.expiresAt,
          formattedExpiresAt: new Date(state.expiresAt).toLocaleString()
        });
      }
    },

    /**
     * Cập nhật token cho admin
     */
    updateAdminToken: (
      state,
      action: PayloadAction<{
        accessToken: string;
        expiresIn: number;
        expiresAt: number;
      }>
    ) => {
      if (state.authType === AuthType.ADMIN) {
        // Đảm bảo expiresAt được tính toán đúng
        let expiresAt = action.payload.expiresAt;
        if (!expiresAt && action.payload.expiresIn) {
          expiresAt = Date.now() + action.payload.expiresIn * 1000;
        }

        // Cập nhật thông tin token
        state.accessToken = action.payload.accessToken;
        state.expiresIn = action.payload.expiresIn;
        state.expiresAt = expiresAt;

        // Log thông tin để debug
        console.log('Admin token updated in authCommon slice:', {
          tokenLength: state.accessToken.length,
          expiresIn: state.expiresIn,
          expiresAt: state.expiresAt,
          formattedExpiresAt: new Date(state.expiresAt).toLocaleString()
        });
      }
    },

    /**
     * Cập nhật thông tin người dùng
     */
    updateUser: (
      state,
      action: PayloadAction<{
        user: User;
      }>
    ) => {
      if (state.authType === AuthType.USER) {
        state.user = action.payload.user;
      }
    },

    /**
     * Cập nhật thông tin nhân viên
     */
    updateEmployee: (
      state,
      action: PayloadAction<{
        employee: Employee;
      }>
    ) => {
      if (state.authType === AuthType.ADMIN) {
        state.employee = action.payload.employee;
      }
    },

    /**
     * Đăng xuất
     */
    logout: () => {
      // Xóa tất cả thông tin xác thực
      return initialState;
    },

    /**
     * Lưu thông tin xác thực email (cho user)
     */
    saveVerifyInfo: (
      state,
      action: PayloadAction<{
        verifyToken: string;
        expiresIn?: number;
        expiresAt?: number;
        info?: unknown[];
      }>
    ) => {
      state.verifyToken = action.payload.verifyToken;

      // Lưu expiresIn nếu có
      if (action.payload.expiresIn) {
        state.verifyExpiresIn = action.payload.expiresIn;
      }

      // Lưu expiresAt nếu có
      if (action.payload.expiresAt) {
        state.verifyExpiresAt = action.payload.expiresAt;
      } else if (action.payload.expiresIn) {
        // Nếu không có expiresAt nhưng có expiresIn, tính toán expiresAt
        state.verifyExpiresAt = Date.now() + action.payload.expiresIn * 1000;
      }

      // Lưu info nếu có
      if (action.payload.info) {
        state.verifyInfo = action.payload.info;
      }
    },

    /**
     * Lưu thông tin xác thực 2FA (cho user)
     */
    saveTwoFactorInfo: (
      state,
      action: PayloadAction<{
        verifyToken: string;
        expiresAt: number;
        enabledMethods: Array<{ type: string; value: string }>;
      }>
    ) => {
      state.twoFactorVerifyToken = action.payload.verifyToken;
      state.twoFactorExpiresAt = action.payload.expiresAt;
      // Tính toán expiresIn từ expiresAt để tương thích với code cũ
      state.twoFactorExpiresIn = Math.floor((action.payload.expiresAt - Date.now()) / 1000);
      state.enabledMethods = action.payload.enabledMethods;
    },
  },
});

// Export actions
export const {
  userLoginSuccess,
  adminLoginSuccess,
  updateUserToken,
  updateAdminToken,
  updateUser,
  updateEmployee,
  logout,
  saveVerifyInfo,
  saveTwoFactorInfo,
} = authCommonSlice.actions;

// Export reducer
export default authCommonSlice.reducer;
