import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { MediaService } from '../services/media.service';
import { AdminMediaDto, MediaQueryDto, PaginatedMediaResult } from '../types/media.types';

// Key cho React Query
const ADMIN_MEDIA_QUERY_KEY = 'admin-media';

/**
 * Hook để lấy danh sách media cho admin
 * @param params Tham số truy vấn
 * @returns Query object với danh sách media
 */
export const useAdminMediaList = (params?: MediaQueryDto) => {
  return useQuery({
    queryKey: [ADMIN_MEDIA_QUERY_KEY, 'list', params],
    queryFn: () => MediaService.getMediaList(params),
    select: data => data.result as PaginatedMediaResult,
  });
};

/**
 * Hook để lấy thông tin chi tiết media
 * @param id ID của media
 * @returns Query object với thông tin chi tiết media
 */
export const useAdminMediaDetail = (id: string) => {
  return useQuery({
    queryKey: [ADMIN_MEDIA_QUERY_KEY, 'detail', id],
    queryFn: () => MediaService.getMediaById(id),
    select: data => data.result as AdminMediaDto,
    enabled: !!id,
  });
};

/**
 * Hook để xóa media
 * @returns Mutation object để xóa media
 */
export const useDeleteMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => MediaService.deleteMedia(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ADMIN_MEDIA_QUERY_KEY, 'list'] });
    },
  });
};

/**
 * Hook để xóa nhiều media
 * @returns Mutation object để xóa nhiều media
 */
export const useDeleteMultipleMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => MediaService.deleteMultipleMedia(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ADMIN_MEDIA_QUERY_KEY, 'list'] });
    },
  });
};

/**
 * Hook để xóa liên kết agent media
 * @returns Mutation object để xóa liên kết agent media
 */
export const useDeleteAgentMedia = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (mediaIds: string[]) => MediaService.deleteAgentMedia(mediaIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ADMIN_MEDIA_QUERY_KEY, 'list'] });
    },
  });
};
