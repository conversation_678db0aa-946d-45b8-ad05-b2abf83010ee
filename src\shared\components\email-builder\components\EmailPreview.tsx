import React from 'react';
import { Mo<PERSON>, Button } from '@/shared/components/common';
import { EmailElement, EmailData } from '../types';
import { generateHTML } from '../utils';

interface EmailPreviewProps {
  open: boolean;
  onClose: () => void;
  emailElements: EmailElement[];
  emailData: EmailData;
}

const EmailPreview: React.FC<EmailPreviewProps> = ({
  open,
  onClose,
  emailElements,
  emailData
}) => {
  // Tạo HTML cho email
  const emailHtml = generateHTML(emailElements, emailData);

  return (
    <Modal
      title="Xem trước email"
      isOpen={open}
      onClose={onClose}
      size="xl"
      footer={
        <div className="flex justify-end">
          <Button variant="outline" onClick={onClose}>
            Đóng
          </Button>
        </div>
      }
    >
      <div className="h-[70vh] overflow-auto border rounded-md">
        <iframe
          srcDoc={emailHtml}
          title="Email Preview"
          className="w-full h-full"
          style={{ border: 'none' }}
        />
      </div>
    </Modal>
  );
};

export default EmailPreview;
