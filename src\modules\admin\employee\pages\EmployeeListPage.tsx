/**
 * Trang danh sách nhân viên
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Card, Table, ActionMenu } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import useNotification from '@/shared/hooks/common/useNotification';
import NotificationContainer from '@/shared/components/layout/chat-panel/NotificationContainer';
import {
  EmployeeDto,
  EmployeeQueryDto,
  EmployeeStatus,
} from '../types/employee.types';
import EmployeeForm from '../components/forms/EmployeeForm';
import { useEmployees } from '../hooks/useEmployeeQuery';

/**
 * Trang danh sách nhân viên
 */
const EmployeeListPage: React.FC = () => {
  const { t } = useTranslation(['employee', 'common']);
  const navigate = useNavigate();
  const { notifications, removeNotification } = useNotification();

  // State cho query và dữ liệu
  const [query, setQuery] = useState<EmployeeQueryDto>({
    page: 1,
    limit: 10,
    search: '',
    status: undefined,
  });

  // State lưu trữ dữ liệu nhân viên
  const [employees, setEmployees] = useState<EmployeeDto[]>([]);
  const [totalItems, setTotalItems] = useState(0);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Queries
  const employeesQuery = useEmployees(query);

  // Effect to update state when API data is available
  useEffect(() => {
    console.log('Component mounted or query changed:', query);
    console.log('Employees query state:', employeesQuery);

    // Update state when API data is available
    if (employeesQuery.data) {
      setEmployees(employeesQuery.data.items || []);
      setTotalItems(employeesQuery.data.meta?.totalItems || 0);
    }
  }, [query, employeesQuery]);

  // Xử lý tìm kiếm
  const handleSearch = (search: string) => {
    setQuery(prev => ({ ...prev, search, page: 1 }));
  };

  // Xử lý lọc theo trạng thái
  const handleFilterStatus = (status: EmployeeStatus | undefined) => {
    setQuery(prev => ({ ...prev, status, page: 1 }));
  };

  // Xử lý phân trang
  const handlePageChange = (page: number) => {
    setQuery(prev => ({ ...prev, page }));
  };

  // Xử lý thêm nhân viên mới
  const handleAdd = () => {
    showForm();
  };

  // Xử lý chỉnh sửa nhân viên
  const handleEdit = (employee: EmployeeDto) => {
    navigate(`/admin/employees/edit/${employee.id}`);
  };

  // Xử lý xem chi tiết nhân viên
  const handleView = (employee: EmployeeDto) => {
    navigate(`/admin/employees/view/${employee.id}`);
  };

  // Xử lý khi form tạo nhân viên thành công
  const handleFormSuccess = () => {
    // Đóng form
    hideForm();

    // Refresh danh sách nhân viên
    employeesQuery.refetch();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
  };

  // Columns cho bảng
  const columns: TableColumn<EmployeeDto>[] = [
    { key: 'id', title: 'ID', dataIndex: 'id', width: '5%' },
    {
      key: 'fullName',
      title: t('employee:list.fullName'),
      dataIndex: 'fullName',
      width: '20%',
      render: (_value: unknown, record: EmployeeDto) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full overflow-hidden mr-2 bg-primary/20 flex items-center justify-center">
            {record.avatar ? (
              <img
                src={
                  record.avatar.startsWith('http')
                    ? record.avatar
                    : `https://cdn.redai.vn/${record.avatar}`
                }
                alt={record.fullName}
                className="w-full h-full object-cover"
                onError={e => {
                  // Fallback to first letter if image fails to load
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.parentElement!.innerHTML = `<span class="text-primary font-medium">${record.fullName.charAt(0)}</span>`;
                }}
              />
            ) : (
              <span className="text-primary font-medium">{record.fullName.charAt(0)}</span>
            )}
          </div>
          <span>{record.fullName}</span>
        </div>
      ),
    },
    { key: 'email', title: t('employee:list.email'), dataIndex: 'email', width: '20%' },
    {
      key: 'phoneNumber',
      title: t('employee:list.phoneNumber'),
      dataIndex: 'phoneNumber',
      width: '15%',
    },
    {
      key: 'department',
      title: t('employee:list.department'),
      dataIndex: 'department',
      width: '15%',
      render: (value: unknown) => (typeof value === 'string' ? value : '-'),
    },
    {
      key: 'status',
      title: t('employee:list.status'),
      dataIndex: 'enable',
      width: '10%',
      render: (value: unknown) => {
        // Sử dụng trường enable thay vì status
        const isActive = value as boolean;
        return (
          <div
            className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
              isActive
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
            }`}
          >
            {isActive ? t('common:active') : t('common:inactive')}
          </div>
        );
      },
    },
    {
      key: 'actions',
      title: t('common:actions'),
      width: '15%',
      render: (_: unknown, record: EmployeeDto) => {
        // Tạo danh sách các action items
        const actionItems = [
          {
            id: 'view',
            label: t('common:view'),
            icon: 'eye',
            onClick: () => handleView(record),
          },
          {
            id: 'edit',
            label: t('common:edit'),
            icon: 'edit',
            onClick: () => handleEdit(record),
          },
          // Có thể thêm các action khác vào đây
          // {
          //   id: 'delete',
          //   label: t('common:delete'),
          //   icon: 'trash',
          //   onClick: () => handleDelete(record),
          // },
        ];

        return (
          <div className="flex justify-center">
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm hành động')} // Thêm fallback text
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              menuIcon="more-horizontal" // Sử dụng icon ellipsis ngang
              showAllInMenu={true} // Hiển thị tất cả action trong menu
              preferRight={true} // Ưu tiên hiển thị menu bên phải nếu không đủ không gian bên trái
              preferTop={true} // Ưu tiên hiển thị menu bên trên nếu không đủ không gian bên dưới
            />
          </div>
        );
      },
    },
  ];

  // Debug: Log data
  useEffect(() => {
    console.log('Employees state:', employees);
  }, [employees]);

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAdd}
        items={[
          {
            id: 'all',
            label: t('common:all'),
            icon: 'list',
            onClick: () => handleFilterStatus(undefined),
          },
          {
            id: 'active',
            label: t('common:active'),
            icon: 'check',
            onClick: () => handleFilterStatus(EmployeeStatus.ACTIVE),
          },
          {
            id: 'inactive',
            label: t('common:inactive'),
            icon: 'eye-off',
            onClick: () => handleFilterStatus(EmployeeStatus.INACTIVE),
          },
        ]}
        columns={[]}
        showColumnFilter={false}
      />

      <SlideInForm isVisible={isVisible}>
        <EmployeeForm onSuccess={handleFormSuccess} onCancel={handleCancel} />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={columns}
          data={employees} // Use employees state directly instead of filteredData
          rowKey="id"
          pagination={{
            current: query.page || 1,
            pageSize: query.limit || 10,
            total: totalItems,
            onChange: handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
          loading={employeesQuery.isLoading}
          sortable={true}
        />
      </Card>

      {/* Notification container */}
      <NotificationContainer notifications={notifications} onRemove={removeNotification} />
    </div>
  );
};

export default EmployeeListPage;
