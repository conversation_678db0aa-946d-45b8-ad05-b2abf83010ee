{"employee": {"title": "Employee Management", "description": "Manage employee information and permissions in the system", "common": {"employee": "Employee", "employees": "Employees", "role": "Role", "roles": "Roles", "permission": "Permission", "permissions": "Permissions", "add": "Add Employee", "edit": "Edit Employee", "delete": "Delete Employee", "view": "View Employee", "search": "Search Employee", "filter": "Fi<PERSON>ee", "sort": "Sort Employee", "status": "Status", "active": "Active", "inactive": "Inactive", "enable": "Enable", "disable": "Disable", "save": "Save", "cancel": "Cancel", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "loading": "Loading...", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "confirm": "Confirm", "actions": "Actions"}, "form": {"fullName": "Full Name", "fullName_placeholder": "Enter full name", "email": "Email", "email_placeholder": "Enter email", "phoneNumber": "Phone Number", "phoneNumber_placeholder": "Enter phone number", "password": "Password", "password_placeholder": "Enter password", "address": "Address", "address_placeholder": "Enter address", "status": "Status", "roles": "Roles", "roles_placeholder": "Select roles", "avatar": "Avatar", "avatar_upload": "Upload Avatar", "avatar_change": "Change Avatar", "avatar_remove": "Remove Avatar", "avatar_view": "View Avatar", "avatar_upload_success": "Avatar uploaded successfully", "avatar_upload_error": "Failed to upload avatar", "avatar_upload_size": "Maximum size: {{size}}", "avatar_upload_type": "Format: {{type}}", "newPassword": "New Password", "newPassword_placeholder": "Enter new password", "confirmPassword": "Confirm Password", "confirmPassword_placeholder": "Re-enter new password", "passwordMismatch": "Passwords do not match", "passwordChanged": "Password changed successfully", "passwordChangeFailed": "Failed to change password", "roleAssigned": "Roles assigned successfully", "roleAssignFailed": "Failed to assign roles", "permissionAssigned": "Permissions assigned successfully", "permissionAssignFailed": "Failed to assign permissions"}, "list": {"title": "Employee List", "empty": "No employees found", "search_placeholder": "Search by name, email, phone number", "filter_status": "Filter by status", "filter_role": "Filter by role", "sort_name": "Sort by name", "sort_email": "Sort by email", "sort_createdAt": "Sort by creation date", "total": "Total: {{count}} employees"}, "detail": {"title": "Employee Details", "basic_info": "Basic Information", "contact_info": "Contact Information", "account_info": "Account Information", "role_info": "Role Information", "created_at": "Created At", "updated_at": "Updated At", "last_login": "Last Login"}, "add": {"title": "Add New Employee", "success": "Employee added successfully", "error": "Failed to add employee", "duplicate_email": "Email already exists", "invalid_data": "Invalid data"}, "edit": {"title": "Edit Employee", "success": "Employee updated successfully", "error": "Failed to update employee"}, "delete": {"title": "Delete Employee", "confirm": "Are you sure you want to delete this employee?", "success": "Employee deleted successfully", "error": "Failed to delete employee"}, "role": {"title": "Role Management", "list_title": "Role List", "empty": "No roles found", "add": "Add Role", "edit": "Edit Role", "delete": "Delete Role", "name": "Role Name", "description": "Description", "permissions": "Permissions", "assign_permissions": "Assign Permissions", "permission_list": "Permission List", "module": "<PERSON><PERSON><PERSON>", "action": "Action", "select_all": "Select All", "unselect_all": "Unselect All"}}}