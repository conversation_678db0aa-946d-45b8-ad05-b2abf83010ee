import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Textarea,
  ConditionalField,
  Chip,
} from '@/shared/components/common';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { updateProductSchema } from '../../schemas/product.schema';
import {
  PriceTypeEnum,
  ProductDto,
  HasPriceDto,
  StringPriceDto,
  UpdateProductDto
} from '../../types/product.types';
import { useUpdateProduct } from '../../hooks/useProductQuery';
import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form';

interface ProductEditFormProps {
  product: ProductDto | null;
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  isSubmitting: boolean;
}

// Interface cho form values
interface ProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    length?: string | number;
    width?: string | number;
    height?: string | number;
    weight?: string | number;
  };
}

/**
 * Form chỉnh sửa sản phẩm
 */
const ProductEditForm: React.FC<ProductEditFormProps> = ({
  product,
  onSubmit,
  onCancel,
  isSubmitting,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const { mutateAsync: updateProduct, isPending } = useUpdateProduct();
  // Usando o tipo FormRef da interface do Form
  const formRef = React.useRef<FormRef<ProductFormValues>>(null);
  const [tempTags, setTempTags] = useState<Record<string, string[]>>({});

  // Cập nhật giá trị form khi product thay đổi
  useEffect(() => {
    if (product && formRef.current) {
      // Chuẩn bị dữ liệu cho form
      const formData: ProductFormValues = {
        name: product.name,
        typePrice: product.typePrice,
        description: product.description || '',
        tags: product.tags || [],
      };

      // Thêm dữ liệu giá dựa trên loại giá
      if (product.typePrice === PriceTypeEnum.HAS_PRICE && product.price) {
        const hasPrice = product.price as HasPriceDto;
        formData.listPrice = hasPrice.listPrice;
        formData.salePrice = hasPrice.salePrice;
        formData.currency = hasPrice.currency || 'VND';
      } else if (product.typePrice === PriceTypeEnum.STRING_PRICE && product.price) {
        const stringPrice = product.price as StringPriceDto;
        formData.priceDescription = stringPrice.priceDescription;
      }

      // Thêm dữ liệu cấu hình vận chuyển nếu có
      if (product.shipmentConfig) {
        formData.shipmentConfig = {
          length: product.shipmentConfig.length || '',
          width: product.shipmentConfig.width || '',
          height: product.shipmentConfig.height || '',
          weight: product.shipmentConfig.weight || '',
        };
      }

      // Cập nhật giá trị form
      formRef.current.reset(formData);

      // Khởi tạo tempTags từ tags của sản phẩm
      if (product.id && product.tags && product.tags.length > 0) {
        setTempTags({ [product.id]: product.tags });
      }
    }
  }, [product]);

  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    if (!product) return;

    try {
      const formValues = values as ProductFormValues;

      // Lấy tags từ state
      const productTags = product.id ? tempTags[product.id] || [] : [];

      // Chuyển đổi giá trị form thành dữ liệu API
      const productData: UpdateProductDto = {
        name: formValues.name,
        typePrice: formValues.typePrice,
        price: getPriceData(formValues),
        description: formValues.description,
        tags: productTags,
        shipmentConfig: formValues.shipmentConfig ? {
          length: Number(formValues.shipmentConfig.length) || undefined,
          width: Number(formValues.shipmentConfig.width) || undefined,
          height: Number(formValues.shipmentConfig.height) || undefined,
          weight: Number(formValues.shipmentConfig.weight) || undefined,
        } : undefined,
      };

      // Gọi API cập nhật sản phẩm
      await updateProduct({ id: product.id, data: productData });

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.updateSuccess'),
        duration: 3000,
      });

      // Gọi callback onSubmit
      onSubmit(values as Record<string, unknown>);
    } catch (error) {
      console.error('Error updating product:', error);
      NotificationUtil.error({
        message: t('business:product.updateError'),
        duration: 3000,
      });
    }
  };

  // Hàm lấy dữ liệu giá dựa trên loại giá
  const getPriceData = (values: ProductFormValues): HasPriceDto | StringPriceDto | null => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE && values.listPrice && values.salePrice) {
      return {
        listPrice: Number(values.listPrice),
        salePrice: Number(values.salePrice),
        currency: values.currency || 'VND',
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE && values.priceDescription) {
      return {
        priceDescription: values.priceDescription,
      };
    }
    return null;
  };

  // Giá trị mặc định cho form
  const defaultValues = {
    name: '',
    typePrice: PriceTypeEnum.HAS_PRICE,
    listPrice: '',
    salePrice: '',
    currency: 'VND',
    priceDescription: '',
    description: '',
    tags: [],
    shipmentConfig: {
      length: '',
      width: '',
      height: '',
      weight: '',
    },
  };

  return (
    <Card title={t('business:product.form.editTitle')}>
      <Form
        ref={formRef as unknown as React.RefObject<FormRef<FieldValues>>}
        schema={updateProductSchema(t)}
        onSubmit={handleSubmit}
        defaultValues={defaultValues}
        className="p-4 space-y-4"
      >
        <FormItem
          name="name"
          label={t('business:product.name')}
          required
        >
          <Input fullWidth placeholder={t('business:product.form.namePlaceholder')} />
        </FormItem>

        <FormItem
          name="typePrice"
          label={t('business:product.priceType.title')}
          required
        >
          <Select
            fullWidth
            options={[
              { value: PriceTypeEnum.HAS_PRICE, label: t('business:product.priceType.hasPrice') },
              { value: PriceTypeEnum.STRING_PRICE, label: t('business:product.priceType.stringPrice') },
              { value: PriceTypeEnum.NO_PRICE, label: t('business:product.priceType.noPrice') },
            ]}
          />
        </FormItem>

        {/* Hiển thị các trường giá dựa trên loại giá */}
        <ConditionalField
          condition={{
            field: 'typePrice',
            type: ConditionType.EQUALS,
            value: PriceTypeEnum.HAS_PRICE,
          }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem
              name="listPrice"
              label={t('business:product.listPrice')}
              required
            >
              <Input fullWidth type="number" min="0" placeholder="0" />
            </FormItem>
            <FormItem
              name="salePrice"
              label={t('business:product.salePrice')}
              required
            >
              <Input fullWidth type="number" min="0" placeholder="0" />
            </FormItem>
            <FormItem
              name="currency"
              label={t('business:product.currency')}
              required
            >
              <Input fullWidth defaultValue="VND" />
            </FormItem>
          </div>
        </ConditionalField>

        <ConditionalField
          condition={{
            field: 'typePrice',
            type: ConditionType.EQUALS,
            value: PriceTypeEnum.STRING_PRICE,
          }}
        >
          <FormItem
            name="priceDescription"
            label={t('business:product.priceDescription')}
            required
          >
            <Input fullWidth placeholder={t('business:product.form.priceDescriptionPlaceholder')} />
          </FormItem>
        </ConditionalField>

        <FormItem
          name="description"
          label={t('business:product.description')}
        >
          <Textarea
            fullWidth
            rows={4}
            placeholder={t('business:product.form.descriptionPlaceholder')}
          />
        </FormItem>

        <FormItem name="tags" label={t('business:product.tags')}>
          <div className="space-y-2">
            <Input
              fullWidth
              placeholder={t('business:product.form.tagsPlaceholder')}
              onKeyDown={e => {
                if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                  e.preventDefault();

                  // Lấy tags hiện tại từ state
                  const productId = product?.id;
                  if (!productId) return;

                  // Lấy tags hiện tại hoặc tạo mảng rỗng
                  const currentTempTags = tempTags[productId] || [];
                  const newTag = e.currentTarget.value.trim();

                  // Thêm tag mới nếu chưa tồn tại
                  if (!currentTempTags.includes(newTag)) {
                    setTempTags(prev => ({
                      ...prev,
                      [productId]: [...currentTempTags, newTag],
                    }));
                  }

                  e.currentTarget.value = '';
                }
              }}
            />
            <div className="flex flex-wrap gap-1 mt-2">
              {(() => {
                const productId = product?.id;
                if (!productId) return null;

                const currentTempTags = tempTags[productId] || [];
                return currentTempTags.map((tag, tagIndex) => (
                  <Chip
                    key={`${tagIndex}-${tag}`}
                    size="sm"
                    closable
                    onClose={() => {
                      setTempTags(prev => ({
                        ...prev,
                        [productId]: prev[productId].filter(t => t !== tag),
                      }));
                    }}
                  >
                    {tag}
                  </Chip>
                ));
              })()}
            </div>
          </div>
        </FormItem>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting || isPending}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            isLoading={isSubmitting || isPending}
          >
            {t('common:save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default ProductEditForm;
