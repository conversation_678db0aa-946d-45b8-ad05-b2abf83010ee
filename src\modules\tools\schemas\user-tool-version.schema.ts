import * as z from 'zod';
import { ToolStatus } from '../types/common.types';

/**
 * Schema cho tham số chỉnh sửa phiên bản tool
 */
export const editUserToolVersionSchema = z.object({
  toolName: z.string().min(1, 'Tên hiển thị tool là bắt buộc'),
  toolDescription: z.string().optional(),
  parameters: z.record(z.unknown()),
  changeDescription: z.string().optional(),
  status: z.nativeEnum(ToolStatus).optional(),
});

/**
 * Schema cho tham số đặt phiên bản mặc định
 */
export const setDefaultVersionSchema = z.object({
  toolId: z.string().min(1, 'ID tool là bắt buộc'),
  versionId: z.string().min(1, 'ID phiên bản là bắt buộc'),
});

/**
 * Schema cho tham số xóa phiên bản
 */
export const deleteVersionSchema = z.object({
  toolId: z.string().min(1, 'ID tool là bắt buộc'),
  versionId: z.string().min(1, 'ID phiên bản là bắt buộc'),
});

/**
 * Schema cho tham số truy vấn chi tiết phiên bản
 */
export const getVersionDetailSchema = z.object({
  toolId: z.string().min(1, 'ID tool là bắt buộc'),
  versionId: z.string().min(1, 'ID phiên bản là bắt buộc'),
});
