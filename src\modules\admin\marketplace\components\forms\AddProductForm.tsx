import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Typography,
  Select,
  Card,
  FormGrid,
  Divider,
  Icon,
} from '@/shared/components/common';
import { ProductCategory, CreateProductDto } from '@/modules/admin/marketplace/types/product.types';
import { z } from 'zod';

// Create a custom schema that properly handles string to number conversion
const addProductSchema = z.object({
  name: z
    .string()
    .min(3, 'Tên sản phẩm phải có ít nhất 3 ký tự')
    .max(500, 'Tên sản phẩm không được vượt quá 500 ký tự'),
  description: z.string().min(1, 'Mô tả sản phẩm là bắt buộc'),
  listedPrice: z
    .string()
    .min(1, '<PERSON>i<PERSON> niêm yết là bắt buộc')
    .transform((val) => Number(val))
    .pipe(z.number().min(0, '<PERSON><PERSON><PERSON> niêm yết không được âm')),
  discountedPrice: z
    .string()
    .min(1, 'Gi<PERSON> khuyến mãi là bắt buộc')
    .transform((val) => Number(val))
    .pipe(z.number().min(0, 'Giá khuyến mãi không được âm')),
  category: z.nativeEnum(ProductCategory, {
    errorMap: () => ({ message: 'Loại sản phẩm là bắt buộc' }),
  }),
  sourceId: z.string().min(1, 'Source ID là bắt buộc'),
  imagesMediaTypes: z.array(z.string()).optional(),
  userManualMediaType: z.string().optional(),
  detailMediaType: z.string().optional(),
});

export interface AddProductFormProps {
  /**
   * Function to handle form submission
   */
  onSubmit: (values: CreateProductDto) => void;

  /**
   * Function to handle form cancellation
   */
  onCancel: () => void;
}

/**
 * Form component for adding new products to the marketplace
 */
const AddProductForm: React.FC<AddProductFormProps> = ({ onSubmit, onCancel }) => {
  const { t } = useTranslation(['admin', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form ref
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const formRef = React.useRef<any>(null);

  // File upload refs
  const imageInputRef = useRef<HTMLInputElement>(null);
  const userManualInputRef = useRef<HTMLInputElement>(null);
  const detailInputRef = useRef<HTMLInputElement>(null);

  // File states
  const [images, setImages] = useState<File[]>([]);
  const [userManuals, setUserManuals] = useState<File[]>([]);
  const [userManualTypes, setUserManualTypes] = useState<string[]>([]);
  const [detail, setDetail] = useState<File | null>(null);

  // Default values for the form
  const defaultValues = {
    name: '',
    description: '',
    listedPrice: '0',
    discountedPrice: '0',
    category: ProductCategory.KNOWLEDGE_FILE,
    sourceId: '',
    imagesMediaTypes: ['image/jpeg', 'image/png'],
    userManualMediaType: 'application/pdf',
    detailMediaType: 'application/pdf',
  };

  // Handle form submission
  const handleSubmit = async (values: Record<string, unknown>) => {
    try {
      setIsSubmitting(true);

      // Get image media types
      const imageTypes = images.map(image => image.type);

      // Convert string values to numbers for price fields
      const productData: CreateProductDto = {
        name: values.name as string,
        description: values.description as string,
        category: values.category as ProductCategory,
        listedPrice: Number(values.listedPrice),
        discountedPrice: Number(values.discountedPrice),
        sourceId: values.sourceId as string,
        imagesMediaTypes: imageTypes.length > 0 ? imageTypes : (values.imagesMediaTypes as string[]),
        userManualMediaType: userManualTypes.length > 0
          ? userManualTypes.join(',')
          : (values.userManualMediaType as string),
        detailMediaType: detail?.type || (values.detailMediaType as string),
      };

      console.log('Submitting product with file types:', {
        imageTypes,
        userManualTypes,
        detailType: detail?.type
      });

      onSubmit(productData);
    } catch (error) {
      console.error('Error submitting product form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle image file selection
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);
    setImages(prev => [...prev, ...fileArray]);
  };

  // Handle user manual file selection
  const handleUserManualChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) {
      setUserManuals([]);
      setUserManualTypes([]);
      return;
    }

    // Convert FileList to array
    const fileArray = Array.from(files);

    // Extract file types
    const fileTypes = fileArray.map(file => file.type);

    // Update states
    setUserManuals(fileArray);
    setUserManualTypes(fileTypes);

    console.log('Files:', fileArray);
    console.log('File types:', fileTypes);
  };

  // Handle detail file selection
  const handleDetailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) {
      setDetail(null);
      return;
    }

    setDetail(files[0]);
  };

  // Remove image
  const handleRemoveImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  // Remove user manual
  const handleRemoveUserManual = () => {
    setUserManuals([]);
    setUserManualTypes([]);
    if (userManualInputRef.current) {
      userManualInputRef.current.value = '';
    }
  };

  // Remove specific user manual by index
  const handleRemoveUserManualByIndex = (index: number) => {
    setUserManuals(prev => {
      const newUserManuals = prev.filter((_, i) => i !== index);

      // Update types as well
      const newTypes = newUserManuals.map(file => file.type);
      setUserManualTypes(newTypes);

      return newUserManuals;
    });
  };

  // Remove detail
  const handleRemoveDetail = () => {
    setDetail(null);
    if (detailInputRef.current) {
      detailInputRef.current.value = '';
    }
  };

  // List of product categories
  const categoryOptions = Object.values(ProductCategory).map(category => ({
    value: category,
    label: t(`admin:marketplace.product.category.${category}`, category),
  }));

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h6" className="mb-4">
          {t('admin:marketplace.product.addNew', 'Thêm sản phẩm mới')}
        </Typography>

        <Form
          ref={formRef}
          defaultValues={defaultValues}
          schema={addProductSchema}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onSubmit={handleSubmit as any}
          className="space-y-6"
        >
          {/* Basic Information */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:marketplace.product.basicInfo', 'Thông tin cơ bản')}
            </Typography>

            <FormItem
              name="name"
              label={t('admin:marketplace.product.form.name', 'Tên sản phẩm')}
              required
            >
              <Input
                fullWidth
                placeholder={t('admin:marketplace.product.form.namePlaceholder', 'Nhập tên sản phẩm')}
              />
            </FormItem>

            <FormItem
              name="description"
              label={t('admin:marketplace.product.form.description', 'Mô tả')}
              required
            >
              <Textarea
                rows={5}
                placeholder={t('admin:marketplace.product.form.descriptionPlaceholder', 'Nhập mô tả sản phẩm')}
              />
            </FormItem>

            <FormItem
              name="category"
              label={t('admin:marketplace.product.form.category', 'Loại sản phẩm')}
              required
            >
              <Select options={categoryOptions} />
            </FormItem>

            <FormItem
              name="sourceId"
              label={t('admin:marketplace.product.form.sourceId', 'Source ID')}
              required
            >
              <Input
                fullWidth
                placeholder={t('admin:marketplace.product.form.sourceIdPlaceholder', 'Nhập source ID')}
              />
            </FormItem>
          </div>

          <Divider />

          {/* Price Information */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:marketplace.product.priceInfo', 'Thông tin giá')}
            </Typography>

            <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
              <FormItem
                name="listedPrice"
                label={t('admin:marketplace.product.form.listedPrice', 'Giá niêm yết')}
                required
              >
                <Input
                  type="number"
                  min={0}
                  fullWidth
                  placeholder="0"
                />
              </FormItem>

              <FormItem
                name="discountedPrice"
                label={t('admin:marketplace.product.form.discountedPrice', 'Giá khuyến mãi')}
                required
              >
                <Input
                  type="number"
                  min={0}
                  fullWidth
                  placeholder="0"
                />
              </FormItem>
            </FormGrid>
          </div>

          <Divider />

          {/* Media Types */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:marketplace.product.mediaTypes', 'Tệp tin & Hình ảnh')}
            </Typography>

            {/* Image Upload */}
            <FormItem
              name="images"
              label={t('admin:marketplace.product.form.images', 'Hình ảnh sản phẩm')}
              helpText={t('admin:marketplace.product.form.imagesHelp', 'Hỗ trợ định dạng: JPG, PNG')}
            >
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    ref={imageInputRef}
                    type="file"
                    accept="image/jpeg, image/png"
                    onChange={handleImageChange}
                    multiple
                    className="hidden"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => imageInputRef.current?.click()}
                    leftIcon={<Icon name="upload" />} 
                  >
                    {t('admin:marketplace.product.form.selectImages', 'Chọn hình ảnh')}
                  </Button>
                </div>

                {images.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-3">
                    {images.map((image, index) => (
                      <div key={index} className="relative group">
                        <div className="border border-border rounded-md overflow-hidden h-24">
                          <img
                            src={URL.createObjectURL(image)}
                            alt={`Preview ${index}`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <button
                          type="button"
                          onClick={() => handleRemoveImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <Icon name="x" size="sm" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </FormItem>

            <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
              {/* User Manual Upload */}
              <FormItem
                name="userManual"
                label={t('admin:marketplace.product.form.userManual', 'Hướng dẫn sử dụng')}
                helpText={t('admin:marketplace.product.form.userManualHelp', 'Hỗ trợ định dạng: PDF')}
              >
                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      ref={userManualInputRef}
                      type="file"
                      accept="application/pdf"
                      onChange={handleUserManualChange}
                      multiple
                      className="hidden"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => userManualInputRef.current?.click()}
                      leftIcon={<Icon name="upload" />}
                    >
                      {t('admin:marketplace.product.form.selectFiles', 'Chọn tệp tin')}
                    </Button>
                    <span className="ml-3 text-sm text-gray-500">
                      {userManuals.length > 0
                        ? `${t('admin:marketplace.product.form.selectedFiles', 'Đã chọn')}: ${userManuals.length} ${t('admin:marketplace.product.form.files', 'tệp tin')}`
                        : t('admin:marketplace.product.form.noFilesSelected', 'Chưa chọn tệp tin nào')}
                    </span>
                  </div>

                  {userManuals.length > 0 && (
                    <div className="mt-3">
                      <div className="flex items-center justify-between mb-2">
                        <Typography variant="body2" className="font-medium">
                          {t('admin:marketplace.product.form.selectedFiles', 'Tệp tin đã chọn')}
                        </Typography>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleRemoveUserManual}
                          leftIcon={<Icon name="trash" />}
                        >
                          {t('admin:marketplace.product.form.removeAll', 'Xóa tất cả')}
                        </Button>
                      </div>
                      <div className="space-y-2">
                        {userManuals.map((file, index) => (
                          <div key={index} className="flex items-center p-2 bg-card-muted rounded border border-border">
                            <Icon name="file-text" className="mr-2 text-primary" />
                            <span className="text-sm truncate flex-1">{file.name}</span>
                            <span className="text-xs text-gray-500 mx-2">
                              ({file.type})
                            </span>
                            <Button
                              type="button"
                              variant="ghost"
                              onClick={() => handleRemoveUserManualByIndex(index)}
                              className="text-red-500 hover:text-red-700 p-1"
                            >
                              <Icon name="x" size="sm" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </FormItem>

              {/* Detail Document Upload */}
              <FormItem
                name="detail"
                label={t('admin:marketplace.product.form.detail', 'Tài liệu chi tiết')}
                helpText={t('admin:marketplace.product.form.detailHelp', 'Hỗ trợ định dạng: PDF')}
              >
                <div className="space-y-2">
                  <div className="flex items-center">
                    <input
                      ref={detailInputRef}
                      type="file"
                      accept="application/pdf"
                      onChange={handleDetailChange}
                      className="hidden"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => detailInputRef.current?.click()}
                      leftIcon={<Icon name="upload" />}
                    >
                      {detail ? t('admin:marketplace.product.form.changeFile', 'Thay đổi file') : t('admin:marketplace.product.form.selectFile', 'Chọn file')}
                    </Button>
                  </div>

                  {detail && (
                    <div className="flex items-center mt-2 p-2 bg-card-muted rounded border border-border">
                      <Icon name="file-text" className="mr-2 text-primary" />
                      <span className="text-sm truncate flex-1">{detail.name}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        onClick={handleRemoveDetail}
                        className="ml-2 text-red-500 hover:text-red-700 p-1"
                      >
                        <Icon name="x" size="sm" />
                      </Button>
                    </div>
                  )}
                </div>
              </FormItem>
            </FormGrid>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={onCancel} type="button" disabled={isSubmitting}>
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button variant="primary" type="submit" >
              {t('common:save', 'Lưu')}
            </Button>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default AddProductForm;
