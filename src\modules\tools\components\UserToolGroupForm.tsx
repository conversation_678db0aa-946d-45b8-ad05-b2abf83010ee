import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Typography,
  Textarea,
  Loading,
  Select,
} from '@/shared/components/common';
import { useUserTools } from '../hooks/useTool';
import { ToolQueryParams } from '../types/tool.types';
import {
  CreateUserGroupToolParams,
  UpdateUserGroupToolParams,
  UserGroupToolDetail,
} from '../types';
import {
  createUserGroupToolSchema,
  updateUserGroupToolSchema,
} from '../schemas/user-group-tool.schema';

interface UserToolGroupFormProps {
  initialValues?: UserGroupToolDetail;
  onSubmit: (values: CreateUserGroupToolParams | UpdateUserGroupToolParams) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  isEdit?: boolean;
}

/**
 * Component form tạo mới/chỉnh sửa nhóm tool
 */
const UserToolGroupForm: React.FC<UserToolGroupFormProps> = ({
  initialValues,
  onSubmit,
  onCancel,
  isLoading = false,
  isEdit = false,
}) => {
  const { t } = useTranslation(['common']);
  const [selectedTools, setSelectedTools] = useState<string[]>([]);

  // Lấy danh sách tool để hiển thị trong dropdown
  const toolsQueryParams: ToolQueryParams = {
    page: 1,
    limit: 100, // Lấy tối đa 100 tools
  };

  const { data: toolsData, isLoading: isLoadingTools } = useUserTools(toolsQueryParams);

  // Cập nhật selectedTools khi có initialValues
  useEffect(() => {
    if (initialValues?.tools) {
      const toolIds = initialValues.tools.map(mapping => mapping.toolId);
      setSelectedTools(toolIds);
    }
  }, [initialValues]);

  // Xử lý submit form
  const handleSubmit = async (values: CreateUserGroupToolParams | UpdateUserGroupToolParams) => {
    // Nếu là form tạo mới, thêm toolIds vào values
    if (!isEdit) {
      (values as CreateUserGroupToolParams).toolIds = selectedTools;
    }

    await onSubmit(values);
  };

  // Xử lý thay đổi selection tools
  const handleToolsChange = (values: string[] | number[] | string | number) => {
    setSelectedTools(values as string[]);
  };

  // Tạo options cho dropdown tools
  const toolOptions =
    toolsData?.items?.map(tool => ({
      value: tool.id,
      label: tool.name,
    })) || [];

  // Schema validation
  const formSchema = isEdit ? updateUserGroupToolSchema : createUserGroupToolSchema;

  // Hiển thị loading
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading size="lg" />
      </div>
    );
  }

  // Hiển thị khi không có dữ liệu
  if (isEdit && !initialValues) {
    return (
      <Card className="p-6">
        <Typography variant="h5" className="text-center text-red-500">
          {t('tools.notFound', 'Không tìm thấy nhóm công cụ')}
        </Typography>
        <div className="flex justify-center mt-4">
          <Button variant="primary" onClick={onCancel}>
            {t('common.close', 'Đóng')}
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex justify-between items-center">
          <Typography variant="h5" className="font-bold">
            {isEdit
              ? t('tools.editToolGroup', 'Chỉnh sửa nhóm tool')
              : t('tools.createToolGroup', 'Tạo mới nhóm tool')}
          </Typography>
        </div>
        {initialValues && (
          <Typography variant="body2" className="text-gray-600 mt-1">
            {initialValues.description || t('tools.noDescription', 'Không có mô tả')}
          </Typography>
        )}
      </div>

      {/* Content */}
      <div className="flex-grow overflow-auto p-4">
        <Form
          schema={formSchema}
          onSubmit={handleSubmit}
          defaultValues={{
            name: initialValues?.name || '',
            description: initialValues?.description || '',
          }}
        >
          <div className="space-y-4">
            <FormItem name="name" label={t('tools.name', 'Tên nhóm')} required={!isEdit}>
              <Input placeholder={t('tools.namePlaceholder', 'Nhập tên nhóm tool')} />
            </FormItem>

            <FormItem name="description" label={t('tools.description', 'Mô tả')}>
              <Textarea
                placeholder={t('tools.descriptionPlaceholder', 'Nhập mô tả cho nhóm tool')}
                rows={3}
              />
            </FormItem>

            {!isEdit && (
              <FormItem label={t('tools.selectTools', 'Chọn tools')}>
                <Select
                  options={toolOptions}
                  value={selectedTools}
                  onChange={handleToolsChange}
                  placeholder={t('tools.selectToolsPlaceholder', 'Chọn các tool cho nhóm')}
                  loading={isLoadingTools}
                  multiple
                />
              </FormItem>
            )}

            {initialValues && (
              <div className="mt-6">
                <Typography variant="h6">{t('tools.groupInfo', 'Thông tin nhóm')}</Typography>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                  <div>
                    <Typography variant="subtitle2" className="text-gray-500">
                      {t('tools.id', 'ID')}
                    </Typography>
                    <Typography>{initialValues.id}</Typography>
                  </div>
                  <div>
                    <Typography variant="subtitle2" className="text-gray-500">
                      {t('tools.createdAt', 'Ngày tạo')}
                    </Typography>
                    <Typography>
                      {new Date(initialValues.createdAt).toLocaleDateString()}
                    </Typography>
                  </div>
                  <div>
                    <Typography variant="subtitle2" className="text-gray-500">
                      {t('tools.updatedAt', 'Cập nhật lần cuối')}
                    </Typography>
                    <Typography>
                      {new Date(initialValues.updatedAt).toLocaleDateString()}
                    </Typography>
                  </div>
                  <div>
                    <Typography variant="subtitle2" className="text-gray-500">
                      {t('tools.toolCount', 'Số lượng công cụ')}
                    </Typography>
                    <Typography>{initialValues.tools?.length || 0}</Typography>
                  </div>
                </div>
              </div>
            )}

            {initialValues && initialValues.tools && initialValues.tools.length > 0 && (
              <div className="mt-6">
                <Typography variant="h6">
                  {t('tools.toolsInGroup', 'Công cụ trong nhóm')}
                </Typography>
                <div className="space-y-2 mt-3">
                  {initialValues.tools.map(mapping => (
                    <Card key={mapping.id} className="p-3 border">
                      <div className="flex justify-between items-start">
                        <div>
                          <Typography variant="subtitle1" className="font-semibold">
                            {mapping.tool.name}
                          </Typography>
                          <Typography variant="body2" className="text-gray-600 mt-1">
                            {mapping.tool.description || t('tools.noDescription', 'Không có mô tả')}
                          </Typography>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-end space-x-2 pt-6 mt-6 border-t">
            <Button variant="outline" onClick={onCancel} disabled={isLoading}>
              {isEdit ? t('common.cancel', 'Hủy') : t('common.cancel', 'Hủy')}
            </Button>
            <Button type="submit" variant="primary" isLoading={isLoading}>
              {isEdit ? t('common.save', 'Lưu') : t('common.create', 'Tạo mới')}
            </Button>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default UserToolGroupForm;
