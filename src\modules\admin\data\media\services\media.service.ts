import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import {
  AdminMediaDto,
  DeleteMediaResult,
  MediaQueryDto,
  PaginatedMediaResult,
} from '../types/media.types';

// Đường dẫn API chung cho admin media
const ADMIN_MEDIA_API_PATH = '/admin/media';

/**
 * Service cho quản lý media của admin
 */
export const MediaService = {
  /**
   * Lấy danh sách media cho admin
   * @param params Tham số truy vấn
   * @returns Promise với danh sách media có phân trang
   */
  getMediaList: async (params?: MediaQueryDto): Promise<ApiResponseDto<PaginatedMediaResult>> => {
    const response = await apiClient.get<PaginatedMediaResult>(ADMIN_MEDIA_API_PATH, {
      params,
    });
    return response;
  },

  /**
   * <PERSON><PERSON><PERSON> thông tin chi tiết media theo ID
   * @param id ID của media
   * @returns Promise với thông tin chi tiết media
   */
  getMediaById: async (id: string): Promise<ApiResponseDto<AdminMediaDto>> => {
    const response = await apiClient.get<AdminMediaDto>(`${ADMIN_MEDIA_API_PATH}/${id}`);
    return response;
  },

  /**
   * Xóa media theo ID
   * @param id ID của media
   * @returns Promise với kết quả xóa
   */
  deleteMedia: async (id: string): Promise<ApiResponseDto<null>> => {
    const response = await apiClient.delete<null>(`${ADMIN_MEDIA_API_PATH}/${id}`);
    return response;
  },

  /**
   * Xóa nhiều media theo danh sách ID
   * @param ids Danh sách ID của media cần xóa
   * @returns Promise với kết quả xóa
   */
  deleteMultipleMedia: async (ids: string[]): Promise<ApiResponseDto<DeleteMediaResult>> => {
    const response = await apiClient.delete<DeleteMediaResult>(`${ADMIN_MEDIA_API_PATH}/batch`, {
      data: { ids },
    });
    return response;
  },

  /**
   * Xóa liên kết agent media theo danh sách ID
   * @param mediaIds Danh sách ID của media cần xóa liên kết
   * @returns Promise với kết quả xóa liên kết
   */
  deleteAgentMedia: async (mediaIds: string[]): Promise<ApiResponseDto<DeleteMediaResult>> => {
    const response = await apiClient.delete<DeleteMediaResult>(
      `${ADMIN_MEDIA_API_PATH}/agent-media`,
      {
        data: mediaIds,
      }
    );
    return response;
  },
};
