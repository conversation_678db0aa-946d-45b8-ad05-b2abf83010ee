import React from 'react';
import { Container } from '@/shared/components/common';
import { ModelBaseForm } from '../components';
import { useNavigate } from 'react-router-dom';

const CreateModelBasePage: React.FC = () => {
  const navigate = useNavigate();

  const handleSuccess = () => {
    // Chuyển hướng đến trang danh sách model base sau khi tạo thành công
    navigate('/model-training/model-bases');
  };

  return (
    <Container>
      <ModelBaseForm onSuccess={handleSuccess} />
    </Container>
  );
};

export default CreateModelBasePage;
