import { CustomField, CustomFieldType, CustomFieldStatus } from '../types/custom-field.types';

/**
 * Mock data cho Custom Fields
 */
export const mockCustomFields: CustomField[] = [
  {
    createdBy: 4,
    displayName: 'Độ tuổi',
    fieldKey: 'do_tuoi',
    description: '<PERSON><PERSON>ảng độ tuổi của khách hàng',
    dataType: 'integer',
    status: CustomFieldStatus.ACTIVE,
    createdAt: '2024-06-15T08:00:00Z',
    updatedAt: '2024-06-15T08:00:00Z',
  },
  {
    createdBy: 4,
    displayName: 'Địa chỉ',
    fieldKey: 'dia_chi',
    description: 'Đ<PERSON>a chỉ của khách hàng',
    dataType: CustomFieldType.TEXT,
    status: CustomFieldStatus.ACTIVE,
    createdAt: '2024-06-16T09:30:00Z',
    updatedAt: '2024-06-16T09:30:00Z',
  },
  {
    createdBy: 4,
    displayName: '<PERSON><PERSON><PERSON> sinh',
    fieldKey: 'ngay_sinh',
    description: '<PERSON><PERSON><PERSON> sinh của khách hàng',
    dataType: CustomFieldType.DATE,
    status: CustomFieldStatus.ACTIVE,
    createdAt: '2024-06-17T10:45:00Z',
    updatedAt: '2024-06-17T10:45:00Z',
  },
  {
    createdBy: 4,
    displayName: 'Đã xác thực',
    fieldKey: 'da_xac_thuc',
    description: 'Khách hàng đã xác thực danh tính',
    dataType: CustomFieldType.BOOLEAN,
    status: CustomFieldStatus.ACTIVE,
    createdAt: '2024-06-18T11:15:00Z',
    updatedAt: '2024-06-18T11:15:00Z',
  },
  {
    createdBy: 4,
    displayName: 'Ghi chú',
    fieldKey: 'ghi_chu',
    description: 'Ghi chú về khách hàng',
    dataType: CustomFieldType.TEXT,
    status: CustomFieldStatus.INACTIVE,
    createdAt: '2024-06-19T12:30:00Z',
    updatedAt: '2024-06-19T12:30:00Z',
  },
];

/**
 * Hàm lấy danh sách custom fields với phân trang và lọc
 */
export const getCustomFields = (params: {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: string;
}) => {
  let filteredFields = [...mockCustomFields];

  // Xử lý tìm kiếm
  if (params.search) {
    const searchLower = params.search.toLowerCase();
    filteredFields = filteredFields.filter(
      field =>
        field.displayName.toLowerCase().includes(searchLower) ||
        field.fieldKey.toLowerCase().includes(searchLower) ||
        (field.description && field.description.toLowerCase().includes(searchLower))
    );
  }

  // Xử lý sắp xếp
  if (params.sortBy) {
    const sortBy = params.sortBy as keyof CustomField;
    const sortDirection = params.sortDirection === 'DESC' ? -1 : 1;

    filteredFields.sort((a, b) => {
      // Kiểm tra xem thuộc tính có tồn tại không
      const aValue = a[sortBy];
      const bValue = b[sortBy];

      // Nếu một trong hai giá trị là undefined, xử lý đặc biệt
      if (aValue === undefined && bValue === undefined) return 0;
      if (aValue === undefined) return -1 * sortDirection;
      if (bValue === undefined) return 1 * sortDirection;

      // So sánh các giá trị
      if (aValue < bValue) return -1 * sortDirection;
      if (aValue > bValue) return 1 * sortDirection;
      return 0;
    });
  }

  // Xử lý phân trang
  const page = params.page || 1;
  const limit = params.limit || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedFields = filteredFields.slice(startIndex, endIndex);

  const totalPages = Math.ceil(filteredFields.length / limit);

  return {
    data: paginatedFields,
    meta: {
      total: filteredFields.length,
      page: page,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
      limit: limit,
      totalPages: totalPages
    },
  };
};

/**
 * Hàm lấy custom field theo fieldKey
 */
export const getCustomFieldById = (fieldKey: string) => {
  const field = mockCustomFields.find(field => field.fieldKey === fieldKey);

  if (!field) {
    throw new Error('Custom field not found');
  }

  return field;
};
