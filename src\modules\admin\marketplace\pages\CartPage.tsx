import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Table, Tooltip, IconCard } from '@/shared/components/common';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';

// Import components
import CartDetailForm from '@/modules/admin/marketplace/components/forms/CartDetailForm';

// Import hooks
import { useAllCarts, useCartById } from '@/modules/admin/marketplace/hooks/useCart';

// Import types
import { Cart, CartItem, CartResponse } from '@/modules/admin/marketplace/types/cart.types';

/**
 * Trang quản lý giỏ hàng trong marketplace
 */
const CartPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const [userId, setUserId] = useState<number>(0);
  const [selectedCartId, setSelectedCartId] = useState<string | number>('');
  const [cart, setCart] = useState<Cart | null>(null);
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [allCarts, setAllCarts] = useState<CartResponse[]>([]);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isDetailFormVisible,
    showForm: showDetailForm,
    hideForm: hideDetailForm,
  } = useSlideForm();

  // Hooks để gọi API
  const { data: allCartsData, isLoading: isLoadingAllCarts } = useAllCarts();
  const { data: cartByIdData, isLoading: isLoadingCartById } = useCartById(selectedCartId);

  // Cập nhật state khi có dữ liệu danh sách giỏ hàng
  useEffect(() => {
    if (allCartsData && allCartsData.result) {
      // Kiểm tra cấu trúc dữ liệu và xử lý phù hợp
      if (Array.isArray(allCartsData.result)) {
        // Nếu result là một mảng, sử dụng trực tiếp
        setAllCarts(allCartsData.result);
      } else {
        // Kiểm tra xem result có thuộc tính items không
        const resultWithItems = allCartsData.result as { items?: CartResponse[] };
        if (resultWithItems.items && Array.isArray(resultWithItems.items)) {
          // Nếu result có thuộc tính items là một mảng
          setAllCarts(resultWithItems.items);
        } else {
          // Trường hợp khác, đặt mảng rỗng
          setAllCarts([]);
        }
      }
    }
  }, [allCartsData]);

  // Cập nhật state khi có dữ liệu chi tiết giỏ hàng theo ID
  useEffect(() => {
    if (cartByIdData && cartByIdData.result) {
      setCart(cartByIdData.result);
      setCartItems(cartByIdData.result.items || []);
    }
  }, [cartByIdData]);

  // Xử lý tìm kiếm người dùng
  const handleSearch = useCallback((term: string) => {
    // Nếu là số, tìm theo userId
    const parsedUserId = parseInt(term);
    if (!isNaN(parsedUserId)) {
      setUserId(parsedUserId);
      setSelectedCartId('');
    }
    // Nếu là chuỗi, tìm theo cartId
    else if (term) {
      setSelectedCartId(term);
      setUserId(0);
    }
  }, []);

  // Xử lý đóng form chi tiết
  const handleCloseDetailForm = useCallback(() => {
    hideDetailForm();
    // Đặt timeout để đảm bảo animation đóng hoàn tất trước khi xóa dữ liệu
    setTimeout(() => {
      setSelectedCartId('');
      setCart(null);
      setCartItems([]);
    }, 300);
  }, [hideDetailForm]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'product',
        title: t('admin:marketplace.cartDetails.table.product', 'Sản phẩm'),
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        dataIndex: 'product.name' as any,
        width: '30%',
        render: (_: unknown, record: CartItem) => (
          <div className="flex flex-col">
            <span className="font-medium">{record.product.name}</span>
            <span className="text-xs text-gray-500">ID: {record.product.id}</span>
          </div>
        ),
      },
      {
        key: 'price',
        title: t('admin:marketplace.cartDetails.table.price', 'Đơn giá'),
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        dataIndex: 'product.discountedPrice' as any,
        width: '15%',
        render: (value: unknown) => {
          return <span>{typeof value === 'number' ? value : 0} points</span>;
        },
      },
      {
        key: 'quantity',
        title: t('admin:marketplace.cartDetails.table.quantity', 'Số lượng'),
        dataIndex: 'quantity',
        width: '15%',
      },
      {
        key: 'total',
        title: t('admin:marketplace.cartDetails.table.total', 'Thành tiền'),
        width: '15%',
        render: (_: unknown, record: CartItem) => {
          const total = record.quantity * record.product.discountedPrice;
          return <span>{total} points</span>;
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        render: (_: unknown) => (
          <div className="flex space-x-2">
            <Tooltip content={t('admin:marketplace.cart.edit', 'Sửa số lượng')}>
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => console.log('Chức năng chỉnh sửa không khả dụng')}
              />
            </Tooltip>

          
          </div>
        ),
      },
    ];

    // Lọc các cột dựa trên visibleColumns
    if (visibleColumns.length === 0) {
      return allColumns;
    }

    const visibleColumnIds = visibleColumns.filter(col => col.visible).map(col => col.id);

    // Luôn hiển thị cột actions
    return allColumns.filter(col => col.key === 'actions' || visibleColumnIds.includes(col.key));
  }, [t, visibleColumns]);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: 'Tất cả', visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns]);

  // Tạo cột cho bảng danh sách giỏ hàng
  const cartListColumns = useMemo(() => {
    return [
      {
        key: 'id',
        title: t('admin:marketplace.cartDetails.table.id', 'ID'),
        dataIndex: 'id',
        width: '15%',
      },
      {
        key: 'user',
        title: t('admin:marketplace.cartDetails.table.customer', 'Người dùng'),
        width: '20%',
        render: (_: unknown, record: CartResponse) => (
          <div className="flex flex-col">
            <span className="font-medium">{record.user?.name || 'N/A'}</span>
            <span className="text-xs text-gray-500">ID: {record.user?.id || 'N/A'}</span>
          </div>
        ),
      },
      {
        key: 'totalItems',
        title: t('admin:marketplace.cartDetails.table.items', 'Số sản phẩm'),
        width: '15%',
        render: (_: unknown, record: CartResponse) => {
          // Safely access items property which might not exist in some API responses
          return <span>{record.items ? record.items.length : 0}</span>;
        },
      },
      {
        key: 'totalValue',
        title: t('admin:marketplace.cartDetails.table.total', 'Tổng tiền'),
        dataIndex: 'totalValue',
        width: '15%',
        render: (_: unknown, record: CartResponse) => <span>{record.totalValue || 0} points</span>,
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '15%',
        render: (_: unknown, record: CartResponse) => (
          <div className="flex space-x-2">
            <Tooltip content={t('admin:marketplace.cart.view', 'Xem chi tiết')}>
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => {
                  setSelectedCartId(record.id);
                  setUserId(0);
                  showDetailForm();
                }}
              />
            </Tooltip>
          </div>
        ),
      },
    ];
  }, [t, showDetailForm]);

  return (
    <div>
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          items={[
            {
              id: 'clear',
              label: t('admin:marketplace.cart.clearCart', 'Xóa giỏ hàng'),
              icon: 'delete-bin',
              onClick: () => console.log('Chức năng xóa giỏ hàng không khả dụng'),
              disabled: !cart || cartItems.length === 0,
            },
          ]}
          onColumnVisibilityChange={handleColumnVisibilityChange}
          columns={visibleColumns}
          showColumnFilter={true}
        />
      </div>

      {/* SlideInForm cho form xem chi tiết */}
      <SlideInForm isVisible={isDetailFormVisible}>
        <CartDetailForm cart={cart} onClose={handleCloseDetailForm} />
      </SlideInForm>

      {/* Hiển thị danh sách giỏ hàng khi không có ID cụ thể */}
      {!userId && !selectedCartId && (
        <Card className="overflow-hidden">
          <Typography variant="h6" className="p-4 border-b">
            {t('admin:marketplace.cart.allCarts', 'Danh sách giỏ hàng')}
          </Typography>
          <Table<CartResponse>
            columns={cartListColumns}
            data={allCarts}
            rowKey="id"
            loading={isLoadingAllCarts}
            loadingText={t('admin:marketplace.cart.loading', 'Đang tải dữ liệu...')}
          />
        </Card>
      )}

      {/* Hiển thị chi tiết giỏ hàng khi có ID */}
      {(userId > 0 || selectedCartId) && (
        <Card className="overflow-hidden">
          <Table<CartItem>
            columns={filteredColumns}
            data={cartItems}
            rowKey="id"
            loading={isLoadingCartById}
            loadingText={t('admin:marketplace.cart.loading', 'Đang tải dữ liệu...')}
          />
        </Card>
      )}
    </div>
  );
};

export default CartPage;
