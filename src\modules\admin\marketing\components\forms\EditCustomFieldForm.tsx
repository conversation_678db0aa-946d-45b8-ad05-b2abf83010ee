import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Typography,
  Loading,
} from '@/shared/components/common';
import { z } from 'zod';
import { TFunction } from 'i18next';

// Import enum từ types
import { CustomFieldType } from '../../types/custom-field.types';
import { useCustomField } from '../../hooks';

// Schema for the edit form
const getFormSchema = (t: TFunction) =>
  z.object({
    // Add fieldKey to schema but don't validate as it's readonly
    fieldKey: z.string(),
    displayName: z
      .string()
      .min(1, t('marketingAdmin:customFields.form.validation.displayNameRequired')),
    dataType: z.union([
      z.nativeEnum(CustomFieldType),
      z.literal('integer')
    ], {
      errorMap: () => ({
        message: t('marketingAdmin:customFields.form.validation.dataTypeRequired'),
      }),
    }),
    description: z.string().optional(),
  });

export type EditCustomFieldFormValues = z.infer<ReturnType<typeof getFormSchema>>;

interface EditCustomFieldFormProps {
  id: string;
  onSubmit: (id: string, values: Record<string, unknown>) => void;
  onCancel: () => void;
}

/**
 * Component for editing custom fields
 */
const EditCustomFieldForm: React.FC<EditCustomFieldFormProps> = ({ id, onSubmit, onCancel }) => {
  const { t } = useTranslation(['marketingAdmin', 'common']);
  const { data: customField, isLoading } = useCustomField(id);

  const formSchema = getFormSchema(t);

  // Handle form submission
  const handleSubmit = (values: Record<string, unknown>) => {
    onSubmit(id, values);
  };

  // Prepare default values for the form
  const defaultValues = customField
    ? {
        fieldKey: customField.fieldKey,
        displayName: customField.displayName,
        dataType: customField.dataType,
        description: customField.description || '',
      }
    : undefined;

  if (isLoading) {
    return (
      <Card className="mb-4 p-4 min-h-[300px] flex items-center justify-center">
        <Loading className="rounded-lg" />
      </Card>
    );
  }

  if (!customField) {
    return (
      <Card className="mb-4 p-4">
        <Typography variant="body1" className="text-red-500">
          {t('marketingAdmin:customFields.notFound')}
        </Typography>
        <div className="flex justify-end mt-4">
          <Button variant="outline" onClick={onCancel}>
            {t('common:cancel')}
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card className="mb-4 p-4">
      <Typography variant="h5" className="mb-4">
        {t('marketingAdmin:customFields.edit')}
      </Typography>

      <Form schema={formSchema} onSubmit={handleSubmit} defaultValues={defaultValues} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="fieldKey" label={t('marketingAdmin:customFields.form.fieldKeyLabel')}>
            <Input
              value={customField.fieldKey}
              fullWidth
              disabled
              helperText={t('marketingAdmin:customFields.form.fieldKeyReadOnly')}
            />
          </FormItem>

          <FormItem name="displayName" label={t('marketingAdmin:customFields.form.displayNameLabel')} required>
            <Input
              placeholder={t('marketingAdmin:customFields.form.displayNamePlaceholder')}
              fullWidth
            />
          </FormItem>
        </div>

        <FormItem name="dataType" label={t('marketingAdmin:customFields.form.dataTypeLabel')} required>
          <Select
            options={[
              { value: CustomFieldType.TEXT, label: t('marketingAdmin:customFields.types.text') },
              { value: CustomFieldType.NUMBER, label: t('marketingAdmin:customFields.types.number') },
              { value: CustomFieldType.DATE, label: t('marketingAdmin:customFields.types.date') },
              { value: CustomFieldType.BOOLEAN, label: t('marketingAdmin:customFields.types.boolean') },
              // Add integer type for API compatibility
              { value: 'integer', label: t('marketingAdmin:customFields.types.integer') },
            ]}
            placeholder={t('marketingAdmin:customFields.form.dataTypePlaceholder')}
            fullWidth
          />
        </FormItem>

        <FormItem name="description" label={t('marketingAdmin:customFields.form.descriptionLabel')}>
          <Input
            placeholder={t('marketingAdmin:customFields.form.descriptionPlaceholder')}
            fullWidth
          />
        </FormItem>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onCancel}>
            {t('common:cancel')}
          </Button>
          <Button type="submit" variant="primary">
            {t('common:save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditCustomFieldForm;
