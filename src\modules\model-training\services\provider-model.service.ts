import { apiClient } from '@/shared/api';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  ProviderModel,
  CreateProviderModelDto,
  UpdateProviderModelDto,
  ProviderModelQueryParams,
} from '../types/provider-model.types';

const API_BASE_URL = '/user/provider-model';

/**
 * Lấy danh sách provider model
 * @param params Tham số truy vấn
 * @returns Danh sách provider model đã phân trang
 */
export const getProviderModels = async (
  params: ProviderModelQueryParams = {}
): Promise<PaginatedResult<ProviderModel>> => {
  const response = await apiClient.get<PaginatedResult<ProviderModel>>(API_BASE_URL, { params });
  return response.result;
};

/**
 * Lấy thông tin provider model theo ID
 * @param id ID của provider model
 * @returns Thông tin chi tiết của provider model
 */
export const getProviderModelById = async (id: string): Promise<ProviderModel> => {
  const response = await apiClient.get<ProviderModel>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * Tạo provider model mới
 * @param data Thông tin provider model cần tạo
 * @returns Thông tin provider model đã tạo
 */
export const createProviderModel = async (data: CreateProviderModelDto): Promise<ProviderModel> => {
  const response = await apiClient.post<ProviderModel>(API_BASE_URL, data);
  return response.result;
};

/**
 * Cập nhật thông tin provider model
 * @param id ID của provider model
 * @param data Thông tin cần cập nhật
 * @returns Thông tin provider model đã cập nhật
 */
export const updateProviderModel = async (id: string, data: UpdateProviderModelDto): Promise<ProviderModel> => {
  const response = await apiClient.patch<ProviderModel>(`${API_BASE_URL}/${id}`, data);
  return response.result;
};

/**
 * Xóa provider model
 * @param id ID của provider model cần xóa
 * @returns Kết quả xóa
 */
export const deleteProviderModel = async (id: string): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(`${API_BASE_URL}/${id}`);
  return { success: response.code === 0 };
};
