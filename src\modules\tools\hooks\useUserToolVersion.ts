import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { userToolVersionService } from '../services/user-tool-version.service';
import { EditUserToolVersionParams } from '../types';
import { TOOL_QUERY_KEYS } from './useTool';

// Định nghĩa các query key
export const USER_TOOL_VERSION_QUERY_KEYS = {
  all: ['user-tool-versions'] as const,
  details: () => [...USER_TOOL_VERSION_QUERY_KEYS.all, 'detail'] as const,
  detail: (toolId: string, versionId: string) =>
    [...USER_TOOL_VERSION_QUERY_KEYS.details(), toolId, versionId] as const,
};

/**
 * Hook lấy thông tin chi tiết phiên bản tool
 * @param toolId ID của tool
 * @param versionId ID của phiên bản
 * @returns Query object
 */
export const useUserToolVersionDetail = (toolId: string, versionId: string) => {
  return useQuery({
    queryKey: USER_TOOL_VERSION_QUERY_KEYS.detail(toolId, versionId),
    queryFn: () => userToolVersionService.getVersionById(toolId, versionId),
    enabled: !!toolId && !!versionId,
  });
};

/**
 * Hook chỉnh sửa phiên bản tool
 * @returns Mutation object
 */
export const useEditUserToolVersion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      toolId,
      versionId,
      data
    }: {
      toolId: string;
      versionId: string;
      data: EditUserToolVersionParams
    }) => userToolVersionService.editVersion(toolId, versionId, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết tool
      queryClient.invalidateQueries({
        queryKey: TOOL_QUERY_KEYS.detail(variables.toolId),
      });
    },
  });
};

/**
 * Hook đặt phiên bản mặc định cho tool
 * @returns Mutation object
 */
export const useSetDefaultUserToolVersion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ toolId, versionId }: { toolId: string; versionId: string }) =>
      userToolVersionService.setDefaultVersion(toolId, versionId),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết tool
      queryClient.invalidateQueries({
        queryKey: TOOL_QUERY_KEYS.detail(variables.toolId),
      });
    },
  });
};

/**
 * Hook xóa phiên bản tool
 * @returns Mutation object
 */
export const useDeleteUserToolVersion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ toolId, versionId }: { toolId: string; versionId: string }) =>
      userToolVersionService.deleteVersion(toolId, versionId),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết tool
      queryClient.invalidateQueries({
        queryKey: TOOL_QUERY_KEYS.detail(variables.toolId),
      });
      // Invalidate chi tiết phiên bản
      queryClient.invalidateQueries({
        queryKey: USER_TOOL_VERSION_QUERY_KEYS.detail(variables.toolId, variables.versionId),
      });
    },
  });
};
