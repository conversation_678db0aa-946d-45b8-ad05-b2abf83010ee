import { z } from 'zod';
import { MediaStatus } from '../types/media.types';

/**
 * Schema cho phản hồi API
 */
export const ApiResponseSchema = <T extends z.ZodTypeAny>(resultSchema: T) =>
  z.object({
    code: z.number(),
    message: z.string(),
    result: resultSchema,
  });

/**
 * Schema cho thông tin phân trang
 */
export const PaginationMetaSchema = z.object({
  currentPage: z.number(),
  itemCount: z.number(),
  itemsPerPage: z.number(),
  totalItems: z.number(),
  totalPages: z.number(),
});

/**
 * Schema cho thông tin media của admin
 */
export const AdminMediaSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  size: z.number().min(0),
  tags: z.array(z.string()).optional(),
  storageKey: z.string(),
  ownedBy: z.number(),
  author: z.string().optional(),
  avatar: z.string().optional(),
  createdAt: z.number(),
  updatedAt: z.number(),
  status: z.nativeEnum(MediaStatus),
  viewUrl: z.string().url().optional(),
});

/**
 * Schema cho kết quả phân trang media
 */
export const PaginatedMediaResultSchema = z.object({
  items: z.array(AdminMediaSchema),
  meta: PaginationMetaSchema,
});

/**
 * Schema cho kết quả xóa nhiều media
 */
export const DeleteMediaResultSchema = z.object({
  deletedIds: z.array(z.string()),
  skippedIds: z.array(z.string()),
  failedIds: z.array(
    z.object({
      id: z.string(),
      reason: z.string(),
    })
  ),
});

/**
 * Schema cho tham số truy vấn media
 */
export const MediaQuerySchema = z.object({
  page: z.number().optional(),
  limit: z.number().optional(),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
  status: z.nativeEnum(MediaStatus).optional(),
  ownedBy: z.number().optional(),
  tags: z.array(z.string()).optional(),
});
