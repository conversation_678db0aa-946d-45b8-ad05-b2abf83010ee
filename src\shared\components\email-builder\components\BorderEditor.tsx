import React from 'react';
import { Input, Typography } from '@/shared/components/common';
import { EmailElement } from '../types';

interface BorderEditorProps {
  selectedElement: EmailElement;
  updateSelectedElement: (property: string, value: unknown) => void;
}

const BorderEditor: React.FC<BorderEditorProps> = ({
  selectedElement,
  updateSelectedElement
}) => {
  return (
    <div className="space-y-4">
      {/* Border Section */}
      <div className="p-4 rounded-md">
        <Typography variant="body1" className="font-medium mb-2">Border</Typography>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <Typography variant="body2" className="text-xs mb-1">Border Width</Typography>
            <div className="flex items-center">
              <Input
                type="number"
                value={selectedElement.style?.borderWidth || 0}
                onChange={(e) => updateSelectedElement('style.borderWidth', parseInt(e.target.value) || 0)}
                className="w-full"
              />
              <span className="ml-2">px</span>
            </div>
          </div>

          <div>
            <Typography variant="body2" className="text-xs mb-1">Border Radius</Typography>
            <div className="flex items-center">
              <Input
                type="number"
                value={selectedElement.style?.borderRadius || 0}
                onChange={(e) => updateSelectedElement('style.borderRadius', parseInt(e.target.value) || 0)}
                className="w-full"
              />
              <span className="ml-2">px</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <Typography variant="body2" className="text-xs mb-1">Border Style</Typography>
            <select
              value={selectedElement.style?.borderStyle || 'solid'}
              onChange={(e) => updateSelectedElement('style.borderStyle', e.target.value)}
              className="w-full p-2 border rounded"
            >
              <option value="none">None</option>
              <option value="solid">Solid</option>
              <option value="dashed">Dashed</option>
              <option value="dotted">Dotted</option>
              <option value="double">Double</option>
            </select>
          </div>

          <div>
            <Typography variant="body2" className="text-xs mb-1">Border Color</Typography>
            <div className="flex items-center gap-2">
              <input
                type="color"
                value={selectedElement.style?.borderColor || '#000000'}
                onChange={(e) => updateSelectedElement('style.borderColor', e.target.value)}
                className="w-8 h-8 rounded"
              />
              <Input
                value={selectedElement.style?.borderColor || '#000000'}
                onChange={(e) => updateSelectedElement('style.borderColor', e.target.value)}
                className="flex-1"
              />
            </div>
          </div>
        </div>

        <div>
          <Typography variant="body2" className="text-xs mb-1">Individual Borders</Typography>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Typography variant="body2" className="text-xs mb-1">Top</Typography>
              <div className="flex items-center">
                <Input
                  type="number"
                  value={selectedElement.style?.borderTopWidth || selectedElement.style?.borderWidth || 0}
                  onChange={(e) => updateSelectedElement('style.borderTopWidth', parseInt(e.target.value) || 0)}
                  className="w-full"
                />
                <span className="ml-2">px</span>
              </div>
            </div>
            <div>
              <Typography variant="body2" className="text-xs mb-1">Right</Typography>
              <div className="flex items-center">
                <Input
                  type="number"
                  value={selectedElement.style?.borderRightWidth || selectedElement.style?.borderWidth || 0}
                  onChange={(e) => updateSelectedElement('style.borderRightWidth', parseInt(e.target.value) || 0)}
                  className="w-full"
                />
                <span className="ml-2">px</span>
              </div>
            </div>
            <div>
              <Typography variant="body2" className="text-xs mb-1">Bottom</Typography>
              <div className="flex items-center">
                <Input
                  type="number"
                  value={selectedElement.style?.borderBottomWidth || selectedElement.style?.borderWidth || 0}
                  onChange={(e) => updateSelectedElement('style.borderBottomWidth', parseInt(e.target.value) || 0)}
                  className="w-full"
                />
                <span className="ml-2">px</span>
              </div>
            </div>
            <div>
              <Typography variant="body2" className="text-xs mb-1">Left</Typography>
              <div className="flex items-center">
                <Input
                  type="number"
                  value={selectedElement.style?.borderLeftWidth || selectedElement.style?.borderWidth || 0}
                  onChange={(e) => updateSelectedElement('style.borderLeftWidth', parseInt(e.target.value) || 0)}
                  className="w-full"
                />
                <span className="ml-2">px</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Box Shadow Section */}
      <div className="p-4 rounded-md">
        <Typography variant="body1" className="font-medium mb-2">Box Shadow</Typography>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <Typography variant="body2" className="text-xs mb-1">Horizontal Offset</Typography>
            <div className="flex items-center">
              <Input
                type="number"
                value={selectedElement.style?.boxShadowX || 0}
                onChange={(e) => {
                  const x = parseInt(e.target.value) || 0;
                  const y = selectedElement.style?.boxShadowY || 0;
                  const blur = selectedElement.style?.boxShadowBlur || 0;
                  const color = selectedElement.style?.boxShadowColor || 'rgba(0,0,0,0.1)';
                  updateSelectedElement('style.boxShadowX', x);
                  updateSelectedElement('style.boxShadow', `${x}px ${y}px ${blur}px ${color}`);
                }}
                className="w-full"
              />
              <span className="ml-2">px</span>
            </div>
          </div>

          <div>
            <Typography variant="body2" className="text-xs mb-1">Vertical Offset</Typography>
            <div className="flex items-center">
              <Input
                type="number"
                value={selectedElement.style?.boxShadowY || 0}
                onChange={(e) => {
                  const x = selectedElement.style?.boxShadowX || 0;
                  const y = parseInt(e.target.value) || 0;
                  const blur = selectedElement.style?.boxShadowBlur || 0;
                  const color = selectedElement.style?.boxShadowColor || 'rgba(0,0,0,0.1)';
                  updateSelectedElement('style.boxShadowY', y);
                  updateSelectedElement('style.boxShadow', `${x}px ${y}px ${blur}px ${color}`);
                }}
                className="w-full"
              />
              <span className="ml-2">px</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Typography variant="body2" className="text-xs mb-1">Blur Radius</Typography>
            <div className="flex items-center">
              <Input
                type="number"
                value={selectedElement.style?.boxShadowBlur || 0}
                onChange={(e) => {
                  const x = selectedElement.style?.boxShadowX || 0;
                  const y = selectedElement.style?.boxShadowY || 0;
                  const blur = parseInt(e.target.value) || 0;
                  const color = selectedElement.style?.boxShadowColor || 'rgba(0,0,0,0.1)';
                  updateSelectedElement('style.boxShadowBlur', blur);
                  updateSelectedElement('style.boxShadow', `${x}px ${y}px ${blur}px ${color}`);
                }}
                className="w-full"
              />
              <span className="ml-2">px</span>
            </div>
          </div>

          <div>
            <Typography variant="body2" className="text-xs mb-1">Shadow Color</Typography>
            <div className="flex items-center gap-2">
              <input
                type="color"
                value={selectedElement.style?.boxShadowColor || '#000000'}
                onChange={(e) => {
                  const x = selectedElement.style?.boxShadowX || 0;
                  const y = selectedElement.style?.boxShadowY || 0;
                  const blur = selectedElement.style?.boxShadowBlur || 0;
                  const color = e.target.value;
                  updateSelectedElement('style.boxShadowColor', color);
                  updateSelectedElement('style.boxShadow', `${x}px ${y}px ${blur}px ${color}`);
                }}
                className="w-8 h-8 rounded"
              />
              <Input
                value={selectedElement.style?.boxShadowColor || '#000000'}
                onChange={(e) => {
                  const x = selectedElement.style?.boxShadowX || 0;
                  const y = selectedElement.style?.boxShadowY || 0;
                  const blur = selectedElement.style?.boxShadowBlur || 0;
                  const color = e.target.value;
                  updateSelectedElement('style.boxShadowColor', color);
                  updateSelectedElement('style.boxShadow', `${x}px ${y}px ${blur}px ${color}`);
                }}
                className="flex-1"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BorderEditor;
