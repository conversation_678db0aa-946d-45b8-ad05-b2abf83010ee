import { ProviderType } from './provider.types';

/**
 * Interface cho thông tin provider model
 */
export interface ProviderModel {
  /**
   * ID của provider model
   */
  id?: string;

  /**
   * Tên của provider model
   */
  name: string;

  /**
   * Loại provider model
   */
  type: ProviderType;

  /**
   * API key của provider model
   */
  apiKey?: string;

  /**
   * Thời gian tạo (unix timestamp)
   */
  createdAt?: number;
}

/**
 * Interface cho request tạo provider model mới
 */
export interface CreateProviderModelDto {
  /**
   * Tên của provider model
   */
  name: string;

  /**
   * Loại provider model
   */
  type: ProviderType;

  /**
   * API key của provider model
   */
  apiKey: string;
}

/**
 * Interface cho request cập nhật provider model
 */
export interface UpdateProviderModelDto {
  /**
   * Tên của provider model
   */
  name?: string;

  /**
   * API key của provider model
   */
  apiKey?: string;
}

/**
 * Interface cho tham số truy vấn danh sách provider model
 */
export interface ProviderModelQueryParams {
  /**
   * Trang hiện tại
   */
  page?: number;

  /**
   * Số lượng kết quả trên một trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm
   */
  search?: string;

  /**
   * Trường sắp xếp
   */
  sortBy?: string;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC';

  /**
   * Lọc theo loại provider model
   */
  type?: ProviderType;
}
