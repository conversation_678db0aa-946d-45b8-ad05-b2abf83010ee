import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import {
  VirtualWarehouse,
  Warehouse,
  WarehouseCustomField,
} from '@modules/business/entities';
import { PaginatedResult } from '@common/response'; // Using user's simpler path
import { QueryVirtualWarehouseDto as QueryVirtualWarehouseDtoUser } from '@modules/business/user/dto/warehouse'; // Aliased DTO
import { QueryVirtualWarehouseDto as QueryVirtualWarehouseDtoAdmin } from '@modules/business/admin/dto/warehouse'; // Aliased DTO

/**
 * Repository xử lý truy vấn dữ liệu cho entity VirtualWarehouse,
 * kết hợp chức năng từ cả user và admin context.
 */
@Injectable()
export class VirtualWarehouseRepository extends Repository<VirtualWarehouse> {
  private readonly logger = new Logger(VirtualWarehouseRepository.name);

  constructor(private readonly dataSource: DataSource) { // Using user's 'readonly' for dataSource
    super(VirtualWarehouse, dataSource.createEntityManager());
  }

  // --- Base Query Builders ---

  /**
   * Tạo query builder cơ bản cho VirtualWarehouse (User context version)
   * @returns Query builder
   */
  private createBaseQuery_user(): SelectQueryBuilder<VirtualWarehouse> {
    return this.createQueryBuilder('virtual_warehouse'); // User's alias
  }

  /**
   * Tạo query builder cơ bản cho virtual warehouse (Admin context version)
   * @returns SelectQueryBuilder<VirtualWarehouse>
   */
  private createBaseQuery_admin(): SelectQueryBuilder<VirtualWarehouse> {
    this.logger.log('(Admin) Tạo query builder cơ bản cho virtual warehouse');
    return this.createQueryBuilder('virtualWarehouse')
      .select('virtualWarehouse.warehouseId', 'virtualWarehouse_warehouseId')
      .addSelect('virtualWarehouse.associatedSystem', 'virtualWarehouse_associatedSystem')
      .addSelect('virtualWarehouse.purpose', 'virtualWarehouse_purpose'); // Chọn tất cả các trường cần thiết với alias rõ ràng
  }

  // --- Methods with same name, differentiated by suffix ---

  /**
   * Tìm kho ảo theo ID kho (User context version)
   * @param warehouseId ID của kho
   * @returns Thông tin kho ảo hoặc null nếu không tìm thấy
   */
  async findByWarehouseId_user(
    warehouseId: number,
  ): Promise<VirtualWarehouse | null> {
    try {
      return await this.createBaseQuery_user() // User base query
        .where('virtual_warehouse.warehouseId = :warehouseId', { warehouseId }) // User alias
        .getOne();
    } catch (error) {
      this.logger.error(
        `(User) Lỗi khi tìm kho ảo theo ID ${warehouseId}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Lỗi khi tìm kho ảo theo ID ${warehouseId}: ${error.message}`,
      );
    }
  }

  /**
   * Tìm kiếm kho ảo theo ID kho (Admin context version)
   * @param warehouseId ID của kho
   * @returns Kho ảo
   */
  async findByWarehouseId_admin(warehouseId: number): Promise<VirtualWarehouse | null> {
    this.logger.log(`(Admin) Tìm kiếm kho ảo với warehouseId: ${warehouseId}`);
    const qb = this.createBaseQuery_admin() // Admin base query
      .where('virtualWarehouse.warehouseId = :warehouseId', { warehouseId }); // Admin alias
    return qb.getOne();
  }


  // --- User specific or unique methods ---

  /**
   * Tạo kho ảo mới (User context method)
   * @param virtualWarehouse Thông tin kho ảo
   * @returns Kho ảo đã tạo
   */
  async createVirtualWarehouse(
    virtualWarehouse: VirtualWarehouse,
  ): Promise<VirtualWarehouse> {
    try {
      return await this.save(virtualWarehouse);
    } catch (error) {
      this.logger.error(`(User) Lỗi khi tạo kho ảo: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo kho ảo: ${error.message}`);
    }
  }

  /**
   * Tìm kho ảo theo ID kho và kết hợp với thông tin kho chung và trường tùy chỉnh (User context method)
   * @param warehouseId ID của kho
   * @returns Thông tin đầy đủ của kho ảo hoặc null nếu không tìm thấy
   */
  async findByWarehouseIdWithDetails(warehouseId: number): Promise<any | null> {
    try {
      // User's original raw query logic
      const queryBuilder = this.dataSource
        .createQueryBuilder()
        .select('vw.warehouse_id', 'warehouseId') // Uses DB column names for raw query
        .addSelect('vw.associated_system', 'associatedSystem')
        .addSelect('vw.purpose', 'purpose')
        .addSelect('w.name', 'name')
        .addSelect('w.description', 'description')
        .addSelect('w.type', 'type')
        .from(VirtualWarehouse, 'vw') // Entity can be used in 'from' for QueryBuilder
        .innerJoin(Warehouse, 'w', 'vw.warehouse_id = w.warehouse_id') // Uses DB column names for join condition
        .where('vw.warehouse_id = :warehouseId', { warehouseId });

      const virtualWarehouseWithDetails = await queryBuilder.getRawOne();

      if (!virtualWarehouseWithDetails) {
        return null;
      }

      const customFields = await this.dataSource
        .createQueryBuilder()
        .select('wcf.warehouse_id', 'warehouseId') // Uses DB column names
        .addSelect('wcf.field_id', 'fieldId')
        .addSelect('wcf.value', 'value')
        .from(WarehouseCustomField, 'wcf')
        .where('wcf.warehouse_id = :warehouseId', { warehouseId })
        .getRawMany();

      return {
        ...virtualWarehouseWithDetails,
        customFields: customFields || [],
      };
    } catch (error) {
      this.logger.error(
        `(User) Lỗi khi tìm kho ảo với thông tin chi tiết theo ID ${warehouseId}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Lỗi khi tìm kho ảo với thông tin chi tiết theo ID ${warehouseId}: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật kho ảo (User context method)
   * @param warehouseId ID của kho
   * @param updateData Dữ liệu cập nhật
   * @returns Kho ảo đã cập nhật
   */
  async updateVirtualWarehouse(
    warehouseId: number,
    updateData: Partial<VirtualWarehouse>,
  ): Promise<VirtualWarehouse> {
    try {
      await this.update({ warehouseId }, updateData); // TypeORM update uses entity property names
      const updatedWarehouse = await this.findByWarehouseId_user(warehouseId); // Call user-suffixed version
      if (!updatedWarehouse) {
        throw new Error(
          `Không tìm thấy kho ảo với ID ${warehouseId} sau khi cập nhật`,
        );
      }
      return updatedWarehouse;
    } catch (error) {
      this.logger.error(
        `(User) Lỗi khi cập nhật kho ảo với ID ${warehouseId}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Lỗi khi cập nhật kho ảo với ID ${warehouseId}: ${error.message}`,
      );
    }
  }

  /**
   * Xóa kho ảo (User context method)
   * @param warehouseId ID của kho
   * @returns Kết quả xóa
   */
  async deleteVirtualWarehouse(warehouseId: number): Promise<boolean> {
    try {
      const result = await this.delete({ warehouseId }); // TypeORM delete uses entity property names
      return (
        result.affected !== undefined &&
        result.affected !== null &&
        result.affected > 0
      );
    } catch (error) {
      this.logger.error(
        `(User) Lỗi khi xóa kho ảo với ID ${warehouseId}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Lỗi khi xóa kho ảo với ID ${warehouseId}: ${error.message}`,
      );
    }
  }

  /**
   * Tìm kiếm kho ảo với các điều kiện lọc và phân trang (User context method)
   * @param queryDto Tham số truy vấn (User DTO)
   * @returns Danh sách kho ảo với phân trang
   */
  async findAll(queryDto: QueryVirtualWarehouseDtoUser): Promise<PaginatedResult<VirtualWarehouse>> {
    try {
      const {
        page = 1,
        limit = 10,
        offset = (page - 1) * limit,
        search,
        sortBy = 'warehouseId', // Entity property name
        sortDirection = 'ASC',
      } = queryDto;

      // User's original query logic
      const queryBuilder = this.createBaseQuery_user() // Uses 'virtual_warehouse' alias
        // User file used 'warehouse' as table name and 'w' as alias.
        // For TypeORM, better to use entity name or alias if joining entity.
        // Assuming 'Warehouse' entity is related and TypeORM can resolve it.
        .innerJoin(Warehouse, 'w', 'virtual_warehouse.warehouseId = w.warehouseId'); // Uses entity property names for join for TypeORM

      if (search) {
        // Uses entity property names for where clauses with TypeORM QB, TypeORM handles DB name mapping
        queryBuilder.andWhere(
          '(virtual_warehouse.associatedSystem ILIKE :search OR virtual_warehouse.purpose ILIKE :search OR w.name ILIKE :search OR w.description ILIKE :search)',
          { search: `%${search}%` }
        );
      }

      const total = await queryBuilder.getCount();

      let orderField = `virtual_warehouse.${sortBy}`; // Default to user's alias
      if (sortBy === 'name' || sortBy === 'description') {
        orderField = `w.${sortBy}`; // If sorting by joined table field
      }

      queryBuilder
        .orderBy(orderField, sortDirection as 'ASC' | 'DESC')
        .skip(offset)
        .take(limit);

      const items = await queryBuilder.getMany();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `(User) Lỗi khi tìm kiếm kho ảo: ${error.message}`,
        error.stack,
      );
      throw new Error(`Lỗi khi tìm kiếm kho ảo: ${error.message}`);
    }
  }

  // --- Admin specific or unique methods ---

  /**
   * Tìm kiếm kho ảo theo ID kho với thông tin kho (Admin context method)
   * @param warehouseId ID của kho
   * @returns Kho ảo với thông tin kho
   */
  async findByWarehouseIdWithWarehouse(warehouseId: number): Promise<any | null> {
    this.logger.log(`(Admin) Tìm kiếm kho ảo với thông tin kho, warehouseId: ${warehouseId}`);
    try {
      // Admin's original raw query logic
      const qb = this.dataSource
        .createQueryBuilder()
        .select('vw.warehouse_id', 'warehouse_id') // Using raw DB column names
        .addSelect('vw.associated_system', 'associated_system')
        .addSelect('vw.purpose', 'purpose')
        .addSelect('w.warehouse_id', 'w_warehouse_id')
        .addSelect('w.name', 'w_name')
        .addSelect('w.description', 'w_description')
        .addSelect('w.type', 'w_type')
        .from('virtual_warehouse', 'vw') // Raw table names
        .leftJoin('warehouse', 'w', 'w.warehouse_id = vw.warehouse_id') // Raw table names & DB columns
        .where('vw.warehouse_id = :warehouseId', { warehouseId });

      const sqlQuery = qb.getSql();
      const params = qb.getParameters();
      this.logger.log(`(Admin) Câu SQL để lấy chi tiết kho ảo: ${sqlQuery}`);
      this.logger.log(`(Admin) Tham số truy vấn: ${JSON.stringify(params)}`);

      const result = await qb.getRawMany(); // Should be getRawOne() if expecting one or null
      this.logger.log(`(Admin) Kết quả truy vấn: ${result.length} bản ghi`);

      if (result.length > 0) {
        const item = result[0];
        this.logger.log(`(Admin) Đã tìm thấy kho ảo với warehouseId: ${warehouseId}`);
        this.logger.log(`(Admin) Chi tiết dữ liệu thô: ${JSON.stringify(item)}`);
        this.logger.log(`(Admin) Cấu trúc của item: ${JSON.stringify(Object.keys(item))}`);

        // Tìm tên của các cột warehouseId
        const warehouseIdKeys = Object.keys(item).filter(key => key.toLowerCase().includes('warehouse_id') && !key.toLowerCase().includes('w_'));
        const warehouseWarehouseIdKeys = Object.keys(item).filter(key => key.toLowerCase().includes('w_warehouse_id'));

        this.logger.log(`(Admin) Các key chứa warehouseId: ${JSON.stringify(warehouseIdKeys)}`);
        this.logger.log(`(Admin) Các key chứa w_warehouse_id: ${JSON.stringify(warehouseWarehouseIdKeys)}`);

        const warehouseIdKey = warehouseIdKeys[0] || 'warehouse_id';
        const warehouseWarehouseIdKey = warehouseWarehouseIdKeys[0] || 'w_warehouse_id';

        const formattedResult = {
          warehouseId: item[warehouseIdKey] ? Number(item[warehouseIdKey]) : null,
          associatedSystem: item.associated_system,
          purpose: item.purpose,
          warehouse: {
            warehouseId: item[warehouseWarehouseIdKey] ? Number(item[warehouseWarehouseIdKey]) : null,
            name: item.w_name,
            description: item.w_description,
            type: item.w_type
          }
        };
        this.logger.log(`(Admin) Dữ liệu đã chuyển đổi: ${JSON.stringify(formattedResult)}`);
        return formattedResult;
      } else {
        this.logger.log(`(Admin) Không tìm thấy kho ảo với warehouseId: ${warehouseId}`);
        return null;
      }
    } catch (error) {
      this.logger.error(`(Admin) Lỗi khi thực hiện truy vấn lấy chi tiết kho ảo: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tìm kiếm danh sách kho ảo với phân trang (Admin context method)
   * @param queryDto DTO truy vấn (Admin DTO)
   * @returns Danh sách kho ảo với phân trang
   */
  async findAllWithPagination(
    queryDto: QueryVirtualWarehouseDtoAdmin
  ): Promise<[any[], number]> { // Return type [any[], number] as in original admin
    this.logger.log('(Admin) Tìm kiếm danh sách kho ảo với phân trang');
    const { page, limit, associatedSystem, sortBy, sortDirection } = queryDto;
    this.logger.log(`(Admin) Tham số truy vấn: page=${page}, limit=${limit}, associatedSystem=${associatedSystem}, sortBy=${sortBy}, sortDirection=${sortDirection}`);

    const skip = (page - 1) * limit;

    const qb = this.createBaseQuery_admin(); // Uses 'virtualWarehouse' alias
    this.logger.log(`(Admin) Thêm join với bảng warehouse`);
    // Sử dụng join trực tiếp với bảng warehouse thay vì dựa vào relation
    qb.leftJoin(
      'warehouse', // Tên bảng warehouse
      'warehouse', // Alias cho bảng warehouse
      'warehouse.warehouse_id = virtualWarehouse.warehouseId' // Điều kiện join
    )
    .addSelect('warehouse.warehouse_id', 'warehouse_warehouse_id')
    .addSelect('warehouse.name', 'warehouse_name')
    .addSelect('warehouse.description', 'warehouse_description')
    .addSelect('warehouse.type', 'warehouse_type');

    if (associatedSystem) {
      // Uses entity property names for where clauses
      qb.andWhere('virtualWarehouse.associatedSystem ILIKE :associatedSystem', { associatedSystem: `%${associatedSystem}%` });
    }

    if (sortBy) {
      if (sortBy.startsWith('warehouse.')) { // If sorting by property of joined 'warehouse'
        // e.g. warehouse.name -> sortBy will be 'warehouse.name'
        qb.orderBy(sortBy, sortDirection);
      } else if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
        // Assuming VirtualWarehouse entity doesn't have these, fallback to warehouseId
        qb.orderBy('virtualWarehouse.warehouseId', sortDirection);
      } else {
        // Sorting by VirtualWarehouse property
        qb.orderBy(`virtualWarehouse.${sortBy}`, sortDirection);
      }
    } else {
      qb.orderBy('virtualWarehouse.warehouseId', 'DESC');
    }

    qb.offset(skip).limit(limit);

    const sqlQuery = qb.getQuery();
    const params = qb.getParameters();
    this.logger.log(`(Admin) Câu SQL để lấy dữ liệu: ${sqlQuery}`);
    this.logger.log(`(Admin) Tham số truy vấn: ${JSON.stringify(params)}`);

    // Count query
    const countQb = this.createBaseQuery_admin();
    if (associatedSystem) { // Apply same filters to count query
      countQb.andWhere('virtualWarehouse.associatedSystem ILIKE :associatedSystem', { associatedSystem: `%${associatedSystem}%` });
    }
    // If joining for filtering, the count query might also need the join:
    if (sortBy && sortBy.startsWith('warehouse.')) { // Example if join is needed for count filter
      countQb.leftJoin(
        'warehouse',
        'warehouse',
        'warehouse.warehouse_id = virtualWarehouse.warehouseId'
      );
    }

    const countSqlQuery = countQb.getQuery();
    const countParams = countQb.getParameters();
    this.logger.log(`(Admin) Câu SQL để đếm tổng số bản ghi: ${countSqlQuery}`);
    this.logger.log(`(Admin) Tham số truy vấn count: ${JSON.stringify(countParams)}`);

    try {
      const total = await countQb.getCount();
      const rawItems = await qb.getRawMany(); // getRawMany if mapping manually
      // Or getMany() if relying on TypeORM's entity hydration with leftJoinAndSelect

      this.logger.log(`(Admin) Đã tìm thấy ${rawItems.length}/${total} kho ảo`);

      // Admin's original mapping from raw items
      // In ra một item mẫu để xem cấu trúc chính xác
      if (rawItems.length > 0) {
        this.logger.log(`(Admin) Mẫu item đầu tiên: ${JSON.stringify(rawItems[0])}`);
      }

      const items = rawItems.map(item => {
        // Xem cấu trúc của item để biết chính xác tên của các trường
        this.logger.log(`(Admin) Cấu trúc của item: ${JSON.stringify(Object.keys(item))}`);

        // Tìm tên của các cột warehouseId
        const warehouseIdKeys = Object.keys(item).filter(key => key.toLowerCase().includes('warehouseid') || key.toLowerCase().includes('warehouse_id'));
        this.logger.log(`(Admin) Các key chứa warehouseId: ${JSON.stringify(warehouseIdKeys)}`);

        // Tìm tên của các cột warehouse_warehouse_id
        const warehouseWarehouseIdKeys = Object.keys(item).filter(key => key.toLowerCase().includes('warehouse') && key.toLowerCase().includes('id') && key !== warehouseIdKeys[0]);
        this.logger.log(`(Admin) Các key chứa warehouse_warehouse_id: ${JSON.stringify(warehouseWarehouseIdKeys)}`);

        // Đảm bảo trả về đầy đủ các trường cần thiết
        const warehouseIdKey = warehouseIdKeys[0] || 'virtualWarehouse_warehouseId';
        const warehouseWarehouseIdKey = warehouseWarehouseIdKeys[0] || 'warehouse_warehouse_id';

        // Tìm các key khác
        const associatedSystemKey = Object.keys(item).find(key => key.toLowerCase().includes('associated')) || 'virtualWarehouse_associatedSystem';
        const purposeKey = Object.keys(item).find(key => key.toLowerCase().includes('purpose')) || 'virtualWarehouse_purpose';
        const nameKey = Object.keys(item).find(key => key.toLowerCase().includes('name')) || 'warehouse_name';
        const descriptionKey = Object.keys(item).find(key => key.toLowerCase().includes('description')) || 'warehouse_description';
        const typeKey = Object.keys(item).find(key => key.toLowerCase().includes('type')) || 'warehouse_type';

        this.logger.log(`(Admin) Sử dụng các key: warehouseId=${warehouseIdKey}, warehouseWarehouseId=${warehouseWarehouseIdKey}, associatedSystem=${associatedSystemKey}, purpose=${purposeKey}, name=${nameKey}, description=${descriptionKey}, type=${typeKey}`);

        const result = {
          warehouseId: item[warehouseIdKey] ? Number(item[warehouseIdKey]) : null,
          associatedSystem: item[associatedSystemKey] || null,
          purpose: item[purposeKey] || null,
          warehouse: {
            warehouseId: item[warehouseWarehouseIdKey] ? Number(item[warehouseWarehouseIdKey]) : null,
            name: item[nameKey] || null,
            description: item[descriptionKey] || null,
            type: item[typeKey] || null
          }
        };

        this.logger.log(`(Admin) Dữ liệu đã chuyển đổi: ${JSON.stringify(result)}`);
        return result;
      });

      return [items, total];
    } catch (error) {
      this.logger.error(`(Admin) Lỗi khi thực hiện truy vấn hoặc đếm: ${error.message}`);
      throw error;
    }
  }
}