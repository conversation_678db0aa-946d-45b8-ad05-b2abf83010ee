import { apiClient } from '@/shared/api/axios';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  ModelBase,
  CreateModelBaseDto,
  ModelBaseQueryParams,
} from '../types/model-base.types';

const API_BASE_URL = '/user/model-training/model-bases';

/**
 * L<PERSON>y danh sách model base
 * @param params Tham số truy vấn
 * @returns Danh sách model base đã phân trang
 */
export const getModelBases = async (
  params: ModelBaseQueryParams = {}
): Promise<PaginatedResult<ModelBase>> => {
  const response = await apiClient.get<PaginatedResult<ModelBase>>(API_BASE_URL, { params });
  return response.result;
};

/**
 * Lấy thông tin model base theo ID
 * @param id ID của model base
 * @returns Thông tin chi tiết của model base
 */
export const getModelBaseById = async (id: string): Promise<ModelBase> => {
  const response = await apiClient.get<ModelBase>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * Tạo model base mới
 * @param data Thông tin model base cần tạo
 * @returns Thông tin model base đã tạo
 */
export const createModelBase = async (data: CreateModelBaseDto): Promise<ModelBase> => {
  const response = await apiClient.post<ModelBase>(API_BASE_URL, data);
  return response.result;
};

/**
 * Cập nhật thông tin model base
 * @param id ID của model base
 * @param data Thông tin cần cập nhật
 * @returns Thông tin model base đã cập nhật
 */
export const updateModelBase = async (id: string, data: Partial<ModelBase>): Promise<ModelBase> => {
  const response = await apiClient.put<ModelBase>(`${API_BASE_URL}/${id}`, data);
  return response.result;
};

/**
 * Xóa model base
 * @param id ID của model base cần xóa
 * @returns Kết quả xóa
 */
export const deleteModelBase = async (id: string): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(`${API_BASE_URL}/${id}`);
  return response.result;
};
