import { Injectable, Logger } from '@nestjs/common';
import { UserConvertRepository, UserConvertCustomerRepository } from '@modules/business/repositories';
import { QueryUserConvertDto, UserConvertResponseDto, UserConvertDetailResponseDto, UserConvertCustomerResponseDto } from '../dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions';
import { BUSINESS_ADMIN_ERROR_CODES } from '@modules/business/admin/exceptions';
import { UserConvert, UserConvertCustomer } from '@modules/business/entities';
import { ValidationHelper } from '../helpers/validation.helper';

/**
 * Service xử lý nghiệp vụ liên quan đến bản ghi chuyển đổi khách hàng cho admin
 */
@Injectable()
export class UserConvertAdminService {
  private readonly logger = new Logger(UserConvertAdminService.name);

  constructor(
    private readonly userConvertRepository: UserConvertRepository,
    private readonly userConvertCustomerRepository: UserConvertCustomerRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Lấy danh sách bản ghi chuyển đổi khách hàng với phân trang và lọc
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách bản ghi chuyển đổi khách hàng phân trang
   */
  async getUserConverts(
    employeeId: number,
    queryDto: QueryUserConvertDto,
  ): Promise<PaginatedResult<UserConvertResponseDto>> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy danh sách bản ghi chuyển đổi khách hàng với query: ${JSON.stringify(queryDto)}`,
      `method: ${this.getUserConverts.name}`
    );

    try {
      // Gọi repository để lấy dữ liệu
      const result = await this.userConvertRepository.findUserConverts(queryDto);

      // Chuyển đổi từ entity sang DTO
      const convertDtos = result.items.map(convert => this.mapToUserConvertResponseDto(convert));

      this.logger.log(`Đã tìm thấy ${result.items.length} bản ghi chuyển đổi khách hàng`);

      return {
        items: convertDtos,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách bản ghi chuyển đổi khách hàng: ${error.message}`,
        error.stack,
        `method: ${this.getUserConverts.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_FETCH_ERROR,
        'Lỗi khi lấy danh sách bản ghi chuyển đổi khách hàng',
      );
    }
  }

  /**
   * Lấy chi tiết bản ghi chuyển đổi khách hàng theo ID
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param convertId ID của bản ghi chuyển đổi khách hàng
   * @returns Chi tiết bản ghi chuyển đổi khách hàng
   */
  async getUserConvertById(
    employeeId: number,
    convertId: number,
  ): Promise<UserConvertDetailResponseDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy chi tiết bản ghi chuyển đổi khách hàng với ID: ${convertId}`,
      `method: ${this.getUserConvertById.name}`
    );

    try {
      // Lấy thông tin bản ghi chuyển đổi khách hàng kèm thông tin khách hàng
      const convert = await this.userConvertRepository.findUserConvertByIdWithCustomer(convertId);

      // Kiểm tra bản ghi có tồn tại không
      this.validationHelper.validateUserConvertExists(convert);

      this.logger.log(`Đã tìm thấy bản ghi chuyển đổi khách hàng với ID: ${convertId}`);

      // Chuyển đổi từ entity sang DTO
      // Sử dụng non-null assertion vì đã kiểm tra bằng validationHelper
      return this.mapToUserConvertDetailResponseDto(convert!);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy chi tiết bản ghi chuyển đổi khách hàng: ${error.message}`,
        error.stack,
        `method: ${this.getUserConvertById.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_FETCH_ERROR,
        'Lỗi khi lấy chi tiết bản ghi chuyển đổi khách hàng',
      );
    }
  }

  /**
   * Chuyển đổi từ entity UserConvert sang DTO UserConvertResponseDto
   * @param convert Entity UserConvert
   * @returns DTO UserConvertResponseDto
   */
  private mapToUserConvertResponseDto(convert: UserConvert): UserConvertResponseDto {
    return {
      id: typeof convert.id === 'string' ? parseInt(convert.id) : convert.id,
      convertCustomerId: typeof convert.convertCustomerId === 'string' ? parseInt(convert.convertCustomerId) : convert.convertCustomerId,
      userId: typeof convert.userId === 'string' ? parseInt(convert.userId) : convert.userId,
      conversionType: convert.conversionType,
      source: convert.source,
      notes: convert.notes,
      content: convert.content,
      createdAt: typeof convert.createdAt === 'string' ? parseInt(convert.createdAt) : convert.createdAt,
      updatedAt: typeof convert.updatedAt === 'string' ? parseInt(convert.updatedAt) : convert.updatedAt,
    };
  }

  /**
   * Chuyển đổi từ entity UserConvert sang DTO UserConvertDetailResponseDto
   * bao gồm thông tin chi tiết khách hàng
   * @param data Dữ liệu bản ghi chuyển đổi với thông tin khách hàng
   * @returns DTO UserConvertDetailResponseDto
   */
  private mapToUserConvertDetailResponseDto(data: any): UserConvertDetailResponseDto {
    this.logger.log(`Chuyển đổi dữ liệu sang DTO chi tiết`, `method: ${this.mapToUserConvertDetailResponseDto.name}`);

    // Lấy thông tin cơ bản của bản ghi chuyển đổi
    const baseDto = this.mapToUserConvertResponseDto(data);

    // Tạo DTO chi tiết
    const detailDto: UserConvertDetailResponseDto = {
      ...baseDto
    };

    // Nếu có thông tin khách hàng, thêm vào DTO
    if (data.customer) {
      this.logger.log(`Đã tìm thấy thông tin khách hàng, chuyển đổi sang DTO`, `method: ${this.mapToUserConvertDetailResponseDto.name}`);

      // Chuyển đổi dữ liệu raw từ database sang DTO
      this.logger.log(`Dữ liệu khách hàng: ${JSON.stringify(data.customer)}`, `method: ${this.mapToUserConvertDetailResponseDto.name}`);

      detailDto.customer = {
        id: parseInt(data.customer.id),
        avatar: data.customer.avatar,
        name: data.customer.name,
        email: data.customer.email,
        phone: data.customer.phone,
        platform: data.customer.platform,
        timezone: data.customer.timezone,
        createdAt: parseInt(data.customer.created_at),
        updatedAt: parseInt(data.customer.updated_at),
        userId: data.customer.user_id ? parseInt(data.customer.user_id) : null,
        agentId: data.customer.agent_id,
        metadata: data.customer.metadata,
      };
    } else {
      this.logger.log(`Không tìm thấy thông tin khách hàng`, `method: ${this.mapToUserConvertDetailResponseDto.name}`);
    }

    return detailDto;
  }

  /**
   * Chuyển đổi từ entity UserConvertCustomer sang DTO UserConvertCustomerResponseDto
   * @param customer Entity UserConvertCustomer
   * @returns DTO UserConvertCustomerResponseDto
   */
  private mapToUserConvertCustomerResponseDto(customer: UserConvertCustomer): UserConvertCustomerResponseDto {
    return {
      id: Number(customer.id), // Đảm bảo id được chuyển đổi sang kiểu number
      avatar: customer.avatar,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      platform: customer.platform,
      timezone: customer.timezone,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      userId: customer.userId,
      agentId: customer.agentId,
      metadata: customer.metadata ? Object.entries(customer.metadata).map(([fieldName, fieldValue]) => ({
        fieldName,
        fieldValue: fieldValue as string | number | boolean | string[] | number[]
      })) : [],
    };
  }
}
