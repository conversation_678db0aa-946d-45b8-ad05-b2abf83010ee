{"modelTraining": {"provider": {"title": "Provider Integration", "list": "Provider List", "add": "Add Provider", "edit": "Edit Provider", "delete": "Delete Provider", "form": {"name": "Provider Name", "type": "Provider Type", "apiKey": "API Key", "namePlaceholder": "Enter provider name", "typePlaceholder": "Select provider type", "apiKeyPlaceholder": "Enter API Key"}, "messages": {"createSuccess": "Provider created successfully", "updateSuccess": "Provider updated successfully", "deleteSuccess": "Provider deleted successfully", "createError": "Error creating provider", "updateError": "Error updating provider", "deleteError": "Error deleting provider", "confirmDelete": "Are you sure you want to delete this provider? This action cannot be undone."}, "empty": {"title": "No providers yet", "description": "You haven't added any providers yet. Add a provider to get started."}}, "modelBase": {"title": "Create Model Base", "list": "Model Base List", "add": "Add Model Base", "edit": "Edit Model Base", "delete": "Delete Model Base", "form": {"name": "Model Base Name", "providerId": "Provider", "pricing": "Pricing", "input": "Input", "output": "Output", "train": "Train", "base": "Base", "fineTuning": "Fine-tuning", "config": "Configuration", "topP": "Top P", "topK": "Top K", "tool": "Tool", "temperature": "Temperature", "text": "Text", "image": "Image", "audio": "Audio", "video": "Video", "toolCall": "Tool call", "namePlaceholder": "Enter Model Base name", "providerPlaceholder": "Select provider"}, "messages": {"createSuccess": "Model Base created successfully", "updateSuccess": "Model Base updated successfully", "deleteSuccess": "Model Base deleted successfully", "createError": "Error creating Model Base", "updateError": "Error updating Model Base", "deleteError": "Error deleting Model Base", "confirmDelete": "Are you sure you want to delete this Model Base? This action cannot be undone."}, "empty": {"title": "No Model Bases yet", "description": "You haven't added any Model Bases yet. Add a Model Base to get started."}}, "dataset": {"title": "Dataset Fine-tuning Model", "list": "Dataset List", "add": "Add Dataset", "edit": "Edit Dataset", "delete": "Delete Dataset", "form": {"name": "Dataset Name", "description": "Description", "trainData": "Train Data", "validationData": "Validation Data", "import": "Import", "systemRole": "System Role", "userRole": "User Role", "assistantRole": "Assistant Role", "namePlaceholder": "Enter dataset name", "descriptionPlaceholder": "Enter dataset description", "messagePlaceholder": "Enter message content...", "send": "Send", "selectRole": "Select role"}, "messages": {"createSuccess": "Dataset created successfully", "updateSuccess": "Dataset updated successfully", "deleteSuccess": "Dataset deleted successfully", "createError": "Error creating dataset", "updateError": "Error updating dataset", "deleteError": "Error deleting dataset", "confirmDelete": "Are you sure you want to delete this dataset? This action cannot be undone."}, "empty": {"title": "No datasets yet", "description": "You haven't added any datasets yet. Add a dataset to get started."}}}}