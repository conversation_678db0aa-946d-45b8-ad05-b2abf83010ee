import * as z from 'zod';
import { AccessType, ToolStatus, ToolSortBy } from '../types/common.types';

/**
 * Schema cho tham số truy vấn danh sách tool của người dùng
 */
export const userToolQuerySchema = z.object({
  page: z.coerce.number().optional().default(1),
  limit: z.coerce.number().optional().default(10),
  search: z.string().optional(),
  sortBy: z.nativeEnum(ToolSortBy).optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
  status: z.nativeEnum(ToolStatus).optional(),
  access: z.nativeEnum(AccessType).optional(),
});

/**
 * Schema cho tham số tạo mới tool
 */
export const createUserToolSchema = z.object({
  name: z.string().min(1, 'Tên tool là bắt buộc'),
  description: z.string().optional(),
  access: z.nativeEnum(AccessType).optional().default(AccessType.PRIVATE),
  adminToolId: z.string().optional(),
  adminVersionId: z.string().optional(),
});

/**
 * Schema cho tham số cập nhật tool
 */
export const updateUserToolSchema = z.object({
  name: z.string().min(1, 'Tên tool là bắt buộc').optional(),
  description: z.string().optional(),
  access: z.nativeEnum(AccessType).optional(),
});

/**
 * Schema cho tham số chỉnh sửa phiên bản tool
 */
export const editUserToolVersionSchema = z.object({
  toolName: z.string().min(1, 'Tên hiển thị tool là bắt buộc'),
  toolDescription: z.string().optional(),
  parameters: z.record(z.unknown()),
  changeDescription: z.string().optional(),
  status: z.nativeEnum(ToolStatus).optional(),
});

/**
 * Schema cho tham số sao chép tool từ admin
 */
export const cloneAdminToolSchema = z.object({
  adminToolId: z.string().min(1, 'ID tool admin là bắt buộc'),
  adminVersionId: z.string().optional(),
});

/**
 * Schema cho tham số cập nhật tool từ phiên bản admin
 */
export const updateFromAdminSchema = z.object({
  userToolId: z.string().min(1, 'ID tool người dùng là bắt buộc'),
  adminVersionId: z.string().min(1, 'ID phiên bản admin là bắt buộc'),
});

/**
 * Schema cho tham số khôi phục về phiên bản admin
 */
export const rollbackToAdminSchema = z.object({
  userToolId: z.string().min(1, 'ID tool người dùng là bắt buộc'),
  adminVersionId: z.string().min(1, 'ID phiên bản admin là bắt buộc'),
});
