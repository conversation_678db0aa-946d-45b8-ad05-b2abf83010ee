import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Card, Typography, Button, Modal } from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ToolGrid, ToolForm, CreateToolForm } from '../components';
import {
  useUserTools,
  useDeleteTool,
  useCloneAllPublicTools,
  useUserToolDetail,
  useUpdateFromAdmin,
  useRollbackToAdminVersion,
} from '../hooks/useTool';
import useCreateTool from '../hooks/useCreateTool';
import { ToolStatus, ToolSortBy } from '../types/common.types';
import { ToolListItem, ToolDetail, CreateToolParams } from '../types/tool.types';

/**
 * <PERSON>rang hiển thị danh sách tools của người dùng
 */
const ToolsPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // State cho tìm kiếm và lọc
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<ToolStatus | 'all'>('all');
  const [filterHasUpdate, setFilterHasUpdate] = useState<boolean | undefined>(undefined);

  // State cho phân trang
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(12);

  // State cho xóa tool
  const [toolToDelete, setToolToDelete] = useState<ToolListItem | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // State cho xem chi tiết tool
  const [toolToView, setToolToView] = useState<ToolListItem | null>(null);

  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isDetailFormVisible,
    showForm: showDetailForm,
    hideForm: hideDetailForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form tạo mới
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo(
    () => ({
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      status: filterStatus !== 'all' ? filterStatus : undefined,
      hasUpdate: filterHasUpdate,
      sortBy: ToolSortBy.CREATED_AT,
      sortDirection: 'DESC' as const,
    }),
    [currentPage, itemsPerPage, searchTerm, filterStatus, filterHasUpdate]
  );

  // Hooks để gọi API
  const { data: toolsData, isLoading } = useUserTools(queryParams);
  const { data: toolDetail, isLoading: isLoadingDetail } = useUserToolDetail(toolToView?.id);
  const deleteToolMutation = useDeleteTool();
  const cloneAllPublicToolsMutation = useCloneAllPublicTools();
  const updateFromAdminMutation = useUpdateFromAdmin();
  const rollbackToAdminMutation = useRollbackToAdminVersion();
  const createToolMutation = useCreateTool();

  const deleteTool = deleteToolMutation.mutateAsync;
  const isDeleting = deleteToolMutation.isPending || false;
  const cloneAllPublicTools = cloneAllPublicToolsMutation.mutateAsync;
  const isCloning = cloneAllPublicToolsMutation.isPending || false;
  const updateFromAdmin = updateFromAdminMutation.mutateAsync;
  const rollbackToAdmin = rollbackToAdminMutation.mutateAsync;
  const createTool = createToolMutation.mutateAsync;
  const isCreating = createToolMutation.isPending || false;

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý lọc theo trạng thái
  const handleFilterStatus = useCallback((status: string) => {
    setFilterStatus(status as ToolStatus | 'all');
    setCurrentPage(1); // Reset về trang 1 khi lọc
  }, []);

  // Các hàm xử lý lọc theo trạng thái cụ thể
  const handleFilterAll = useCallback(() => handleFilterStatus('all'), [handleFilterStatus]);
  const handleFilterDraft = useCallback(
    () => handleFilterStatus(ToolStatus.DRAFT),
    [handleFilterStatus]
  );
  const handleFilterApproved = useCallback(
    () => handleFilterStatus(ToolStatus.APPROVED),
    [handleFilterStatus]
  );
  const handleFilterDeprecated = useCallback(
    () => handleFilterStatus(ToolStatus.DEPRECATED),
    [handleFilterStatus]
  );

  // Xử lý lọc theo có cập nhật hay không
  const handleFilterHasUpdate = useCallback((hasUpdate: boolean | undefined) => {
    setFilterHasUpdate(hasUpdate);
    setCurrentPage(1); // Reset về trang 1 khi lọc
  }, []);

  // Các hàm xử lý lọc theo cập nhật cụ thể
  const handleFilterHasUpdateTrue = useCallback(
    () => handleFilterHasUpdate(true),
    [handleFilterHasUpdate]
  );
  const handleFilterHasUpdateFalse = useCallback(
    () => handleFilterHasUpdate(false),
    [handleFilterHasUpdate]
  );

  // Xử lý xem chi tiết tool
  const handleViewTool = useCallback(
    (tool: ToolListItem) => {
      setToolToView(tool);
      showDetailForm();
    },
    [showDetailForm]
  );

  // Xử lý đóng form chi tiết
  const handleCloseDetailForm = useCallback(() => {
    hideDetailForm();
    setToolToView(null);
  }, [hideDetailForm]);

  // Xử lý xóa tool
  const handleDeleteTool = useCallback((tool: ToolListItem) => {
    setToolToDelete(tool);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!toolToDelete) return;

    try {
      await deleteTool(toolToDelete.id);
      setShowDeleteConfirm(false);
      setToolToDelete(null);
    } catch (error) {
      console.error('Error deleting tool:', error);
    }
  }, [toolToDelete, deleteTool]);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setToolToDelete(null);
  }, []);

  // Xử lý sao chép tất cả tool công khai
  const handleCloneAllPublicTools = useCallback(async () => {
    try {
      await cloneAllPublicTools({});
    } catch (error) {
      console.error('Error cloning all public tools:', error);
    }
  }, [cloneAllPublicTools]);

  // Xử lý cập nhật từ admin
  const handleUpdateFromAdmin = useCallback(
    async (toolId: string, adminVersionId: string) => {
      try {
        await updateFromAdmin({
          userToolId: toolId,
          adminVersionId,
        });
      } catch (error) {
        console.error('Error updating from admin:', error);
      }
    },
    [updateFromAdmin]
  );

  // Xử lý khôi phục về phiên bản admin
  const handleRollbackToAdmin = useCallback(
    async (toolId: string, adminVersionId: string) => {
      try {
        await rollbackToAdmin({
          userToolId: toolId,
          adminVersionId,
        });
      } catch (error) {
        console.error('Error rolling back to admin version:', error);
      }
    },
    [rollbackToAdmin]
  );

  // Xử lý chỉnh sửa phiên bản
  const handleEditVersion = useCallback((toolId: string, versionId: string) => {
    // Chuyển hướng đến trang chỉnh sửa phiên bản
    window.location.href = `/tools/${toolId}/versions/${versionId}/edit`;
  }, []);

  // Xử lý xem danh sách phiên bản
  const handleViewVersions = useCallback(
    (toolId: string) => {
      navigate(`/tools/${toolId}/versions`);
    },
    [navigate]
  );

  // Xử lý tạo tool mới
  const handleSubmitCreateTool = useCallback(
    async (values: CreateToolParams) => {
      try {
        await createTool(values);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating tool:', error);
      }
    },
    [createTool, hideCreateForm]
  );

  return (
    <div className="space-y-4">
      {/* Menu bar */}
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={showCreateForm}
        items={[
          {
            id: 'all',
            label: t('common.all', 'Tất cả'),
            icon: 'list',
            onClick: handleFilterAll,
          },
          {
            id: 'draft',
            label: t('tools.status.draft', 'Bản nháp'),
            icon: 'file',
            onClick: handleFilterDraft,
          },
          {
            id: 'approved',
            label: t('tools.status.approved', 'Đã duyệt'),
            icon: 'check-circle',
            onClick: handleFilterApproved,
          },
          {
            id: 'deprecated',
            label: t('tools.status.deprecated', 'Không dùng'),
            icon: 'archive',
            onClick: handleFilterDeprecated,
          },
          {
            id: 'has-update',
            label: t('tools.hasUpdate', 'Có cập nhật'),
            icon: 'refresh-cw',
            onClick: handleFilterHasUpdateTrue,
          },
          {
            id: 'no-update',
            label: t('tools.noUpdate', 'Không có cập nhật'),
            icon: 'check',
            onClick: handleFilterHasUpdateFalse,
          },
        ]}
      />

      {/* SlideInForm cho form tạo mới */}
      <SlideInForm isVisible={isCreateFormVisible}>
        <CreateToolForm
          onSubmit={handleSubmitCreateTool}
          onCancel={hideCreateForm}
          isLoading={isCreating}
        />
      </SlideInForm>

      {/* SlideInForm cho form xem chi tiết */}
      <SlideInForm isVisible={isDetailFormVisible}>
        <ToolForm
          tool={toolDetail as ToolDetail}
          isLoading={isLoadingDetail}
          readOnly={true}
          onClose={handleCloseDetailForm}
          onUpdateFromAdmin={handleUpdateFromAdmin}
          onRollbackToAdmin={handleRollbackToAdmin}
          onEditVersion={handleEditVersion}
        />
      </SlideInForm>

      {/* Hiển thị danh sách tool */}
      {toolsData?.items && toolsData.items.length > 0 ? (
        <ToolGrid
          tools={toolsData.items}
          onViewTool={handleViewTool}
          onDeleteTool={handleDeleteTool}
          onViewVersions={handleViewVersions}
        />
      ) : (
        <Card className="p-8">
          <div className="flex flex-col items-center justify-center">
            <Typography variant="h6" className="text-gray-500 dark:text-gray-400">
              {isLoading
                ? t('common.loading', 'Đang tải...')
                : t('tools.noTools', 'Không tìm thấy công cụ nào')}
            </Typography>
            {!isLoading && (
              <Button
                variant="primary"
                className="mt-4"
                onClick={handleCloneAllPublicTools}
                disabled={isCloning}
              >
                {isCloning
                  ? t('tools.cloning', 'Đang sao chép...')
                  : t('tools.cloneAllPublic', 'Sao chép tất cả công cụ công khai')}
              </Button>
            )}
          </div>
        </Card>
      )}

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('tools.confirmDelete', 'Xác nhận xóa')}
        size="sm"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancelDelete}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="danger" onClick={handleConfirmDelete} disabled={isDeleting}>
              {isDeleting ? t('common.deleting', 'Đang xóa...') : t('common.delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography>
            {t('tools.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa công cụ này không?')}
          </Typography>
          {toolToDelete && (
            <Typography variant="body2" className="mt-2 font-semibold">
              {toolToDelete.name}
            </Typography>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default ToolsPage;
