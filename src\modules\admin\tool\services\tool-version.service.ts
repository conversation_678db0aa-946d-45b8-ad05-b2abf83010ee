import { apiClient } from '@/shared/api/axios';
import {
  ToolVersion,
  CreateToolVersionParams,
  UpdateToolVersionParams,
} from '../types/tool.types';

/**
 * Service để tương tác với API phiên bản tool của admin
 */
export class AdminToolVersionService {
  private baseUrl = `/admin/tools`;



  /**
   * L<PERSON>y thông tin chi tiết phiên bản của tool
   * @param toolId ID của tool
   * @param versionId ID của phiên bản
   * @returns Thông tin chi tiết phiên bản
   */
  async getVersionById(toolId: string, versionId: string): Promise<ToolVersion> {
    try {
      const response = await apiClient.get<ToolVersion>(
        `/admin/tools/${toolId}/versions/${versionId}`,
        { tokenType: 'admin' }
      );
      return response.result;
    } catch (error) {
      console.error(`Error fetching version ${versionId} for tool ${toolId}:`, error);
      throw error;
    }
  }

  /**
   * T<PERSON>o phiên bản mới cho tool
   * @param toolId ID của tool
   * @param data Dữ liệu tạo phiên bản
   * @returns ID của phiên bản đã tạo
   */
  async createVersion(toolId: string, data: CreateToolVersionParams): Promise<string> {
    try {
      const response = await apiClient.post<{ id: string }>(
        `${this.baseUrl}/${toolId}/versions`,
        data,
        { tokenType: 'admin' }
      );
      return response.result.id;
    } catch (error) {
      console.error(`Error creating version for tool ${toolId}:`, error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin phiên bản
   * @param toolId ID của tool
   * @param versionId ID của phiên bản
   * @param data Dữ liệu cập nhật
   * @returns Kết quả cập nhật
   */
  async updateVersion(
    toolId: string,
    versionId: string,
    data: UpdateToolVersionParams
  ): Promise<boolean> {
    try {
      const response = await apiClient.put<{ success: boolean }>(
        `${this.baseUrl}/${toolId}/versions/${versionId}`,
        data,
        { tokenType: 'admin' }
      );
      return response.result.success;
    } catch (error) {
      console.error(`Error updating version ${versionId} for tool ${toolId}:`, error);
      throw error;
    }
  }

  /**
   * Xóa phiên bản
   * @param toolId ID của tool
   * @param versionId ID của phiên bản
   * @returns Kết quả xóa
   */
  async deleteVersion(toolId: string, versionId: string): Promise<boolean> {
    try {
      const response = await apiClient.delete<{ success: boolean }>(
        `${this.baseUrl}/${toolId}/versions/${versionId}`,
        { tokenType: 'admin' }
      );
      return response.result.success;
    } catch (error) {
      console.error(`Error deleting version ${versionId} for tool ${toolId}:`, error);
      throw error;
    }
  }
}
