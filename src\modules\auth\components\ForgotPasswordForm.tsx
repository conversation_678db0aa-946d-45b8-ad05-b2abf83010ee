import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Form, FormItem, Input, Button, Icon, Alert } from '@/shared/components/common';
import { createForgotPasswordSchema, ForgotPasswordFormValues } from '../schemas/auth.schema';
import { useForgotPassword } from '../hooks/useAuthQuery';
import { useAuthCommon } from '@/shared/hooks/useAuthCommon';

interface ForgotPasswordFormProps {
  onSuccess?: () => void;
}

/**
 * Forgot password form component
 */
const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({ onSuccess }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { mutate: forgotPassword, isPending: isLoading } = useForgotPassword();
  const { saveVerifyInfo } = useAuthCommon();
  const [error, setError] = useState<string | null>(null);

  // Create forgot password schema with translations
  const forgotPasswordSchema = createForgotPasswordSchema(t);

  // Handle form submission
  const handleSubmit = async (values: unknown) => {
    // Clear previous errors
    setError(null);

    // Use type assertion with a specific type instead of 'any'
    const forgotPasswordValues = values as ForgotPasswordFormValues;

    // Call forgot password API
    forgotPassword(
      {
        email: forgotPasswordValues.emailOrPhone,
      },
      {
        onSuccess: response => {
          // Check if the response is successful
          if (response.code === 200) {
            // Save verification info to Redux store
            saveVerifyInfo({
              verifyToken: response.result.otpToken,
              expiresAt: response.result.expiresAt,
              info: [{ platform: 'EMAIL', value: response.result.maskedEmail }],
            });

            // Call success callback if provided
            if (onSuccess) {
              onSuccess();
            }

            // Navigate to verify forgot password page
            navigate('/auth/verify-forgot-password');
          } else {
            // Handle other response codes if needed
            setError(
              response.message ||
                t('auth.forgotPasswordError', 'Có lỗi xảy ra khi yêu cầu đặt lại mật khẩu')
            );
          }
        },
        onError: error => {
          console.error('Forgot password error:', error);

          // Extract error message from response if available
          let errorMessage = t('auth.forgotPasswordError');

          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as { response: { data?: { message?: string } } };
            if (axiosError.response.data?.message) {
              errorMessage = axiosError.response.data.message;
            }
          }

          setError(errorMessage);
        },
      }
    );
  };

  return (
    <Form schema={forgotPasswordSchema} onSubmit={handleSubmit} className="space-y-4">
      {error && <Alert type="error" message={error} className="mb-4" />}

      <FormItem name="emailOrPhone" label={t('auth.emailOrPhone')} required>
        <Input
          placeholder={t('auth.emailOrPhone')}
          leftIcon={<Icon name="user" size="sm" />}
          fullWidth
        />
      </FormItem>

      <div className="flex justify-end">
        <Button type="submit" variant="primary" isLoading={isLoading} fullWidth>
          {t('auth.resetPassword')}
        </Button>
      </div>
    </Form>
  );
};

export default ForgotPasswordForm;
