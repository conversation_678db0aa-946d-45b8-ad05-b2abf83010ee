import React, { useState, useRef } from 'react';
import { Modal, Button, Input, Typography } from '@/shared/components/common';
import { Asset } from '../types';

// Placeholder icons (replace with actual icons from your project)
const UploadCloudIcon = () => <span className="mr-2">📤</span>;
const LinkIcon = () => <span className="mr-2">🔗</span>;
const VideoIcon = () => <span className="mr-2">🎬</span>;

interface AssetUploadDialogProps {
  open: boolean;
  onClose: () => void;
  onUpload: (asset: Asset) => void;
}

const AssetUploadDialog: React.FC<AssetUploadDialogProps> = ({
  open,
  onClose,
  onUpload
}) => {
  const [activeTab, setActiveTab] = useState('image');
  const [imageUrl, setImageUrl] = useState('');
  const [imageName, setImageName] = useState('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [videoUrl, setVideoUrl] = useState('');
  const [videoName, setVideoName] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Xử lý khi chọn file
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      setImageName(file.name);

      // Tạo URL tạm thời cho file
      const fileUrl = URL.createObjectURL(file);
      setImageUrl(fileUrl);
    }
  };

  // Xử lý khi nhấn nút tải lên
  const handleUpload = () => {
    if (activeTab === 'image') {
      if (imageUrl) {
        const newAsset: Asset = {
          id: `asset-${Date.now()}`,
          type: 'image',
          src: imageUrl,
          alt: imageName || 'Uploaded Image',
          name: imageName || 'Uploaded Image',
          category: 'uploaded'
        };
        onUpload(newAsset);
        resetForm();
        onClose();
      }
    } else if (activeTab === 'video') {
      // Xử lý tải lên video (nếu cần)
      resetForm();
      onClose();
    }
  };

  // Reset form
  const resetForm = () => {
    setImageUrl('');
    setImageName('');
    setImageFile(null);
    setVideoUrl('');
    setVideoName('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Modal
      title="Tải lên tài nguyên"
      isOpen={open}
      onClose={() => {
        resetForm();
        onClose();
      }}
      size="lg"
      footer={
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>Hủy</Button>
          <Button onClick={handleUpload}>Tải lên</Button>
        </div>
      }
    >
      <div className="mb-4">
        <div className="flex border-b border-gray-200">
          <div
            className={`px-4 py-2 cursor-pointer ${activeTab === 'image' ? 'border-b-2 border-blue-500 text-blue-500' : ''}`}
            onClick={() => setActiveTab('image')}
          >
            <UploadCloudIcon /> Hình ảnh
          </div>
          <div
            className={`px-4 py-2 cursor-pointer ${activeTab === 'url' ? 'border-b-2 border-blue-500 text-blue-500' : ''}`}
            onClick={() => setActiveTab('url')}
          >
            <LinkIcon /> URL
          </div>
          <div
            className={`px-4 py-2 cursor-pointer ${activeTab === 'video' ? 'border-b-2 border-blue-500 text-blue-500' : ''}`}
            onClick={() => setActiveTab('video')}
          >
            <VideoIcon /> Video
          </div>
        </div>
      </div>

      {activeTab === 'image' && (
        <div className="space-y-4">
          <div className="flex flex-col items-center justify-center border-2 border-dashed rounded-md p-6 cursor-pointer hover:bg-gray-50"
            onClick={() => fileInputRef.current?.click()}>
            <UploadCloudIcon />
            <Typography variant="body2" className="text-gray-500">Nhấp để chọn hoặc kéo thả file vào đây</Typography>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleFileChange}
            />
          </div>

          {imageFile && (
            <div className="mt-4">
              <Typography variant="body1" className="font-medium">File đã chọn:</Typography>
              <Typography variant="body2" className="text-gray-500">{imageFile.name} ({Math.round(imageFile.size / 1024)} KB)</Typography>
            </div>
          )}

          <div className="space-y-2">
            <Typography variant="body2" className="font-medium">Tên hình ảnh</Typography>
            <Input
              id="image-name"
              value={imageName}
              onChange={(e) => setImageName(e.target.value)}
              placeholder="Nhập tên hình ảnh"
            />
          </div>
        </div>
      )}

      {activeTab === 'url' && (
        <div className="space-y-4">
          <div className="space-y-2">
            <Typography variant="body2" className="font-medium">URL hình ảnh</Typography>
            <Input
              id="image-url"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              placeholder="https://example.com/image.jpg"
            />
          </div>

          <div className="space-y-2">
            <Typography variant="body2" className="font-medium">Tên hình ảnh</Typography>
            <Input
              id="url-image-name"
              value={imageName}
              onChange={(e) => setImageName(e.target.value)}
              placeholder="Nhập tên hình ảnh"
            />
          </div>
        </div>
      )}

      {activeTab === 'video' && (
        <div className="space-y-4">
          <div className="space-y-2">
            <Typography variant="body2" className="font-medium">URL video</Typography>
            <Input
              id="video-url"
              value={videoUrl}
              onChange={(e) => setVideoUrl(e.target.value)}
              placeholder="https://example.com/video.mp4"
            />
          </div>

          <div className="space-y-2">
            <Typography variant="body2" className="font-medium">Tên video</Typography>
            <Input
              id="video-name"
              value={videoName}
              onChange={(e) => setVideoName(e.target.value)}
              placeholder="Nhập tên video"
            />
          </div>
        </div>
      )}
    </Modal>
  );
};

export default AssetUploadDialog;
