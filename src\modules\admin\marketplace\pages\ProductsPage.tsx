import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Table,
  Modal,
  IconCard,
  Chip,
  ModernMenu,
} from '@/shared/components/common';
import { ConfirmDeleteModal } from '@/modules/admin/marketplace/components/modals';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ViewProductForm, AddProductForm } from '@/modules/admin/marketplace/components/forms';

// Import hooks từ module marketplace
import {
  useProducts,
  useCreateProduct,
  useDeleteProduct,
  useApproveProduct,
  useRejectProduct,
} from '@/modules/admin/marketplace/hooks/useProduct';

// Import types từ module marketplace
import {
  Product,
  ProductCategory,
  ProductStatus,
  ProductFilterParams,
  CreateProductDto,
} from '@/modules/admin/marketplace/types/product.types';

/**
 * Trang quản lý sản phẩm trong marketplace
 */
const ProductsPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const [products, setProducts] = useState<Product[]>([]);

  // Pagination state
  const [paginationMeta, setPaginationMeta] = useState({
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    totalPages: 1,
    itemCount: 0
  });
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [productToView, setProductToView] = useState<Product | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showApproveConfirm, setShowApproveConfirm] = useState(false);
  const [showRejectForm, setShowRejectForm] = useState(false);
  const [rejectReason, setRejectReason] = useState('');

  // State cho action menu
  const [actionMenu, setActionMenu] = useState<{
    visible: boolean;
    recordId: string | null;
    recordName: string | null;
  }>({
    visible: false,
    recordId: null,
    recordName: null,
  });

  // State cho tìm kiếm
  const [searchTerm, setSearchTerm] = useState('');

  // State cho sắp xếp
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Sử dụng hook animation cho form tạo mới
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isViewFormVisible,
    showForm: showViewSlideForm,
    hideForm: hideViewForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo<ProductFilterParams>(() => {
    const params: ProductFilterParams = {
      page: paginationMeta.currentPage,
      limit: paginationMeta.itemsPerPage,
      search: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection === SortDirection.ASC ? 'ASC' : 'DESC',
    };

    return params;
  }, [paginationMeta.currentPage, paginationMeta.itemsPerPage, searchTerm, sortBy, sortDirection]);

  // Hooks để gọi API
  const {
    data: productsData,
    isLoading: isLoadingProducts,
    error: productsError,
  } = useProducts(queryParams);

  const { mutateAsync: createProduct } = useCreateProduct();
  const { mutateAsync: deleteProduct } = useDeleteProduct();
  const { mutateAsync: approveProduct } = useApproveProduct();
  const { mutateAsync: rejectProduct } = useRejectProduct();

  // Xử lý submit form tạo sản phẩm
  const handleSubmitCreateProduct = useCallback(
    async (productData: CreateProductDto) => {
      try {
        // Gọi API tạo sản phẩm
        await createProduct(productData);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating product:', error);
      }
    },
    [createProduct, hideCreateForm]
  );

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (productsData) {
      setProducts(productsData.items);

      // Update pagination meta data
      setPaginationMeta({
        currentPage: productsData.meta.currentPage,
        itemsPerPage: productsData.meta.itemsPerPage,
        totalItems: productsData.meta.totalItems,
        totalPages: productsData.meta.totalPages,
        itemCount: productsData.meta.itemCount
      });
    }
  }, [productsData, productsError]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setPaginationMeta(prev => ({
        ...prev,
        currentPage: page
      }));

      if (newPageSize !== paginationMeta.itemsPerPage) {
        setPaginationMeta(prev => ({
          ...prev,
          itemsPerPage: newPageSize,
          currentPage: 1 // Reset về trang 1 khi thay đổi số mục trên trang
        }));
      }
    },
    [paginationMeta.itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setPaginationMeta(prev => ({
      ...prev,
      currentPage: 1 // Reset về trang 1 khi tìm kiếm
    }));
  }, []);

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((product: Product) => {
    setProductToDelete(product);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setProductToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!productToDelete) return;

    try {
      await deleteProduct(productToDelete.id);
      setShowDeleteConfirm(false);
      setProductToDelete(null);
    } catch (error) {
      console.error('Error deleting product:', error);
    }
  }, [productToDelete, deleteProduct]);

  // Xử lý hiển thị popup xác nhận phê duyệt
  const handleShowApproveConfirm = useCallback((product: Product) => {
    setProductToView(product);
    setShowApproveConfirm(true);
  }, []);

  // Xử lý hủy phê duyệt
  const handleCancelApprove = useCallback(() => {
    setShowApproveConfirm(false);
    setProductToView(null);
  }, []);

  // Xử lý xác nhận phê duyệt
  const handleConfirmApprove = useCallback(async () => {
    if (!productToView) return;

    try {
      await approveProduct(productToView.id);
      setShowApproveConfirm(false);
      setProductToView(null);
    } catch (error) {
      console.error('Error approving product:', error);
    }
  }, [productToView, approveProduct]);

  // Xử lý hiển thị form từ chối
  const handleShowRejectForm = useCallback((product: Product) => {
    setProductToView(product);
    setRejectReason('');
    setShowRejectForm(true);
  }, []);

  // Xử lý hủy từ chối
  const handleCancelReject = useCallback(() => {
    setShowRejectForm(false);
    setProductToView(null);
    setRejectReason('');
  }, []);

  // Xử lý xác nhận từ chối
  const handleConfirmReject = useCallback(async () => {
    if (!productToView || !rejectReason) return;

    try {
      await rejectProduct({ id: productToView.id, reason: rejectReason });
      setShowRejectForm(false);
      setProductToView(null);
      setRejectReason('');
    } catch (error) {
      console.error('Error rejecting product:', error);
    }
  }, [productToView, rejectReason, rejectProduct]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý hiển thị form xem chi tiết sản phẩm
  const handleShowViewForm = useCallback(
    (product: Product) => {
      setProductToView(product);
      showViewSlideForm();
    },
    [showViewSlideForm]
  );

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'name',
        title: t('admin:marketplace.product.table.name', 'Tên sản phẩm'),
        dataIndex: 'name',
        width: '20%',
        sortable: true,
      },
      {
        key: 'category',
        title: t('admin:marketplace.product.table.category', 'Loại'),
        dataIndex: 'category',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const category = value as ProductCategory;
          return (
            <Chip size="sm" variant="default">
              {t(`admin:marketplace.product.category.${category}`, category)}
            </Chip>
          );
        },
      },
      {
        key: 'discountedPrice',
        title: t('admin:marketplace.product.table.price', 'Giá'),
        dataIndex: 'discountedPrice',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          return <span>{String(value)} points</span>;
        },
      },
      {
        key: 'status',
        title: t('admin:marketplace.product.table.status', 'Trạng thái'),
        dataIndex: 'status',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as ProductStatus;
          let variant: 'default' | 'primary' | 'success' | 'warning' | 'danger' = 'default';

          switch (status) {
            case ProductStatus.APPROVED:
              variant = 'success';
              break;
            case ProductStatus.PENDING:
              variant = 'warning';
              break;
            case ProductStatus.REJECTED:
              variant = 'danger';
              break;
            case ProductStatus.DRAFT:
              variant = 'default';
              break;
            case ProductStatus.DELETED:
              variant = 'danger';
              break;
          }

          return (
            <Chip size="sm" variant={variant}>
              {t(`admin:marketplace.product.status.${status}`, status)}
            </Chip>
          );
        },
      },
      {
        key: 'seller',
        title: t('admin:marketplace.product.table.seller', 'Người bán'),
        dataIndex: 'seller',
        width: '15%',
        render: (value: unknown) => {
          const seller = value as { name: string; email?: string };
          return (
            <div className="flex flex-col">
              <span>{seller.name}</span>
              {seller.email && <span className="text-xs text-gray-500">{seller.email}</span>}
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '10%',
        render: (_: unknown, record: Product) => (
          <div className="relative">
            <IconCard
              icon="menu"
              variant="default"
              size="sm"
              onClick={() => {
                setActionMenu({
                  visible: true,
                  recordId: record.id,
                  recordName: record.name,
                });
              }}
            />
            {actionMenu.visible && actionMenu.recordId === record.id && (
              <ModernMenu
                isOpen={true}
                onClose={() => setActionMenu({ visible: false, recordId: null, recordName: null })}
                placement="left"
                items={[
                  {
                    id: 'view',
                    label: t('common:view', 'Xem'),
                    icon: 'eye',
                    onClick: () => {
                      handleShowViewForm(record);
                      setActionMenu({ visible: false, recordId: null, recordName: null });
                    },
                  },
                  ...(record.status === ProductStatus.PENDING
                    ? [
                        {
                          id: 'approve',
                          label: t('admin:marketplace.product.approve', 'Phê duyệt'),
                          icon: 'check',
                          onClick: () => {
                            handleShowApproveConfirm(record);
                            setActionMenu({ visible: false, recordId: null, recordName: null });
                          },
                        },
                        {
                          id: 'reject',
                          label: t('admin:marketplace.product.reject', 'Từ chối'),
                          icon: 'close',
                          onClick: () => {
                            handleShowRejectForm(record);
                            setActionMenu({ visible: false, recordId: null, recordName: null });
                          },
                        },
                      ]
                    : []),
                  {
                    id: 'delete',
                    label: t('common:delete', 'Xóa'),
                    icon: 'trash',
                    onClick: () => {
                      handleShowDeleteConfirm(record);
                      setActionMenu({ visible: false, recordId: null, recordName: null });
                    },
                  },
                ]}
              />
            )}
          </div>
        ),
      },
    ];

    // Lọc các cột dựa trên visibleColumns
    if (visibleColumns.length === 0) {
      return allColumns;
    }

    const visibleColumnIds = visibleColumns.filter(col => col.visible).map(col => col.id);

    // Luôn hiển thị cột actions
    return allColumns.filter(col => col.key === 'actions' || visibleColumnIds.includes(col.key));
  }, [
    t,
    visibleColumns,
    handleShowDeleteConfirm,
    handleShowViewForm,
    handleShowApproveConfirm,
    handleShowRejectForm,
    actionMenu.visible,
    actionMenu.recordId,
  ]);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: 'Tất cả', visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showCreateForm()}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={true}
            showColumnFilter={true}
            items={[]}
          />
        </div>

        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <AddProductForm onSubmit={handleSubmitCreateProduct} onCancel={hideCreateForm} />
        </SlideInForm>

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          <ViewProductForm
            product={productToView}
            onClose={hideViewForm}
          />
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<Product>
            columns={filteredColumns}
            data={products}
            rowKey="id"
            loading={isLoadingProducts}
            sortable={true}
            onSortChange={handleSortChange}
            defaultSort={{
              column: sortBy || '',
              order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: paginationMeta.currentPage,
              pageSize: paginationMeta.itemsPerPage,
              total: paginationMeta.totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t(
          'admin:marketplace.product.confirmDeleteMessage',
          'Bạn có chắc chắn muốn xóa sản phẩm này?'
        )}
        itemName={productToDelete?.name}
      />

      {/* Modal xác nhận phê duyệt */}
      <ConfirmDeleteModal
        isOpen={showApproveConfirm}
        onClose={handleCancelApprove}
        onConfirm={handleConfirmApprove}
        title={t('admin.marketplace.product.approveTitle', 'Phê duyệt sản phẩm')}
        message={t(
          'admin.marketplace.product.confirmApproveMessage',
          'Bạn có chắc chắn muốn phê duyệt sản phẩm này?'
        )}
        itemName={productToView?.name}
      />

      {/* Modal từ chối sản phẩm */}
      <Modal
        isOpen={showRejectForm}
        onClose={handleCancelReject}
        title={t('admin.marketplace.product.rejectTitle', 'Từ chối sản phẩm')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancelReject}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="danger" onClick={handleConfirmReject} disabled={!rejectReason}>
              {t('admin.marketplace.product.reject', 'Từ chối')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography className="mb-2">
            {t(
              'admin.marketplace.product.confirmRejectMessage',
              'Vui lòng nhập lý do từ chối sản phẩm:'
            )}
          </Typography>
          {productToView && (
            <Typography variant="body2" className="mb-4 font-semibold">
              {productToView.name}
            </Typography>
          )}
          <textarea
            className="w-full p-2 border border-gray-300 rounded-md"
            rows={4}
            value={rejectReason}
            onChange={e => setRejectReason(e.target.value)}
            placeholder={t(
              'admin.marketplace.product.rejectReasonPlaceholder',
              'Nhập lý do từ chối...'
            )}
          />
        </div>
      </Modal>
    </div>
  );
};

export default ProductsPage;
