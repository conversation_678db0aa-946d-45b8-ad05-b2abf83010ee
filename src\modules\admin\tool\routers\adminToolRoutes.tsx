import { RouteObject } from 'react-router-dom';
import { Suspense } from 'react';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { Loading } from '@/shared';
import { lazy } from 'react';
const ToolsPage = lazy(() => import('../pages/ToolsPage'));
const ToolManagementPage = lazy(() => import('../pages/ToolManagementPage'));
const ToolGroupsPage = lazy(() => import('../pages/ToolGroupsPage'));
const ToolVersionsPage = lazy(() => import('../pages/ToolVersionsPage'));

/**
 * Routes cho module admin tool
 */
const adminToolRoutes: RouteObject[] = [
  // Trang tổng quan quản lý tools
  {
    path: '/admin/tools',
    element: (
      <AdminLayout title="Admin Tool Management">
        <Suspense fallback={<Loading />}>
          <ToolManagementPage />
        </Suspense>
      </AdminLayout>
    ),
  },

  // Trang quản lý danh sách tools
  {
    path: '/admin/tools/list',
    element: (
      <AdminLayout title="Admin Tools List">
        <Suspense fallback={<Loading />}>
          <ToolsPage />
        </Suspense>
      </AdminLayout>
    ),
  },

  // Trang quản lý nhóm tools
  {
    path: '/admin/tools/groups',
    element: (
      <AdminLayout title="Admin Tool Groups">
        <Suspense fallback={<Loading />}>
          <ToolGroupsPage />
        </Suspense>
      </AdminLayout>
    ),
  },

  // Trang quản lý phiên bản tools
  {
    path: '/admin/tools/versions',
    element: (
      <AdminLayout title="Admin Tool Versions">
        <Suspense fallback={<Loading />}>
          <ToolVersionsPage />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default adminToolRoutes;
