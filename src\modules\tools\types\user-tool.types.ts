import { AccessType, BaseQueryParams, ToolStatus, UserInfo } from './common.types';

/**
 * Interface định nghĩa tham số truy vấn cho danh sách tool của người dùng
 */
export interface UserToolQueryParams extends BaseQueryParams {
  status?: ToolStatus;
  access?: AccessType;
}

/**
 * Interface định nghĩa thông tin cơ bản của tool
 */
export interface UserToolListItem {
  id: string;
  name: string;
  description?: string;
  status: ToolStatus;
  access: AccessType;
  createdAt: number;
  updatedAt: number;
  createdBy?: UserInfo;
  updatedBy?: UserInfo;
  isDefault?: boolean;
  defaultVersion?: UserToolVersion;
  isFromAdmin?: boolean;
  adminToolId?: string;
}

/**
 * Interface định nghĩa thông tin phiên bản của tool
 */
export interface UserToolVersion {
  id: string;
  versionNumber: number;
  toolName: string;
  toolDescription?: string | null;
  parameters: Record<string, unknown>;
  changeDescription?: string | null;
  status: ToolStatus;
  createdAt: number;
  updatedAt?: number;
  createdBy?: UserInfo;
  updatedBy?: UserInfo;
  isFromAdmin?: boolean;
  adminVersionId?: string;
  edited?: boolean;
}

/**
 * Interface định nghĩa thông tin chi tiết của tool
 */
export interface UserToolDetail extends UserToolListItem {
  versions: UserToolVersion[];
}

/**
 * Interface định nghĩa tham số tạo mới tool
 */
export interface CreateUserToolParams {
  name: string;
  description?: string;
  access?: AccessType;
  adminToolId?: string;
  adminVersionId?: string;
}

/**
 * Interface định nghĩa tham số cập nhật tool
 */
export interface UpdateUserToolParams {
  name?: string;
  description?: string;
  access?: AccessType;
}

/**
 * Interface định nghĩa tham số chỉnh sửa phiên bản tool
 */
export interface EditUserToolVersionParams {
  toolName: string;
  toolDescription?: string;
  parameters: Record<string, unknown>;
  changeDescription?: string;
  status?: ToolStatus;
}

/**
 * Interface định nghĩa tham số sao chép tool từ admin
 */
export interface CloneAdminToolParams {
  adminToolId: string;
  adminVersionId?: string;
}

/**
 * Interface định nghĩa tham số cập nhật tool từ phiên bản admin
 */
export interface UpdateFromAdminParams {
  userToolId: string;
  adminVersionId: string;
}

/**
 * Interface định nghĩa tham số khôi phục về phiên bản admin
 */
export interface RollbackToAdminParams {
  userToolId: string;
  adminVersionId: string;
}
