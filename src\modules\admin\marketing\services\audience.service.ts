import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import {
  AudienceDetailResponse,
  AudienceFilterParams,
  AudienceFormData,
  AudienceListResponse,
} from '../types/audience.types';

/**
 * Service cho quản lý audience
 */
export const AudienceService = {
  /**
   * Lấy danh sách audience
   * @param params Tham số filter
   * @returns Promise với danh sách audience
   */
  getAudiences: async (
    params?: AudienceFilterParams
  ): Promise<ApiResponseDto<AudienceListResponse>> => {
    const response = await apiClient.get<AudienceListResponse>('/v1/admin/audience', { params });
    return response;
  },

  /**
   * Lấy chi tiết audience
   * @param id ID của audience
   * @returns Promise với chi tiết audience
   */
  getAudience: async (id: string): Promise<ApiResponseDto<AudienceDetailResponse>> => {
    const response = await apiClient.get<AudienceDetailResponse>(`/v1/admin/audience/${id}`);
    return response;
  },

  /**
   * Tạo audience mới
   * @param data Dữ liệu audience cần tạo
   * @returns Promise với audience đã tạo
   */
  createAudience: async (
    data: AudienceFormData
  ): Promise<ApiResponseDto<AudienceDetailResponse>> => {
    const response = await apiClient.post<AudienceDetailResponse>('/v1/admin/audience', data);
    return response;
  },

  /**
   * Cập nhật audience
   * @param id ID của audience
   * @param data Dữ liệu audience cần cập nhật
   * @returns Promise với audience đã cập nhật
   */
  updateAudience: async (
    id: string,
    data: AudienceFormData
  ): Promise<ApiResponseDto<AudienceDetailResponse>> => {
    const response = await apiClient.put<AudienceDetailResponse>(`/v1/admin/audience/${id}`, data);
    return response;
  },

  /**
   * Xóa audience
   * @param id ID của audience
   * @returns Promise với kết quả xóa
   */
  deleteAudience: async (id: string): Promise<ApiResponseDto<null>> => {
    const response = await apiClient.delete<null>(`/v1/admin/audience/${id}`);
    return response;
  },

  /**
   * Xóa nhiều audience
   * @param ids Danh sách ID của audience cần xóa
   * @returns Promise với kết quả xóa
   */
  deleteMultipleAudiences: async (ids: string[]): Promise<ApiResponseDto<null>> => {
    const response = await apiClient.delete<null>('/v1/admin/audience', { data: { ids } });
    return response;
  },
};
