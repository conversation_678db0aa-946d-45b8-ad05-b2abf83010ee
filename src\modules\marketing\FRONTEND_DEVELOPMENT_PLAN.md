# Kế hoạch phát triển Frontend cho Module Marketing mở rộng

> Ng<PERSON>y cập nhật: 2025-05-23

## 1. <PERSON><PERSON><PERSON> ti<PERSON><PERSON> sung các phân hệ mới vào module `marketing` gồm:

1. Email Marketing (sử dụng lại trang TemplateEmailPage)
2. SMS Marketing
3. Google Ads Integration
4. Facebook Ads Integration
5. Zalo OA/ZNS Integration

Mỗi phân hệ cần đáp ứng:

- <PERSON> chuẩn ESLint của dự án (airbnb-typescript + prettier).
- Hỗ trợ đa ngôn ngữ (i18n: vi, en, zh).
- Tương thích theme light/dark.
- Tận dụng tối đa **shared components** (`Card`, `Table`, `MenuIconBar`, `ActiveFilters`, …).
- Tách biệt hook, service, type theo chuẩn hiện tại.

## 2. Phạm vi & Roadmap

| <PERSON><PERSON><PERSON> | <PERSON><PERSON><PERSON><PERSON> (ướ<PERSON> t<PERSON>) | <PERSON><PERSON><PERSON>ụ<PERSON> |
|-----------|----------------------|----------------|
| **G1 – Khởi tạo** | 0.5 ngày | • Khởi tạo skeleton pages (SMS, Google Ads, Facebook Ads, Zalo)
| **G2 – UI/UX cơ bản** | 1.5 ngày | • Thêm ModuleCard trên `MarketingPage`  
• Thiết lập routes, i18n keys  
• Layout bảng danh sách (tài khoản/chiến dịch) theo style `KnowledgeFilesPage` & `AffiliateRankListPageOptimized` |
| **G3 – Tích hợp API** | 3 ngày | • Xây hook `useGoogleAdsAccounts`, `useGoogleAdsCampaigns`  
• Xây hook SMS (template, brandname), Facebook Ads, Zalo OA  
• Hiển thị dữ liệu bảng, filter, pagination |
| **G4 – Chức năng nâng cao** | 2 ngày | • Thêm modal tạo/sửa/xóa  
• Upload/preview file (nếu có)  
• Biểu đồ thống kê hiệu suất (Google Ads / Facebook Ads) |
| **G5 – Kiểm thử & Hoàn thiện** | 1 ngày | • Kiểm thử QA  
• Fix bug, tối ưu hiệu năng  
• Viết unit test cho hooks & component quan trọng |

## 3. Cấu trúc thư mục đề xuất
```
src/modules/marketing/
├── pages/
│   ├── MarketingPage.tsx            # Tổng quan
│   ├── SmsMarketingPage.tsx         # SMS Marketing
│   ├── GoogleAdsPage.tsx            # Google Ads
│   ├── FacebookAdsPage.tsx          # Facebook Ads
│   └── ZaloMarketingPage.tsx        # Zalo
├── hooks/
│   ├── google-ads/
│   │   ├── useGoogleAdsAccounts.ts
│   │   └── useGoogleAdsCampaigns.ts
│   └── ...
├── services/
│   ├── google-ads.service.ts
│   ├── sms.service.ts
│   └── ...
├── components/
│   ├── google-ads/
│   │   └── GoogleAdsAccountForm.tsx
│   └── ...
└── marketingRoutes.tsx
```

## 4. Quy ước phát triển

1. **ESLint & Prettier**: tuân thủ rule hiện tại, tuyệt đối không disable rule trừ khi bất khả kháng.
2. **Ngôn ngữ**: `t('marketing:sms.title', 'SMS Marketing')` – luôn truyền fallback tiếng Việt.
3. **Theme**: sử dụng class `dark:` khi cần, tránh inline-style.
4. **Bình luận**: comment tiếng Việt rõ ràng, ngắn gọn.
5. **Commit**: `feat(marketing): add sms skeleton page`, … sau mỗi thay đổi nhỏ.
6. **Build**: chạy `npm run build` & fix lỗi ngay.

## 5. Rủi ro & Giảm thiểu

- **API chưa hoàn thiện** ➜ Mock response tạm thời, sử dụng MSW.
- **Thay đổi yêu cầu** ➜ Chia task nhỏ, review hằng ngày.

---
*Người lập kế hoạch: AI Assistant* 