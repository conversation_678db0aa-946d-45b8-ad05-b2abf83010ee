import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Table,
  ActionMenu,
  ActionMenuItem
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import { GoogleAdsCampaignDto, GoogleAdsCampaignStatus, GoogleAdsCampaignType } from '../../types';

interface GoogleAdsCampaignTableProps {
  campaigns: GoogleAdsCampaignDto[];
  loading: boolean;
  onEdit?: (campaign: GoogleAdsCampaignDto) => void;
  onDelete?: (campaign: GoogleAdsCampaignDto) => void;
  onStatusChange?: (campaign: GoogleAdsCampaignDto, status: GoogleAdsCampaignStatus) => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
}

/**
 * Bảng hiển thị danh sách chiến dịch Google Ads
 */
const GoogleAdsCampaignTable: React.FC<GoogleAdsCampaignTableProps> = ({
  campaigns,
  loading,
  onEdit,
  onDelete,
  onStatusChange,
  pagination,
}) => {
  const { t } = useTranslation(['marketing', 'common']);

  // Hàm format số tiền
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Hàm format ngày
  const formatDate = (dateString?: string): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Định nghĩa các cột cho bảng
  const columns: TableColumn<GoogleAdsCampaignDto>[] = [
    {
      key: 'name',
      title: t('marketing:googleAds.campaignName', 'Tên chiến dịch'),
      dataIndex: 'name',
      width: '20%',
    },
    {
      key: 'campaignId',
      title: t('marketing:googleAds.campaignId', 'ID chiến dịch'),
      dataIndex: 'campaignId',
      width: '15%',
    },
    {
      key: 'type',
      title: t('marketing:googleAds.campaignType', 'Loại chiến dịch'),
      dataIndex: 'type',
      width: '15%',
      render: (value: unknown) => {
        const type = value as GoogleAdsCampaignType;
        return t(`marketing:googleAds.campaignTypes.${type}`, type);
      },
    },
    {
      key: 'budget',
      title: t('marketing:googleAds.budget', 'Ngân sách'),
      dataIndex: 'budget',
      width: '10%',
      render: (value: unknown) => {
        return formatCurrency(value as number);
      },
    },
    {
      key: 'status',
      title: t('common:status', 'Trạng thái'),
      dataIndex: 'status',
      width: '10%',
      render: (value: unknown) => {
        const status = value as GoogleAdsCampaignStatus;
        let color = '';
        let textColor = '';
        
        switch (status) {
          case GoogleAdsCampaignStatus.ENABLED:
            color = 'bg-green-100 dark:bg-green-900';
            textColor = 'text-green-800 dark:text-green-200';
            break;
          case GoogleAdsCampaignStatus.PAUSED:
            color = 'bg-yellow-100 dark:bg-yellow-900';
            textColor = 'text-yellow-800 dark:text-yellow-200';
            break;
          case GoogleAdsCampaignStatus.REMOVED:
            color = 'bg-red-100 dark:bg-red-900';
            textColor = 'text-red-800 dark:text-red-200';
            break;
          case GoogleAdsCampaignStatus.DRAFT:
            color = 'bg-gray-100 dark:bg-gray-700';
            textColor = 'text-gray-800 dark:text-gray-200';
            break;
          default:
            color = 'bg-gray-100 dark:bg-gray-700';
            textColor = 'text-gray-800 dark:text-gray-200';
        }

        return (
          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${color} ${textColor}`}>
            {t(`marketing:googleAds.campaignStatuses.${status}`, status)}
          </div>
        );
      },
    },
    {
      key: 'dates',
      title: t('marketing:googleAds.startDate', 'Ngày bắt đầu'),
      dataIndex: 'startDate',
      width: '10%',
      render: (value: unknown) => {
        return formatDate(value as string);
      },
    },
    {
      key: 'endDate',
      title: t('marketing:googleAds.endDate', 'Ngày kết thúc'),
      dataIndex: 'endDate',
      width: '10%',
      render: (value: unknown) => {
        return value ? formatDate(value as string) : '-';
      },
    },
    {
      key: 'actions',
      title: t('common:actions', 'Thao tác'),
      width: '10%',
      render: (_: unknown, record: GoogleAdsCampaignDto) => {
        // Tạo danh sách các action items
        const actionItems: ActionMenuItem[] = [
          {
            id: 'edit',
            label: t('common:edit', 'Chỉnh sửa'),
            icon: 'edit',
            onClick: () => onEdit?.(record),
          },
          {
            id: 'delete',
            label: t('common:delete', 'Xóa'),
            icon: 'trash',
            onClick: () => onDelete?.(record),
          },
        ];

        // Thêm action kích hoạt/tạm dừng tùy theo trạng thái hiện tại
        if (record.status === GoogleAdsCampaignStatus.ENABLED) {
          actionItems.push({
            id: 'pause',
            label: t('common:pause', 'Tạm dừng'),
            icon: 'pause',
            onClick: () => onStatusChange?.(record, GoogleAdsCampaignStatus.PAUSED),
          });
        } else if (record.status === GoogleAdsCampaignStatus.PAUSED) {
          actionItems.push({
            id: 'enable',
            label: t('common:enable', 'Kích hoạt'),
            icon: 'play',
            onClick: () => onStatusChange?.(record, GoogleAdsCampaignStatus.ENABLED),
          });
        }

        return (
          <div className="flex justify-center">
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm hành động')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              menuIcon="more-horizontal"
              showAllInMenu={true}
              preferRight={true}
              preferTop={false}
            />
          </div>
        );
      },
    },
  ];

  return (
    <Table
      columns={columns}
      data={campaigns}
      rowKey="id"
      loading={loading}
      pagination={pagination}
    />
  );
};

export default GoogleAdsCampaignTable;