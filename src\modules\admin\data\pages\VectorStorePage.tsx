import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Table, Modal, Input, ConfirmDeleteModal } from '@/shared/components/common';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ModernMenuTrigger } from '@/shared/components/common/ModernMenu';
import { NotificationUtil } from '@/shared/utils/notification';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import AssignFilesToVectorStoreForm from '@/modules/data/components/forms/AssignFilesToVectorStoreForm';

import {
  useVectorStores,
  useCreateVectorStore,
  useDeleteVectorStore,
  useAssignFilesToVectorStore,
} from '@/modules/admin/data/knowledge-files/hooks';
import {
  VectorStoreDto,
  VectorStoreQueryParams,
  CreateVectorStoreDto,
  AssignFilesToVectorStoreDto,
} from '@/modules/admin/data/knowledge-files/types';
import { formatDate } from '@/shared/utils/format';

// Hàm formatBytes tạm thời
const formatBytes = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Form tạo vector store
 */
const VectorStoreForm: React.FC<{
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  isLoading?: boolean;
  readOnly?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  initialValues?: any; // Sử dụng any để tránh lỗi type
}> = ({ onSubmit, onCancel, isLoading, readOnly, initialValues }) => {
  const { t } = useTranslation();
  const [name, setName] = useState((initialValues?.name as string) || '');
  const [error, setError] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      setError(t('admin.data.vectorStore.nameRequired', 'Vector store name is required'));
      return;
    }

    onSubmit({ name });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 p-6 bg-white dark:bg-gray-800">
      <Typography variant="h5" className="mb-4 text-primary-600 dark:text-primary-400">
        {readOnly
          ? t('admin.data.vectorStore.viewVectorStore', 'View Vector Store')
          : t('admin.data.vectorStore.createVectorStore', 'Create New Vector Store')}
      </Typography>

      {!readOnly ? (
        <div className="space-y-4">
          <div className={`${error ? 'text-red-500' : ''}`}>
            <label className="block text-sm font-medium mb-2">
              {t('admin.data.vectorStore.name', 'Vector Store Name')}
            </label>
            <Input
              value={name}
              onChange={e => {
                setName(e.target.value);
                setError('');
              }}
              placeholder={t('admin.data.vectorStore.namePlaceholder', 'Enter vector store name')}
              disabled={readOnly || isLoading}
              className="w-full"
            />
            {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
          </div>

          <div className="text-sm text-gray-500 dark:text-gray-400 mt-2">
            <p>{t('admin.data.vectorStore.nameHint', 'Choose a descriptive name for your vector store. This will help you identify it later.')}</p>
          </div>
        </div>
      ) : (
        <div className="space-y-4 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Typography variant="body2" className="font-semibold text-gray-600 dark:text-gray-300">
                {t('admin.data.vectorStore.name', 'Vector Store Name')}
              </Typography>
              <Typography className="text-gray-800 dark:text-gray-100">{initialValues?.storeName as string}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="font-semibold text-gray-600 dark:text-gray-300">
                {t('admin.data.vectorStore.size', 'Size')}
              </Typography>
              <Typography className="text-gray-800 dark:text-gray-100">{formatBytes(initialValues?.size as number)}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="font-semibold text-gray-600 dark:text-gray-300">
                {t('admin.data.vectorStore.files', 'Number of files')}
              </Typography>
              <Typography className="text-gray-800 dark:text-gray-100">{(initialValues?.files as number) || 0}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="font-semibold text-gray-600 dark:text-gray-300">
                {t('admin.data.vectorStore.agents', 'Number of agents using')}
              </Typography>
              <Typography className="text-gray-800 dark:text-gray-100">{(initialValues?.agents as number) || 0}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="font-semibold text-gray-600 dark:text-gray-300">
                {t('admin.data.vectorStore.createdAt', 'Created at')}
              </Typography>
              <Typography className="text-gray-800 dark:text-gray-100">{formatDate(initialValues?.createdAt as number)}</Typography>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700 mt-6">
        <Button variant="outline" onClick={onCancel} disabled={isLoading}>
          {t('common.cancel', 'Cancel')}
        </Button>
        {!readOnly && (
          <Button type="submit" variant="primary" disabled={isLoading}>
            {isLoading
              ? t('admin.data.common.creating', 'Creating...')
              : t('admin.data.common.create', 'Create')}
          </Button>
        )}
      </div>
    </form>
  );
};

// AssignFilesForm đã được thay thế bằng AssignFilesToVectorStoreForm

/**
 * Trang quản lý vector store
 */
const VectorStorePage: React.FC = () => {
  const { t } = useTranslation();
  const [vectorStores, setVectorStores] = useState<VectorStoreDto[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [totalItems, setTotalItems] = useState(0);
  const [vectorStoreToDelete, setVectorStoreToDelete] = useState<VectorStoreDto | null>(null);
  const [vectorStoreToView, setVectorStoreToView] = useState<VectorStoreDto | null>(null);
  const [vectorStoreToAssign, setVectorStoreToAssign] = useState<VectorStoreDto | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [selectedVectorStoreIds, setSelectedVectorStoreIds] = useState<string[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [isAssigning, setIsAssigning] = useState(false);















  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isViewFormVisible,
    showForm: showViewSlideForm,
    hideForm: hideViewForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form gán file
  const {
    isVisible: isAssignFormVisible,
    showForm: showAssignForm,
    hideForm: hideAssignForm,
  } = useSlideForm();



  // Khai báo các hàm xử lý sự kiện trước khi sử dụng trong columns
  const handleShowDeleteConfirm = useCallback((vectorStore: VectorStoreDto) => {
    setVectorStoreToDelete(vectorStore);
    setShowDeleteConfirm(true);
  }, []);

  const handleShowViewForm = useCallback(
    (vectorStore: VectorStoreDto) => {
      setVectorStoreToView(vectorStore);
      showViewSlideForm();
    },
    [showViewSlideForm]
  );

  const handleShowAssignForm = useCallback((vectorStore: VectorStoreDto) => {
    setVectorStoreToAssign(vectorStore);
    showAssignForm();
  }, [showAssignForm]);

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'storeName',
        title: t('admin.data.vectorStore.table.name', 'Vector Store Name'),
        dataIndex: 'storeName',
        width: '25%',
        sortable: true,
      },
      {
        key: 'size',
        title: t('admin.data.vectorStore.table.size', 'Size'),
        dataIndex: 'size',
        width: '15%',
        sortable: true,
        render: (value: unknown) => formatBytes(value as number),
      },
      {
        key: 'files',
        title: t('admin.data.vectorStore.table.files', 'Files'),
        dataIndex: 'files',
        width: '10%',
        sortable: true,
      },
      {
        key: 'agents',
        title: t('admin.data.vectorStore.table.agents', 'Agents'),
        dataIndex: 'agents',
        width: '10%',
        sortable: true,
      },
      {
        key: 'createdAt',
        title: t('admin.data.vectorStore.table.createdAt', 'Created at'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => formatDate(value as number),
      },
      {
        key: 'actions',
        title: t('', ''),
        render: (_: unknown, record: VectorStoreDto) => {
          const menuItems = [
            {
              label: t('common.view', 'View'),
              icon: 'eye',
              onClick: () => handleShowViewForm(record),
            },
            {
              label: t('admin.data.vectorStore.assignFiles', 'Assign Files'),
              icon: 'file-plus',
              onClick: () => handleShowAssignForm(record),
            },
            {
              label: t('common.delete', 'Delete'),
              icon: 'trash',
              onClick: () => handleShowDeleteConfirm(record),
              variant: 'primary' as const,
            },
          ];

          return <ModernMenuTrigger items={menuItems} placement="left" />;
        },
      },
    ];

    return allColumns;
  }, [t, handleShowDeleteConfirm, handleShowViewForm, handleShowAssignForm]);

  // Sử dụng hook useDataTable để quản lý dữ liệu bảng
  const dataTableConfig = useDataTableConfig<VectorStoreDto, VectorStoreQueryParams>({
    columns,
    filterOptions: [],
    showDateFilter: false,
    createQueryParams: (params) => ({
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection as 'ASC' | 'DESC' | undefined,
    }),
  });

  const dataTable = useDataTable(dataTableConfig);

  // Tạo query params cho API
  const queryParams = useMemo<VectorStoreQueryParams>(() => {
    const params: VectorStoreQueryParams = {
      page: dataTable.tableData.currentPage,
      limit: dataTable.tableData.pageSize,
      search: dataTable.tableData.searchTerm || undefined,
      sortBy: dataTable.tableData.sortBy || undefined,
      sortDirection: dataTable.tableData.sortDirection as 'ASC' | 'DESC' | undefined,
    };

    return params;
  }, [
    dataTable.tableData.currentPage,
    dataTable.tableData.pageSize,
    dataTable.tableData.searchTerm,
    dataTable.tableData.sortBy,
    dataTable.tableData.sortDirection,
  ]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearSort,
    handleClearAll,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {},
    t,
  });

  // Hooks để gọi API
  const {
    data: vectorStoresData,
    isLoading: isLoadingVectorStores,
    error: vectorStoresError,
  } = useVectorStores(queryParams);

  const { mutateAsync: createVectorStore } = useCreateVectorStore();
  const { deleteStores } = useDeleteVectorStore();
  const { mutateAsync: assignFilesToVectorStore } = useAssignFilesToVectorStore();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (vectorStoresData) {
      setVectorStores(vectorStoresData.items);
      setTotalItems(vectorStoresData.meta.totalItems);
    }

    setIsLoading(isLoadingVectorStores);
  }, [vectorStoresData, vectorStoresError, isLoadingVectorStores]);

  // Xử lý thay đổi trang - sử dụng dataTable
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      dataTable.tableData.handlePageChange(page, newPageSize);
    },
    [dataTable.tableData]
  );

  // Xử lý tìm kiếm - sử dụng dataTable
  const handleSearch = useCallback((term: string) => {
    dataTable.tableData.handleSearch(term);
  }, [dataTable.tableData]);

  // Xử lý thay đổi sắp xếp - sử dụng dataTable
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    dataTable.tableData.handleSortChange(column, order);
  }, [dataTable.tableData]);



  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setVectorStoreToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!vectorStoreToDelete) return;

    try {
      await deleteStores(vectorStoreToDelete.storeId.toString());
      setShowDeleteConfirm(false);
      setVectorStoreToDelete(null);

      NotificationUtil.success({
        message: t('admin.data.vectorStore.deleteSuccess', 'Vector Store deleted successfully'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting vector store:', error);
      NotificationUtil.error({
        message: t('admin.data.vectorStore.deleteError', 'Error deleting Vector Store'),
        duration: 3000,
      });
    }
  }, [vectorStoreToDelete, deleteStores, t]);

  // Xử lý hiển thị xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedVectorStoreIds.length > 0) {
      setShowBulkDeleteConfirm(true);
    } else {
      NotificationUtil.info({
        message: t('admin.data.vectorStore.selectToDelete', 'Please select at least one Vector Store to delete'),
        duration: 3000,
      });
    }
  }, [selectedVectorStoreIds.length, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedVectorStoreIds.length === 0) return;

    try {
      // Xóa từng Vector Store một
      await Promise.all(selectedVectorStoreIds.map(id => deleteStores(id)));

      setShowBulkDeleteConfirm(false);
      setSelectedVectorStoreIds([]);

      NotificationUtil.success({
        message: t('admin.data.vectorStore.bulkDeleteSuccess', 'Selected Vector Stores deleted successfully'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting multiple Vector Stores:', error);
      NotificationUtil.error({
        message: t('admin.data.vectorStore.bulkDeleteError', 'Error deleting selected Vector Stores'),
        duration: 3000,
      });
    }
  }, [selectedVectorStoreIds, deleteStores, t]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);



  // Xử lý đóng form xem chi tiết vector store
  const handleCloseViewForm = useCallback(() => {
    hideViewForm();
    // Đặt timeout để đảm bảo animation đóng hoàn tất trước khi xóa dữ liệu
    setTimeout(() => {
      setVectorStoreToView(null);
    }, 300);
  }, [hideViewForm]);



  // Xử lý submit form tạo vector store
  const handleSubmitCreateVectorStore = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        setIsCreating(true);

        // Chuẩn bị dữ liệu cho API
        const vectorStoreData: CreateVectorStoreDto = {
          name: values.name as string,
        };

        // Gọi API tạo vector store
        await createVectorStore(vectorStoreData);
        hideForm();
        setIsCreating(false);
      } catch (error) {
        console.error('Error creating vector store:', error);
        setIsCreating(false);
      }
    },
    [createVectorStore, hideForm]
  );

  // Xử lý submit form gán file
  const handleSubmitAssignFiles = useCallback(
    async (values: Record<string, unknown>) => {
      if (!vectorStoreToAssign) return;

      try {
        setIsAssigning(true);

        // Chuẩn bị dữ liệu cho API
        const assignData: { vectorStoreId: string; data: AssignFilesToVectorStoreDto } = {
          vectorStoreId: vectorStoreToAssign.storeId.toString(),
          data: {
            fileIds: values.fileIds as string[],
          },
        };

        // Gọi API gán file
        await assignFilesToVectorStore(assignData);

        // Đóng form và hiển thị thông báo thành công
        hideAssignForm();
        setVectorStoreToAssign(null);

        NotificationUtil.success({
          message: t('admin.data.vectorStore.assignSuccess', 'Files assigned successfully'),
          duration: 3000,
        });
      } catch (error) {
        console.error('Error assigning files:', error);
        NotificationUtil.error({
          message: t('admin.data.vectorStore.assignError', 'Error assigning files'),
          duration: 3000,
        });
      } finally {
        setIsAssigning(false);
      }
    },
    [vectorStoreToAssign, assignFilesToVectorStore, hideAssignForm, t]
  );





  // Lọc các cột hiển thị
  const filteredColumns = useMemo(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: t('common.all', 'All'), visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns, t]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          {/* Thêm MenuIconBar */}
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showForm()}
            items={[
              {
                id: 'all',
                label: t('common.all', 'All'),
                icon: 'list',
                onClick: () => '',
              },
            ]}
            additionalIcons={[
              {
                icon: 'trash',
                tooltip: t('common:bulkDelete', 'Delete multiple'),
                variant: 'primary',
                onClick: handleShowBulkDeleteConfirm,
                condition: selectedVectorStoreIds.length > 0,
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
          />

          {/* Hiển thị ActiveFilters */}
          <ActiveFilters
            searchTerm={dataTable.tableData.searchTerm}
            onClearSearch={handleClearSearch}
            sortBy={dataTable.tableData.sortBy}
            sortDirection={dataTable.tableData.sortDirection as SortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        {/* SlideInForm cho form thêm mới */}
        <SlideInForm isVisible={isVisible}>
          <VectorStoreForm
            onSubmit={handleSubmitCreateVectorStore}
            onCancel={hideForm}
            isLoading={isCreating}
          />
        </SlideInForm>

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {vectorStoreToView && (
            <VectorStoreForm
              initialValues={vectorStoreToView}
              onSubmit={() => {}} // Không cần xử lý submit vì chỉ xem
              onCancel={handleCloseViewForm}
              readOnly={true} // Chế độ chỉ đọc
            />
          )}
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<VectorStoreDto>
            columns={filteredColumns}
            data={vectorStores}
            rowKey="storeId"
            loading={isLoading}
            sortable={true}
            onSortChange={handleSortChange}
            rowSelection={{
              selectedRowKeys: selectedVectorStoreIds,
              onChange: (keys: React.Key[]) => setSelectedVectorStoreIds(keys as string[])
            }}
            defaultSort={{
              column: dataTable.tableData.sortBy || '',
              order: dataTable.tableData.sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: dataTable.tableData.currentPage,
              pageSize: dataTable.tableData.pageSize,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('admin.data.common.confirmDelete', 'Confirm Delete')}
        size="sm"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancelDelete}>
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button variant="danger" onClick={handleConfirmDelete}>
              {t('common.delete', 'Delete')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography>
            {t(
              'admin.data.vectorStore.confirmDeleteMessage',
              'Are you sure you want to delete this vector store?'
            )}
          </Typography>
          {vectorStoreToDelete && (
            <Typography variant="body2" className="mt-2 font-semibold">
              {vectorStoreToDelete.storeName}
            </Typography>
          )}
        </div>
      </Modal>

      {/* SlideInForm cho form gán file */}
      <SlideInForm isVisible={isAssignFormVisible}>
        {vectorStoreToAssign && (
          <AssignFilesToVectorStoreForm
            onSubmit={handleSubmitAssignFiles}
            onCancel={hideAssignForm}
            isLoading={isAssigning}
            vectorStoreId={vectorStoreToAssign.storeId.toString()}
          />
        )}
      </SlideInForm>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('admin.data.vectorStore.bulkDeleteTitle', 'Delete Selected Vector Stores')}
        message={t(
          'admin.data.vectorStore.bulkDeleteMessage',
          'Are you sure you want to delete {{count}} selected Vector Stores?',
          { count: selectedVectorStoreIds.length }
        )}
      />
    </div>
  );
};

export default VectorStorePage;
