import React, { useState, useRef, useEffect } from 'react';
import { Button, Input } from '@/shared/components/common';
import { EmailData } from '../types';
import {
  Save,
  Eye,
  EyeOff,
  Smartphone,
  Tablet,
  Monitor,
  Undo2,
  Redo2,
  FileCode,
  Maximize2,
  Minimize2,
  PanelLeft as LayoutPanelLeft,
  PanelRight as LayoutPanelRight,
  Upload,
  Download,
  Code,
  ChevronDown,
} from 'lucide-react';

// <PERSON><PERSON>u hình kích thước cho các icon
const iconSize = 16;

interface EmailToolbarProps {
  emailData: EmailData;
  setEmailData: (data: EmailData) => void;
  showPreview: boolean;
  setShowPreview: (show: boolean) => void;
  viewMode: 'desktop' | 'tablet' | 'mobile';
  setViewMode: (mode: 'desktop' | 'tablet' | 'mobile') => void;
  canUndo: boolean;
  canRedo: boolean;
  onUndo: () => void;
  onRedo: () => void;
  onSave: () => void;
  onShowCode: () => void;
  isFullscreen: boolean;
  toggleFullscreen: () => void;
  leftPanelVisible: boolean;
  toggleLeftPanel: () => void;
  rightPanelVisible: boolean;
  toggleRightPanel: () => void;
  onShowAssetUpload?: () => void;
  onShowGrapesJSInfo?: () => void;
  // Thêm các props mới
  onSaveHTML?: () => void;
  onSaveHTMLWithCSS?: () => void;
  onSaveGrapesJS?: () => void;
}

const EmailToolbar: React.FC<EmailToolbarProps> = ({
  emailData,
  setEmailData,
  showPreview,
  setShowPreview,
  viewMode,
  setViewMode,
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  onSave,
  onShowCode,
  isFullscreen,
  toggleFullscreen,
  leftPanelVisible,
  toggleLeftPanel,
  rightPanelVisible,
  toggleRightPanel,
  onShowAssetUpload,
  onSaveHTML,
  onSaveHTMLWithCSS,
  onSaveGrapesJS,
}) => {
  // State cho dropdown menu
  const [showSaveMenu, setShowSaveMenu] = useState(false);
  const saveMenuRef = useRef<HTMLDivElement>(null);

  // Xử lý click bên ngoài để đóng dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (saveMenuRef.current && !saveMenuRef.current.contains(event.target as Node)) {
        setShowSaveMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  return (
    <div className="border-b p-2 flex flex-col gap-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={toggleLeftPanel}
            title={leftPanelVisible ? "Ẩn panel trái" : "Hiện panel trái"}
          >
            <LayoutPanelLeft size={iconSize} />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={toggleRightPanel}
            title={rightPanelVisible ? "Ẩn panel phải" : "Hiện panel phải"}
          >
            <LayoutPanelRight size={iconSize} />
          </Button>

          <div className="h-6 border-l mx-1"></div>

          <Button
            variant="outline"
            size="sm"
            onClick={onUndo}
            disabled={!canUndo}
            title="Hoàn tác"
          >
            <Undo2 size={iconSize} />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onRedo}
            disabled={!canRedo}
            title="Làm lại"
          >
            <Redo2 size={iconSize} />
          </Button>

          <div className="h-6 border-l mx-1"></div>
          <div>
            <Input
              id="email-name"
              placeholder="Tên email"
              value={emailData.name}
              onChange={(e) => setEmailData({ ...emailData, name: e.target.value })}

              className="h-8 text-sm"
            />
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode('desktop')}
            className={viewMode === 'desktop' ? 'bg-gray-100' : ''}
            title="Chế độ desktop"
          >
            <Monitor size={iconSize} />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode('tablet')}
            className={viewMode === 'tablet' ? 'bg-gray-100' : ''}
            title="Chế độ tablet"
          >
            <Tablet size={iconSize} />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode('mobile')}
            className={viewMode === 'mobile' ? 'bg-gray-100' : ''}
            title="Chế độ mobile"
          >
            <Smartphone size={iconSize} />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onShowCode}
            title="Xem mã HTML"
          >
            <FileCode size={iconSize} />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowPreview(!showPreview)}
            title={showPreview ? "Tắt xem trước" : "Xem trước"}
          >
            {showPreview ? <EyeOff size={iconSize} /> : <Eye size={iconSize} />}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={toggleFullscreen}
            title={isFullscreen ? "Thoát toàn màn hình" : "Toàn màn hình"}
          >
            {isFullscreen ? <Minimize2 size={iconSize} /> : <Maximize2 size={iconSize} />}
          </Button>

          <div className="relative" ref={saveMenuRef}>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSaveMenu(!showSaveMenu)}
              title="Lưu email"
              className="flex items-center"
            >
              <span className="mr-1"><Save size={iconSize} /></span>
              Lưu
              <span className="ml-1"><ChevronDown size={iconSize} /></span>
            </Button>

            {showSaveMenu && (
              <div className="absolute right-0 mt-1 w-56 bg-white border rounded-md shadow-lg z-10">
                <ul className="py-1">
                  <li className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center" onClick={() => {
                    if (onSave) onSave();
                    setShowSaveMenu(false);
                  }}>
                    <Save size={iconSize} className="mr-2" />
                    <span>Lưu</span>
                  </li>

                  <li className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center" onClick={() => {
                    if (onSaveHTML) onSaveHTML();
                    setShowSaveMenu(false);
                  }}>
                    <Code size={iconSize} className="mr-2" />
                    <span>Lưu mã HTML</span>
                  </li>

                  <li className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center" onClick={() => {
                    if (onSaveHTMLWithCSS) onSaveHTMLWithCSS();
                    setShowSaveMenu(false);
                  }}>
                    <FileCode size={iconSize} className="mr-2" />
                    <span>Lưu HTML+CSS</span>
                  </li>

                  <li className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center" onClick={() => {
                    if (onSaveGrapesJS) onSaveGrapesJS();
                    setShowSaveMenu(false);
                  }}>
                    <Download size={iconSize} className="mr-2" />
                    <span>Lưu file .grapesjs</span>
                  </li>
                </ul>
              </div>
            )}
          </div>

          {onShowAssetUpload && (
            <Button
              variant="outline"
              size="sm"
              onClick={onShowAssetUpload}
              title="Tải lên tài nguyên"
            >
              <span className="mr-1"><Upload size={iconSize} /></span>
              Tải lên
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmailToolbar;
