import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum cho trạng thái kho
 */
export enum WarehouseStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Interface cho thông tin địa chỉ kho
 */
export interface WarehouseAddress {
  street?: string;
  district?: string;
  city?: string;
  province?: string;
  country?: string;
  postalCode?: string;
}

/**
 * Interface cho thông tin liên hệ kho
 */
export interface WarehouseContact {
  name?: string;
  phone?: string;
  email?: string;
  position?: string;
}

/**
 * Interface cho thông tin kho
 */
export interface WarehouseDto {
  id: number;
  name: string;
  code: string;
  description?: string;
  address?: WarehouseAddress;
  contact?: WarehouseContact;
  status: WarehouseStatus;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho danh sách kho
 */
export interface WarehouseListItemDto {
  id: number;
  name: string;
  code: string;
  description?: string;
  status: WarehouseStatus;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho chi tiết kho
 */
export interface WarehouseDetailDto extends WarehouseDto {
  customFields?: WarehouseCustomFieldDto[];
}

/**
 * Interface cho trường tùy chỉnh của kho
 */
export interface WarehouseCustomFieldDto {
  id: number;
  warehouseId: number;
  name: string;
  type: string;
  value: unknown;
  required: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho tham số truy vấn kho
 */
export interface WarehouseQueryParams extends QueryDto {
  status?: WarehouseStatus;
}

/**
 * Interface cho dữ liệu tạo kho
 */
export interface CreateWarehouseDto {
  name: string;
  code: string;
  description?: string;
  address?: WarehouseAddress;
  contact?: WarehouseContact;
  status?: WarehouseStatus;
}

/**
 * Interface cho dữ liệu cập nhật kho
 */
export interface UpdateWarehouseDto {
  name?: string;
  code?: string;
  description?: string;
  address?: WarehouseAddress;
  contact?: WarehouseContact;
  status?: WarehouseStatus;
}

/**
 * Interface cho dữ liệu tạo trường tùy chỉnh của kho
 */
export interface CreateWarehouseCustomFieldDto {
  name: string;
  type: string;
  value: unknown;
  required?: boolean;
  order?: number;
}

/**
 * Interface cho dữ liệu cập nhật trường tùy chỉnh của kho
 */
export interface UpdateWarehouseCustomFieldDto {
  name?: string;
  type?: string;
  value?: unknown;
  required?: boolean;
  order?: number;
}
