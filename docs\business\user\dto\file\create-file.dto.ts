import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo file mới
 */
export class CreateFileDto {
  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  @Type(() => Number)
  userId?: number;

  /**
   * Tên file
   * @example "Tài liệu hướng dẫn.pdf"
   */
  @ApiProperty({
    description: 'Tên file',
    example: 'Tài liệu hướng dẫn.pdf',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Tên file không được để trống' })
  @IsString({ message: 'Tên file phải là chuỗi' })
  @MaxLength(255, { message: 'Tên file không được vượt quá 255 ký tự' })
  name: string;

  /**
   * ID kho ảo chứa file
   * @example 1
   */
  @ApiProperty({
    description: 'ID kho ảo chứa file',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID kho ảo phải là số' })
  @Type(() => Number)
  warehouseId?: number;

  /**
   * ID thư mục chứa file
   * @example 1
   */
  @ApiProperty({
    description: 'ID thư mục chứa file',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID thư mục phải là số' })
  @Type(() => Number)
  folderId?: number | null;

  /**
   * Kích thước file (byte)
   * @example 1024000
   */
  @ApiProperty({
    description: 'Kích thước file (byte)',
    example: 1024000,
  })
  @IsNotEmpty({ message: 'Kích thước file không được để trống' })
  @IsNumber({}, { message: 'Kích thước file phải là số' })
  @Type(() => Number)
  size: number;
}
