import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Folder, VirtualWarehouse } from '@modules/business/entities'; // Using user's path for Folder and VirtualWarehouse
import { User } from '@modules/user/entities';
import { PaginatedResult } from '@common/response'; // Using user's path for PaginatedResult
import { SortDirection } from '@common/dto/query.dto'; // From admin file

/**
 * Repository xử lý truy vấn dữ liệu cho entity Folder,
 * kết hợp chức năng từ cả user và admin context.
 */
@Injectable()
export class FolderRepository extends Repository<Folder> {
  private readonly logger = new Logger(FolderRepository.name);

  constructor(private readonly dataSource: DataSource) { // Using user's constructor style (readonly)
    super(Folder, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho Folder
   * @returns Query builder
   */
  private createBaseQuery(): SelectQueryBuilder<Folder> {
    return this.createQueryBuilder('folder');
  }

  // --- User specific or unique methods ---

  /**
   * Tạo thư mục mới (User context)
   * @param folder Thông tin thư mục
   * @returns Thư mục đã tạo
   */
  async createFolder(folder: Folder): Promise<Folder> {
    try {
      return await this.save(folder);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo thư mục: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo thư mục: ${error.message}`);
    }
  }

  /**
   * Tìm thư mục theo ID với thông tin chi tiết về thư mục cha, người dùng và kho ảo (User context)
   * @param id ID của thư mục
   * @returns Thông tin chi tiết của thư mục hoặc null nếu không tìm thấy
   */
  async findByIdWithDetails(id: number): Promise<{
    id: number;
    name: string;
    parentId: number | null;
    userId: number;
    path: string | null;
    root: number | null;
    createdAt: number;
    updatedAt: number;
    parentFolder: Folder | null;
    user: User | null;
    virtualWarehouse: VirtualWarehouse | null;
  } | null> {
    try {
      const folder = await this.createBaseQuery()
        .where('folder.id = :id', { id })
        .getOne();

      if (!folder) {
        return null;
      }

      let parentFolder: Folder | null = null;
      if (folder.parentId) {
        parentFolder = await this.createBaseQuery()
          .where('folder.id = :parentId', { parentId: folder.parentId })
          .getOne();
      }

      const user: User | null = await this.dataSource
        .getRepository(User)
        .createQueryBuilder('user')
        .select(['user.id', 'user.fullName', 'user.email', 'user.phoneNumber'])
        .where('user.id = :userId', { userId: folder.userId })
        .getOne();

      let virtualWarehouse: VirtualWarehouse | null = null;
      if (folder.root) {
        virtualWarehouse = await this.dataSource
          .getRepository(VirtualWarehouse)
          .createQueryBuilder('vw')
          .select(['vw.warehouseId', 'vw.associatedSystem', 'vw.purpose'])
          .where('vw.warehouseId = :warehouseId', { warehouseId: folder.root })
          .getOne();
      }

      return {
        ...folder,
        parentFolder,
        user,
        virtualWarehouse
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm thư mục chi tiết theo ID ${id}: ${error.message}`,
        error.stack,
      );
      throw new Error(`Lỗi khi tìm thư mục chi tiết theo ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm thư mục theo ID người dùng (User context)
   * @param userId ID của người dùng
   * @returns Danh sách thư mục của người dùng
   */
  async findByUserId(userId: number): Promise<Folder[]> {
    try {
      return await this.createBaseQuery()
        .where('folder.userId = :userId', { userId })
        .getMany();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm thư mục theo ID người dùng ${userId}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Lỗi khi tìm thư mục theo ID người dùng ${userId}: ${error.message}`,
      );
    }
  }

  /**
   * Tìm thư mục theo ID thư mục cha (User context)
   * @param parentId ID của thư mục cha
   * @returns Danh sách thư mục con
   */
  async findByParentId(parentId: number): Promise<Folder[]> {
    try {
      return await this.createBaseQuery()
        .where('folder.parentId = :parentId', { parentId })
        .getMany();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm thư mục theo ID thư mục cha ${parentId}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Lỗi khi tìm thư mục theo ID thư mục cha ${parentId}: ${error.message}`,
      );
    }
  }

  /**
   * Tìm thư mục gốc (không có thư mục cha) theo ID người dùng (User context)
   * @param userId ID của người dùng
   * @returns Danh sách thư mục gốc
   */
  async findRootFoldersByUserId(userId: number): Promise<Folder[]> {
    try {
      return await this.createBaseQuery()
        .where('folder.userId = :userId', { userId })
        .andWhere('folder.parentId IS NULL')
        .getMany();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm thư mục gốc theo ID người dùng ${userId}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Lỗi khi tìm thư mục gốc theo ID người dùng ${userId}: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thư mục (User context)
   * @param id ID của thư mục
   * @param updateData Dữ liệu cập nhật
   * @returns Thư mục đã cập nhật
   */
  async updateFolder(id: number, updateData: Partial<Folder>): Promise<Folder> {
    try {
      await this.update({ id }, updateData);
      const updatedFolder = await this.findById_user(id); // Call the user-specific findById
      if (!updatedFolder) {
        throw new Error(`Không tìm thấy thư mục với ID ${id} sau khi cập nhật`);
      }
      return updatedFolder;
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật thư mục với ID ${id}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Lỗi khi cập nhật thư mục với ID ${id}: ${error.message}`,
      );
    }
  }

  /**
   * Xóa thư mục (User context)
   * @param id ID của thư mục
   * @returns Kết quả xóa
   */
  async deleteFolder(id: number): Promise<boolean> {
    try {
      const result = await this.delete({ id });
      return (
        result.affected !== undefined &&
        result.affected !== null &&
        result.affected > 0
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa thư mục với ID ${id}: ${error.message}`,
        error.stack,
      );
      throw new Error(`Lỗi khi xóa thư mục với ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm thư mục theo ID thư mục cha và tên (User context)
   * @param parentId ID của thư mục cha (có thể null nếu là thư mục gốc)
   * @param name Tên thư mục cần tìm
   * @param userId ID của người dùng sở hữu
   * @returns Thư mục nếu tìm thấy, null nếu không tìm thấy
   */
  async findByParentIdAndName(parentId: number | null, name: string, userId: number): Promise<Folder | null> {
    try {
      const queryBuilder = this.createBaseQuery()
        .where('folder.name = :name', { name })
        .andWhere('folder.userId = :userId', { userId });

      if (parentId === null) {
        queryBuilder.andWhere('folder.parentId IS NULL');
      } else {
        queryBuilder.andWhere('folder.parentId = :parentId', { parentId });
      }

      return await queryBuilder.getOne();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm thư mục theo ID thư mục cha ${parentId} và tên ${name}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Lỗi khi tìm thư mục theo ID thư mục cha ${parentId} và tên ${name}: ${error.message}`,
      );
    }
  }

  // --- Methods with same name, differentiated by suffix ---

  /**
   * Tìm thư mục theo ID (User context version)
   * @param id ID của thư mục
   * @returns Thông tin thư mục hoặc null nếu không tìm thấy
   */
  async findById_user(id: number): Promise<Folder | null> {
    try {
      return await this.createBaseQuery()
        .where('folder.id = :id', { id })
        .getOne();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm thư mục theo ID ${id} (user): ${error.message}`,
        error.stack,
      );
      throw new Error(`Lỗi khi tìm thư mục theo ID ${id} (user): ${error.message}`);
    }
  }

  /**
   * Tìm thư mục theo ID (Admin context version)
   * @param id ID của thư mục
   * @returns Folder nếu tìm thấy, null nếu không tìm thấy
   */
  async findById_admin(id: number): Promise<Folder | null> {
    this.logger.log(`(Admin) Tìm thư mục với ID: ${id}`);
    // Admin version did not have try-catch, preserving that behavior
    return this.createBaseQuery()
      .where('folder.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm kiếm thư mục với các điều kiện lọc và phân trang (User context version)
   * @param queryParams Tham số truy vấn
   * @returns Danh sách thư mục với phân trang
   */
  async findAll_user(queryParams: any): Promise<PaginatedResult<Folder>> {
    try {
      const {
        page = 1,
        limit = 10,
        offset = (page - 1) * limit,
        search,
        userId,
        parentId,
        sortBy = 'id', // Default sort for user
        sortDirection = 'ASC', // Default direction for user
      } = queryParams;

      const queryBuilder = this.createBaseQuery();

      if (search) {
        queryBuilder.andWhere('folder.name ILIKE :search', {
          search: `%${search}%`,
        });
      }
      if (userId) {
        queryBuilder.andWhere('folder.userId = :userId', { userId });
      }
      if (parentId !== undefined) {
        if (parentId === null) {
          queryBuilder.andWhere('folder.parentId IS NULL');
        } else {
          queryBuilder.andWhere('folder.parentId = :parentId', { parentId });
        }
      }

      const total = await queryBuilder.getCount();

      queryBuilder
        .orderBy(`folder.${sortBy}`, sortDirection as 'ASC' | 'DESC')
        .skip(offset)
        .take(limit);

      const items = await queryBuilder.getMany();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm kiếm thư mục (user): ${error.message}`,
        error.stack,
      );
      throw new Error(`Lỗi khi tìm kiếm thư mục (user): ${error.message}`);
    }
  }

  /**
   * Lấy danh sách thư mục với phân trang và tìm kiếm (Admin context version)
   * @param page Số trang
   * @param limit Số lượng bản ghi trên mỗi trang
   * @param search Từ khóa tìm kiếm
   * @param parentId ID thư mục cha (nếu có)
   * @param userId ID người dùng sở hữu
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách thư mục với phân trang
   */
  async findAll_admin(
    page: number = 1,
    limit: number = 10,
    search?: string,
    parentId?: number, // Note: admin's parentId can be undefined, null, or number
    userId?: number,
    sortBy: string = 'createdAt', // Default sort for admin
    sortDirection: SortDirection = SortDirection.DESC, // Default direction for admin
  ): Promise<PaginatedResult<Folder>> {
    this.logger.log(`(Admin) Lấy danh sách thư mục với page=${page}, limit=${limit}, search=${search}, parentId=${parentId}, userId=${userId}`);
    // Admin version did not have try-catch for the main logic, preserving that
    const queryBuilder = this.createBaseQuery();

    if (search) {
      queryBuilder.andWhere('LOWER(folder.name) LIKE LOWER(:search)', { search: `%${search}%` });
    }
    if (parentId !== undefined) { // Admin's logic for parentId
      if (parentId === null) {
        queryBuilder.andWhere('folder.parentId IS NULL');
      } else {
        queryBuilder.andWhere('folder.parentId = :parentId', { parentId });
      }
    }
    if (userId) {
      queryBuilder.andWhere('folder.userId = :userId', { userId });
    }

    const direction = sortDirection === SortDirection.ASC ? 'ASC' : 'DESC';
    queryBuilder.orderBy(`folder.${sortBy}`, direction);

    const totalItems = await queryBuilder.getCount();

    queryBuilder.skip((page - 1) * limit).take(limit);

    const items = await queryBuilder.getMany();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }
}