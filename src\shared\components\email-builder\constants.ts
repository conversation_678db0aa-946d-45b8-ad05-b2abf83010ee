import { EmailData, EmailTemplate } from './types';

// D<PERSON> liệu mẫu mặc định
export const INITIAL_EMAIL: EmailData = {
  name: '',
  subject: '',
  preheader: '',
};

// Đ<PERSON><PERSON> nghĩa các mẫu email
export const EMAIL_TEMPLATES: EmailTemplate[] = [
  {
    id: 'welcome-email',
    name: '<PERSON>ail chào mừng',
    category: 'Marketing',
    description: 'Template email chào mừng người đăng ký mới',
    elements: [
      {
        id: `header-${Date.now()}`,
        type: 'header',
        content: `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#ffffff" style="padding: 20px 0;">
                <img src="https://via.placeholder.com/200x60?text=Logo" alt="Logo" width="200" style="display: block; max-width: 100%;" />
              </td>
            </tr>
            <tr>
              <td align="center" bgcolor="#f8f9fa" style="padding: 10px 0; border-top: 1px solid #e5e7eb; border-bottom: 1px solid #e5e7eb;">
                <table border="0" cellspacing="0" cellpadding="0">
                  <tr>
                    <td style="padding: 0 15px;"><a href="#" style="color: #333; text-decoration: none; font-size: 14px;">Trang chủ</a></td>
                    <td style="padding: 0 15px;"><a href="#" style="color: #333; text-decoration: none; font-size: 14px;">Sản phẩm</a></td>
                    <td style="padding: 0 15px;"><a href="#" style="color: #333; text-decoration: none; font-size: 14px;">Liên hệ</a></td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        `,
        style: {
          padding: 0,
          width: '100%',
          margin: '0 0 20px 0',
        }
      },
      {
        id: `heading-${Date.now()}`,
        type: 'heading',
        content: 'Chào mừng bạn đến với dịch vụ của chúng tôi!',
        headingType: 'h1',
        style: {
          color: '#111111',
          fontSize: 28,
          fontWeight: 'bold',
          textAlign: 'center',
          paddingTop: 20,
          paddingBottom: 20,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.2,
          fontFamily: 'Arial, sans-serif',
        }
      },
      {
        id: `text-${Date.now()}`,
        type: 'text',
        content: 'Cảm ơn bạn đã đăng ký dịch vụ của chúng tôi. Chúng tôi rất vui mừng được chào đón bạn vào cộng đồng của chúng tôi.',
        style: {
          color: '#333333',
          fontSize: 16,
          textAlign: 'center',
          paddingTop: 8,
          paddingBottom: 16,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.5,
        }
      },
      {
        id: `image-${Date.now()}`,
        type: 'image',
        src: 'https://via.placeholder.com/600x300?text=Welcome+Image',
        alt: 'Welcome Image',
        style: {
          width: '100%',
          maxWidth: '600px',
          height: 'auto',
          margin: '0 auto 20px auto',
          display: 'block',
        }
      },
      {
        id: `button-${Date.now()}`,
        type: 'button',
        text: 'Khám phá ngay',
        url: '#',
        style: {
          backgroundColor: '#0070f3',
          color: '#ffffff',
          padding: 12,
          borderRadius: 4,
          textAlign: 'center',
          fontWeight: 'bold',
          width: '200px',
          margin: '16px auto',
        }
      },
      {
        id: `footer-${Date.now()}`,
        type: 'footer',
        content: `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#f8f9fa" style="padding: 30px 20px; border-top: 1px solid #e5e7eb;">
                <table border="0" cellspacing="0" cellpadding="0" width="100%" style="max-width: 600px;">
                  <tr>
                    <td align="center" style="padding-bottom: 20px; color: #666; font-size: 14px; line-height: 1.5;">
                      Công ty TNHH ABC<br />
                      Địa chỉ: 123 Đường ABC, Quận XYZ, TP. HCM<br />
                      Email: <EMAIL> | Điện thoại: (*************
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="color: #666; font-size: 12px; line-height: 1.5;">
                      © ${new Date().getFullYear()} Công ty ABC. Tất cả các quyền được bảo lưu.<br />
                      <a href="#" style="color: #666; text-decoration: underline;">Chính sách bảo mật</a> |
                      <a href="#" style="color: #666; text-decoration: underline;">Hủy đăng ký</a>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        `,
        style: {
          padding: 0,
          width: '100%',
          margin: '20px 0 0 0',
        }
      }
    ]
  },
  {
    id: 'newsletter',
    name: 'Bản tin định kỳ',
    category: 'Newsletter',
    description: 'Template email bản tin định kỳ với nhiều cột',
    elements: [
      {
        id: `header-${Date.now() + 100}`,
        type: 'header',
        content: `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#ffffff" style="padding: 20px 0;">
                <img src="https://via.placeholder.com/200x60?text=Newsletter" alt="Logo" width="200" style="display: block; max-width: 100%;" />
              </td>
            </tr>
            <tr>
              <td align="center" bgcolor="#0070f3" style="padding: 10px 0; color: #ffffff;">
                <table border="0" cellspacing="0" cellpadding="0">
                  <tr>
                    <td style="padding: 0 15px;"><a href="#" style="color: #ffffff; text-decoration: none; font-size: 14px;">Tin mới</a></td>
                    <td style="padding: 0 15px;"><a href="#" style="color: #ffffff; text-decoration: none; font-size: 14px;">Sự kiện</a></td>
                    <td style="padding: 0 15px;"><a href="#" style="color: #ffffff; text-decoration: none; font-size: 14px;">Khuyến mãi</a></td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        `,
        style: {
          padding: 0,
          width: '100%',
          margin: '0 0 20px 0',
        }
      },
      {
        id: `heading-${Date.now() + 100}`,
        type: 'heading',
        content: 'Bản tin tháng 6/2023',
        headingType: 'h1',
        style: {
          color: '#111111',
          fontSize: 28,
          fontWeight: 'bold',
          textAlign: 'center',
          paddingTop: 20,
          paddingBottom: 20,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.2,
          fontFamily: 'Arial, sans-serif',
        }
      },
      {
        id: `text-${Date.now() + 100}`,
        type: 'text',
        content: 'Chào mừng bạn đến với bản tin tháng 6. Dưới đây là những thông tin mới nhất và quan trọng nhất trong tháng.',
        style: {
          color: '#333333',
          fontSize: 16,
          textAlign: 'center',
          paddingTop: 8,
          paddingBottom: 16,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.5,
        }
      },
      {
        id: `2columns-${Date.now() + 100}`,
        type: '2columns',
        isContainer: true,
        columnCount: 2,
        columnGap: 20,
        columnPadding: 20,
        columnBgColor: '#f5f5f5',
        style: {
          width: '100%',
          padding: 0,
          marginBottom: 20,
        },
        children: [
          {
            id: `column-left-${Date.now() + 100}`,
            type: 'column',
            isContainer: true,
            columnPosition: 'left',
            style: {
              width: '100%',
              backgroundColor: '#f5f5f5',
              padding: 20,
              borderRadius: 4,
            },
            children: [
              {
                id: `heading-left-${Date.now() + 100}`,
                type: 'heading',
                content: 'Tin tức nổi bật',
                headingType: 'h2',
                parent: `column-left-${Date.now() + 100}`,
                style: {
                  color: '#111111',
                  fontSize: 22,
                  fontWeight: 'bold',
                  marginBottom: 16,
                  lineHeight: 1.2,
                }
              },
              {
                id: `image-left-${Date.now() + 100}`,
                type: 'image',
                src: 'https://via.placeholder.com/300x200?text=News',
                alt: 'Tin tức',
                parent: `column-left-${Date.now() + 100}`,
                style: {
                  width: '100%',
                  height: 'auto',
                  marginBottom: 16,
                  display: 'block',
                }
              },
              {
                id: `text-left-${Date.now() + 100}`,
                type: 'text',
                content: 'Sự kiện quan trọng đã diễn ra trong tháng qua. Đây là mô tả ngắn về sự kiện và tác động của nó.',
                parent: `column-left-${Date.now() + 100}`,
                style: {
                  color: '#333333',
                  fontSize: 16,
                  lineHeight: 1.5,
                  marginBottom: 16,
                }
              },
              {
                id: `button-left-${Date.now() + 100}`,
                type: 'button',
                text: 'Xem thêm',
                url: '#',
                parent: `column-left-${Date.now() + 100}`,
                style: {
                  backgroundColor: '#0070f3',
                  color: '#ffffff',
                  padding: 10,
                  borderRadius: 4,
                  textAlign: 'center',
                  fontWeight: 'bold',
                  display: 'inline-block',
                }
              }
            ]
          },
          {
            id: `column-right-${Date.now() + 100}`,
            type: 'column',
            isContainer: true,
            columnPosition: 'right',
            style: {
              width: '100%',
              backgroundColor: '#f5f5f5',
              padding: 20,
              borderRadius: 4,
            },
            children: [
              {
                id: `heading-right-${Date.now() + 100}`,
                type: 'heading',
                content: 'Sự kiện sắp tới',
                headingType: 'h2',
                parent: `column-right-${Date.now() + 100}`,
                style: {
                  color: '#111111',
                  fontSize: 22,
                  fontWeight: 'bold',
                  marginBottom: 16,
                  lineHeight: 1.2,
                }
              },
              {
                id: `image-right-${Date.now() + 100}`,
                type: 'image',
                src: 'https://via.placeholder.com/300x200?text=Events',
                alt: 'Sự kiện',
                parent: `column-right-${Date.now() + 100}`,
                style: {
                  width: '100%',
                  height: 'auto',
                  marginBottom: 16,
                  display: 'block',
                }
              },
              {
                id: `text-right-${Date.now() + 100}`,
                type: 'text',
                content: 'Các sự kiện sắp diễn ra trong tháng tới. Đừng bỏ lỡ những cơ hội tuyệt vời này.',
                parent: `column-right-${Date.now() + 100}`,
                style: {
                  color: '#333333',
                  fontSize: 16,
                  lineHeight: 1.5,
                  marginBottom: 16,
                }
              },
              {
                id: `button-right-${Date.now() + 100}`,
                type: 'button',
                text: 'Đăng ký',
                url: '#',
                parent: `column-right-${Date.now() + 100}`,
                style: {
                  backgroundColor: '#0070f3',
                  color: '#ffffff',
                  padding: 10,
                  borderRadius: 4,
                  textAlign: 'center',
                  fontWeight: 'bold',
                  display: 'inline-block',
                }
              }
            ]
          }
        ]
      },
      {
        id: `footer-${Date.now() + 100}`,
        type: 'footer',
        content: `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#0070f3" style="padding: 30px 20px; color: #ffffff;">
                <table border="0" cellspacing="0" cellpadding="0" width="100%" style="max-width: 600px;">
                  <tr>
                    <td align="center" style="padding-bottom: 20px;">
                      <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=FB" alt="Facebook" width="32" height="32" style="display: block;" /></a>
                          </td>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=TW" alt="Twitter" width="32" height="32" style="display: block;" /></a>
                          </td>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=IG" alt="Instagram" width="32" height="32" style="display: block;" /></a>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="padding-bottom: 20px; color: #ffffff; font-size: 14px; line-height: 1.5;">
                      Công ty TNHH ABC<br />
                      Địa chỉ: 123 Đường ABC, Quận XYZ, TP. HCM<br />
                      Email: <EMAIL> | Điện thoại: (*************
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="color: #ffffff; font-size: 12px; line-height: 1.5;">
                      © ${new Date().getFullYear()} Công ty ABC. Tất cả các quyền được bảo lưu.<br />
                      <a href="#" style="color: #ffffff; text-decoration: underline;">Chính sách bảo mật</a> |
                      <a href="#" style="color: #ffffff; text-decoration: underline;">Hủy đăng ký</a>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        `,
        style: {
          padding: 0,
          width: '100%',
          margin: '20px 0 0 0',
        }
      }
    ]
  },
  {
    id: 'promotional',
    name: 'Email khuyến mãi',
    category: 'Marketing',
    description: 'Template email thông báo khuyến mãi, giảm giá',
    elements: [
      {
        id: `header-${Date.now() + 200}`,
        type: 'header',
        content: `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#ffffff" style="padding: 20px 0;">
                <img src="https://via.placeholder.com/200x60?text=Sale" alt="Logo" width="200" style="display: block; max-width: 100%;" />
              </td>
            </tr>
          </table>
        `,
        style: {
          padding: 0,
          width: '100%',
          margin: '0 0 0 0',
        }
      },
      {
        id: `image-${Date.now() + 200}`,
        type: 'image',
        src: 'https://via.placeholder.com/600x300?text=SALE+50%',
        alt: 'Sale Banner',
        style: {
          width: '100%',
          maxWidth: '600px',
          height: 'auto',
          margin: '0 auto 0 auto',
          display: 'block',
        }
      },
      {
        id: `heading-${Date.now() + 200}`,
        type: 'heading',
        content: 'GIẢM GIÁ LỚN - CHỈ TRONG TUẦN NÀY!',
        headingType: 'h1',
        style: {
          color: '#e63946',
          fontSize: 28,
          fontWeight: 'bold',
          textAlign: 'center',
          paddingTop: 20,
          paddingBottom: 10,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.2,
          fontFamily: 'Arial, sans-serif',
        }
      },
      {
        id: `text-${Date.now() + 200}`,
        type: 'text',
        content: 'Cơ hội cuối cùng để sở hữu sản phẩm với giá ưu đãi nhất. Giảm đến 50% cho tất cả các sản phẩm.',
        style: {
          color: '#333333',
          fontSize: 16,
          textAlign: 'center',
          paddingTop: 8,
          paddingBottom: 16,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.5,
        }
      },
      {
        id: `1column-${Date.now() + 200}`,
        type: '1column',
        isContainer: true,
        columnCount: 1,
        columnPadding: 20,
        columnBgColor: '#f9f9f9',
        style: {
          width: '100%',
          padding: 0,
          marginBottom: 20,
        },
        children: [
          {
            id: `text-col-${Date.now() + 200}`,
            type: 'text',
            content: '<div style="text-align: center; font-size: 24px; font-weight: bold; color: #e63946;">Mã giảm giá: SALE50</div>',
            parent: `1column-${Date.now() + 200}`,
            style: {
              padding: 20,
              backgroundColor: '#f9f9f9',
              borderRadius: 4,
              marginBottom: 20,
            }
          }
        ]
      },
      {
        id: `button-${Date.now() + 200}`,
        type: 'button',
        text: 'MUA NGAY',
        url: '#',
        style: {
          backgroundColor: '#e63946',
          color: '#ffffff',
          padding: 15,
          borderRadius: 4,
          textAlign: 'center',
          fontWeight: 'bold',
          fontSize: 18,
          width: '200px',
          margin: '16px auto 30px auto',
        }
      },
      {
        id: `text-${Date.now() + 201}`,
        type: 'text',
        content: 'Ưu đãi kết thúc vào ngày 30/06/2023. Áp dụng cho tất cả các sản phẩm trên website và tại cửa hàng.',
        style: {
          color: '#666666',
          fontSize: 14,
          textAlign: 'center',
          paddingTop: 0,
          paddingBottom: 20,
          paddingLeft: 16,
          paddingRight: 16,
          lineHeight: 1.5,
        }
      },
      {
        id: `footer-${Date.now() + 200}`,
        type: 'footer',
        content: `
          <table width="100%" border="0" cellspacing="0" cellpadding="0" style="min-width:100%;">
            <tr>
              <td align="center" bgcolor="#333333" style="padding: 30px 20px; color: #ffffff;">
                <table border="0" cellspacing="0" cellpadding="0" width="100%" style="max-width: 600px;">
                  <tr>
                    <td align="center" style="padding-bottom: 20px;">
                      <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=FB" alt="Facebook" width="32" height="32" style="display: block;" /></a>
                          </td>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=TW" alt="Twitter" width="32" height="32" style="display: block;" /></a>
                          </td>
                          <td style="padding: 0 8px;">
                            <a href="#"><img src="https://via.placeholder.com/32x32?text=IG" alt="Instagram" width="32" height="32" style="display: block;" /></a>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  <tr>
                    <td align="center" style="color: #ffffff; font-size: 12px; line-height: 1.5;">
                      © ${new Date().getFullYear()} Công ty ABC. Tất cả các quyền được bảo lưu.<br />
                      <a href="#" style="color: #ffffff; text-decoration: underline;">Chính sách bảo mật</a> |
                      <a href="#" style="color: #ffffff; text-decoration: underline;">Hủy đăng ký</a>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        `,
        style: {
          padding: 0,
          width: '100%',
          margin: '20px 0 0 0',
        }
      }
    ]
  }
];

// Các loại phần tử có thể thêm vào email
export const ELEMENT_TYPES = [
  { type: 'text', label: 'Văn bản', icon: 'Type' },
  { type: 'heading', label: 'Tiêu đề', icon: 'Heading1' },
  { type: 'image', label: 'Hình ảnh', icon: 'Image' },
  { type: 'button', label: 'Nút nhấn', icon: 'Square' },
  { type: 'divider', label: 'Đường kẻ', icon: 'Minus' },
  { type: 'spacer', label: 'Khoảng cách', icon: 'ArrowUpDown' },
  { type: 'list', label: 'Danh sách', icon: 'List' },
  { type: 'link', label: 'Liên kết', icon: 'Link' },
  { type: 'social', label: 'Mạng xã hội', icon: 'Share2' },
  { type: 'video', label: 'Video', icon: 'Video' },
  { type: 'html', label: 'HTML', icon: 'Code' },
  { type: 'header', label: 'Header', icon: 'PanelTop' },
  { type: 'footer', label: 'Footer', icon: 'PanelLeft' },
  { type: '1column', label: 'Cột đơn', icon: 'Layout' },
  { type: '2columns', label: 'Hai cột', icon: 'Columns' },
];

// Dữ liệu mẫu cho assets
export const INITIAL_ASSETS = [
  {
    id: 'asset-1',
    type: 'image',
    src: 'https://via.placeholder.com/800x400?text=Sample+Image+1',
    alt: 'Sample Image 1',
    name: 'Sample Image 1',
    category: 'sample'
  },
  {
    id: 'asset-2',
    type: 'image',
    src: 'https://via.placeholder.com/800x400?text=Sample+Image+2',
    alt: 'Sample Image 2',
    name: 'Sample Image 2',
    category: 'sample'
  },
  {
    id: 'asset-3',
    type: 'image',
    src: 'https://via.placeholder.com/800x400?text=Sample+Image+3',
    alt: 'Sample Image 3',
    name: 'Sample Image 3',
    category: 'sample'
  },
  {
    id: 'asset-4',
    type: 'image',
    src: 'https://via.placeholder.com/800x400?text=Sample+Image+4',
    alt: 'Sample Image 4',
    name: 'Sample Image 4',
    category: 'sample'
  }
];
