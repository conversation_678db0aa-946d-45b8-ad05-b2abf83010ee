import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import { CreatePointDto, PointDto, PointQueryParams, UpdatePointDto } from '../types';
import { apiClient } from '@/shared/api';

/**
 * Service xử lý các API liên quan đến gói point
 */
export const PointService = {
  /**
   * Lấy danh sách gói point với phân trang và lọc
   * @param params Tham số truy vấn
   * @returns Danh sách gói point và thông tin phân trang
   */
  async getPoints(params: PointQueryParams): Promise<ApiResponseDto<PaginatedResult<PointDto>>> {
    return apiClient.get('/admin/r-point/points', { params });
  },

  /**
   * Lấy thông tin chi tiết của một gói point
   * @param id ID của gói point
   * @returns Thông tin chi tiết gói point
   */
  async getPointById(id: number): Promise<ApiResponseDto<PointDto>> {
    return apiClient.get(`/admin/r-point/points/${id}`);
  },

  /**
   * Tạo mới gói point
   * @param data Thông tin gói point cần tạo
   * @returns Gói point đã tạo
   */
  async createPoint(data: CreatePointDto): Promise<ApiResponseDto<PointDto>> {
    console.log('Creating point with data:', JSON.stringify(data, null, 2));
    try {
      const response = await apiClient.post<PointDto>('/admin/r-point/points', data);
      console.log('Create point response:', response);
      return response;
    } catch (error) {
      console.error('Error creating point:', error);
      throw error;
    }
  },

  /**
   * Cập nhật thông tin gói point
   * @param id ID của gói point
   * @param data Thông tin cần cập nhật
   * @returns Gói point đã cập nhật
   */
  async updatePoint(id: number, data: UpdatePointDto): Promise<ApiResponseDto<PointDto>> {
    console.log('Updating point with data:', JSON.stringify(data, null, 2));
    try {
      const response = await apiClient.put<PointDto>(`/admin/r-point/points/${id}`, data);
      console.log('Update point response:', response);
      return response;
    } catch (error) {
      console.error('Error updating point:', error);
      throw error;
    }
  },

  /**
   * Xóa gói point
   * @param id ID của gói point
   * @returns Thông báo xóa thành công
   */
  async deletePoint(id: number): Promise<ApiResponseDto<{ message: string }>> {
    return apiClient.delete(`/admin/r-point/points/${id}`);
  },
};
