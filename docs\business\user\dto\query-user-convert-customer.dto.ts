import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp khách hàng chuyển đổi
 */
export enum UserConvertCustomerSortField {
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  PLATFORM = 'platform',
}

/**
 * DTO cho các tham số truy vấn danh sách khách hàng chuyển đổi
 */
export class QueryUserConvertCustomerDto {
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng bản ghi trên một trang',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm theo tên hoặc số điện thoại',
    example: 'Nguyễn',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Nền tảng nguồn',
    example: 'Facebook',
    required: false,
  })
  @IsOptional()
  @IsString()
  platform?: string;

  @ApiProperty({
    description: 'ID agent hỗ trợ khách hàng',
    example: '550e8400-e29b-41d4-a716-446655440000',
    required: false,
  })
  @IsOptional()
  @IsString()
  agentId?: string;

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: UserConvertCustomerSortField,
    default: UserConvertCustomerSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserConvertCustomerSortField)
  sortBy?: UserConvertCustomerSortField = UserConvertCustomerSortField.CREATED_AT;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
