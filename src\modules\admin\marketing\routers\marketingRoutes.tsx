import { Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import { Loading } from '@/shared/components/common';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { lazy } from 'react';
const CustomFieldsPage = lazy(() => import('../pages/CustomFieldsPage'));
const MarketingPage = lazy(() => import('../pages/MarketingPage'));

export const marketingAdminRoutes: RouteObject[] = [
  {
    path: '/admin/marketing',
    element: (
      <AdminLayout title="Quản lý marketing">
        <Suspense fallback={<Loading />}>
          <MarketingPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/marketing/custom-fields',
    element: (
      <AdminLayout title="Tạo trường tùy chỉnh">
        <Suspense fallback={<Loading />}>
          <CustomFieldsPage />
        </Suspense>
      </AdminLayout>
    ),
  },
];
