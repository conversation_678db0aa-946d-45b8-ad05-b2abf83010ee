import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Typography,
  Select,
} from '@/shared/components/common';
import { Product, ProductCategory } from '@/modules/admin/marketplace/types/product.types';

export interface ProductFormProps {
  initialValues?: Partial<Product>;
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  readOnly?: boolean;
}

/**
 * Form component for creating or editing products
 */
const ProductForm: React.FC<ProductFormProps> = ({
  initialValues,
  onSubmit,
  onCancel,
  readOnly = false,
}) => {
  const { t } = useTranslation();

  // Form ref
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const formRef = React.useRef<any>(null);

  // Handle form submission
  const handleSubmit = (values: Record<string, unknown>) => {
    onSubmit(values);
  };

  // Danh sách các loại sản phẩm
  const categoryOptions = Object.values(ProductCategory).map(category => ({
    value: category,
    label: t(`admin.marketplace.product.category.${category}`, category),
  }));

  return (
    <div className="p-6">
      <Typography variant="h6" className="mb-4">
        {readOnly
          ? t('admin.marketplace.product.details', 'Chi tiết sản phẩm')
          : initialValues?.id
            ? t('admin.marketplace.product.edit', 'Chỉnh sửa sản phẩm')
            : t('admin.marketplace.product.addNew', 'Thêm sản phẩm mới')}
      </Typography>

      <Form
        ref={formRef}
        defaultValues={initialValues}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        className="space-y-4"
      >
        <FormItem
          name="name"
          label={t('admin.marketplace.product.form.name', 'Tên sản phẩm')}
          required
        >
          <Input fullWidth readOnly={readOnly} />
        </FormItem>

        <FormItem
          name="description"
          label={t('admin.marketplace.product.form.description', 'Mô tả')}
        >
          <Textarea rows={5} readOnly={readOnly} />
        </FormItem>

        <FormItem
          name="category"
          label={t('admin.marketplace.product.form.category', 'Loại sản phẩm')}
          required
        >
          {readOnly ? (
            <Input
              fullWidth
              readOnly
              value={
                initialValues?.category
                  ? t(
                      `admin.marketplace.product.category.${initialValues.category}`,
                      initialValues.category
                    )
                  : ''
              }
            />
          ) : (
            <Select options={categoryOptions} />
          )}
        </FormItem>

        <FormItem
          name="price"
          label={t('admin.marketplace.product.form.price', 'Giá gốc')}
          required
        >
          <Input type="number" min={0} fullWidth readOnly={readOnly} />
        </FormItem>

        <FormItem
          name="discountedPrice"
          label={t('admin.marketplace.product.form.discountedPrice', 'Giá khuyến mãi')}
          required
        >
          <Input type="number" min={0} fullWidth readOnly={readOnly} />
        </FormItem>

        {!readOnly && (
          <div className="text-xs text-gray-500 -mt-3 mb-3">
            {t(
              'admin.marketplace.product.form.priceDescription',
              'Giá được tính bằng điểm (points)'
            )}
          </div>
        )}

        {readOnly && initialValues?.status && (
          <FormItem name="status" label={t('admin.marketplace.product.form.status', 'Trạng thái')}>
            <Input
              fullWidth
              readOnly
              value={t(
                `admin.marketplace.product.status.${initialValues.status}`,
                initialValues.status
              )}
            />
          </FormItem>
        )}

        {readOnly && initialValues?.seller && (
          <FormItem name="seller" label={t('admin.marketplace.product.form.seller', 'Người bán')}>
            <Input fullWidth readOnly value={initialValues.seller.name} />
          </FormItem>
        )}

        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={onCancel}>
            {readOnly ? t('common.close', 'Đóng') : t('common.cancel', 'Hủy')}
          </Button>
          {!readOnly && (
            <Button variant="primary" type="submit">
              {t('common.save', 'Lưu')}
            </Button>
          )}
        </div>
      </Form>
    </div>
  );
};

export default ProductForm;
