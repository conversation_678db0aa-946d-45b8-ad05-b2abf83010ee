import { apiClient } from '@/shared/api/axios';
import { BaseQueryParams, IntegrationResult, IntegrationToolDetail, IntegrationToolListItem, IntegrateFromOpenApiParams, UpdateBaseUrlParams, UpdateToolAuthParams } from '../types';
import { PaginatedResult } from '../types/common.types';

/**
 * Service xử lý các API liên quan đến tích hợp tool
 */
export class ToolIntegrationService {
  private baseUrl = '/user/tools/integration';

  /**
   * Tích hợp công cụ từ đặc tả OpenAPI
   * @param params Tham số tích hợp
   * @returns Kết quả tích hợp
   */
  async integrateFromOpenApi(params: IntegrateFromOpenApiParams): Promise<IntegrationResult> {
    try {
      const response = await apiClient.post<IntegrationResult>(
        `${this.baseUrl}/openapi`,
        params,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error('Error integrating from OpenAPI:', error);
      throw error;
    }
  }

  /**
   * L<PERSON>y danh sách công cụ tùy chỉnh
   * @param params Tham số truy vấn
   * @returns Danh sách công cụ tùy chỉnh với phân trang
   */
  async getCustomTools(params: BaseQueryParams): Promise<PaginatedResult<IntegrationToolListItem>> {
    try {
      const response = await apiClient.get<PaginatedResult<IntegrationToolListItem>>(
        `${this.baseUrl}/tools`,
        {
          params,
          tokenType: 'user',
        }
      );
      return response.result;
    } catch (error) {
      console.error('Error fetching custom tools:', error);
      throw error;
    }
  }

  /**
   * Lấy chi tiết công cụ tùy chỉnh
   * @param id ID của công cụ
   * @returns Chi tiết công cụ tùy chỉnh
   */
  async getCustomToolDetail(id: string): Promise<IntegrationToolDetail> {
    try {
      const response = await apiClient.get<IntegrationToolDetail>(
        `${this.baseUrl}/tools/${id}`,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error(`Error fetching custom tool ${id}:`, error);
      throw error;
    }
  }

  /**
   * Cập nhật xác thực cho công cụ tùy chỉnh
   * @param params Tham số cập nhật
   * @returns Kết quả cập nhật
   */
  async updateCustomToolAuth(params: UpdateToolAuthParams): Promise<IntegrationToolDetail> {
    try {
      const response = await apiClient.patch<IntegrationToolDetail>(
        `${this.baseUrl}/tools/auth`,
        params,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error('Error updating custom tool auth:', error);
      throw error;
    }
  }

  /**
   * Xóa công cụ tùy chỉnh
   * @param id ID của công cụ
   * @returns Kết quả xóa
   */
  async deleteCustomTool(id: string): Promise<void> {
    try {
      await apiClient.delete(
        `${this.baseUrl}/tools/${id}`,
        { tokenType: 'user' }
      );
    } catch (error) {
      console.error(`Error deleting custom tool ${id}:`, error);
      throw error;
    }
  }

  /**
   * Cập nhật base URL cho công cụ tùy chỉnh
   * @param params Tham số cập nhật
   * @returns Kết quả cập nhật
   */
  async updateBaseUrl(params: UpdateBaseUrlParams): Promise<IntegrationToolDetail> {
    try {
      const response = await apiClient.patch<IntegrationToolDetail>(
        `${this.baseUrl}/tools/base-url`,
        params,
        { tokenType: 'user' }
      );
      return response.result;
    } catch (error) {
      console.error('Error updating base URL:', error);
      throw error;
    }
  }
}

export const toolIntegrationService = new ToolIntegrationService();
