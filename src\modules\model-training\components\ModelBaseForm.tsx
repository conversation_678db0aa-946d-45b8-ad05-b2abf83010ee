import React from 'react';
import {
  FormItem,
  Select,
  Input,
  Card,
  Typo<PERSON>,
  Button,
  Toggle,
} from '@/shared/components/common';
import { useProviders } from '../hooks/useProviderQuery';
import { useCreateModelBase } from '../hooks/useModelBaseQuery';
import { CreateModelBaseSchema } from '../schemas/model-base.schema';
import { CreateModelBaseDto, ModelBaseConfig } from '../types/model-base.types';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

interface ModelBaseFormProps {
  onSuccess?: () => void;
}

const ModelBaseForm: React.FC<ModelBaseFormProps> = ({ onSuccess }) => {
  const { t } = useTranslation();
  const { data: providersData } = useProviders();
  const createModelBase = useCreateModelBase();

  // Khởi tạo form với React Hook Form và Zod
  const methods = useForm<CreateModelBaseDto>({
    resolver: zodResolver(CreateModelBaseSchema),
    defaultValues: {
      name: '',
      providerId: '',
      baseInputRate: 0,
      baseOutputRate: 0,
      baseTrainRate: 0,
      fineTuningInputRate: 0,
      fineTuningOutputRate: 0,
      fineTuningTrainRate: 0,
      config: {
        hasTopP: false,
        hasTopK: false,
        hasFunction: false,
        hasTemperature: false,
        hasText: false,
        hasImage: false,
        hasAudio: false,
        hasVideo: false,
        hasParallelToolCall: false,
      },
    },
  });

  const { formState, setValue, watch } = methods;
  const { errors } = formState;
  const selectedProviderId = watch('providerId');

  // Chuyển đổi danh sách nhà cung cấp thành options cho Select
  const providerOptions = providersData?.items.map(provider => ({
    value: provider.id || '',
    label: provider.name,
  })) || [];

  // Tạo danh sách các tùy chọn cho Model Base Id
  const modelBaseIdOptions = [
    { value: 'gpt-4', label: 'GPT-4' },
    { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
    { value: 'claude-3-opus', label: 'Claude 3 Opus' },
    { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet' },
    { value: 'claude-3-haiku', label: 'Claude 3 Haiku' },
    { value: 'gemini-pro', label: 'Gemini Pro' },
    { value: 'gemini-ultra', label: 'Gemini Ultra' },
    { value: 'llama-3-70b', label: 'Llama 3 70B' },
    { value: 'llama-3-8b', label: 'Llama 3 8B' },
    { value: 'mistral-large', label: 'Mistral Large' },
    { value: 'mistral-medium', label: 'Mistral Medium' },
    { value: 'mistral-small', label: 'Mistral Small' },
  ];

  const selectedModelBaseId = watch('name');

  // Xử lý submit form
  const onSubmit = (data: CreateModelBaseDto) => {
    createModelBase.mutate(data, {
      onSuccess: () => {
        if (onSuccess) {
          onSuccess();
        }
      },
    });
  };

  // Xử lý thay đổi toggle
  const handleToggleChange = (key: keyof ModelBaseConfig, checked: boolean) => {
    setValue(`config.${key}` as `config.${keyof ModelBaseConfig}`, checked, { shouldValidate: true });
  };

  // Tạo form HTML thông thường thay vì sử dụng component Form
  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = methods.getValues();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleFormSubmit}>
      <Card className="mb-4">
        <Typography variant="h6" className="mb-4">
          {t('Tạo Model Base')}
        </Typography>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <FormItem
            name="providerId"
            label={t('Chọn nhà cung cấp')}
            helpText={errors.providerId?.message}
            required
          >
            <Select
              options={providerOptions}
              placeholder={t('Chọn nhà cung cấp')}
              value={selectedProviderId}
              onChange={(value) => setValue('providerId', value as string, { shouldValidate: true })}
              error={errors.providerId?.message as string}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="name"
            label={t('Model Base Id')}
            helpText={errors.name?.message}
            required
          >
            <Select
              options={modelBaseIdOptions}
              placeholder={t('Chọn model base')}
              value={selectedModelBaseId}
              onChange={(value) => setValue('name', value as string, { shouldValidate: true })}
              error={errors.name?.message as string}
              fullWidth
            />
          </FormItem>
        </div>

        <Typography variant="h6" className="mb-4">
          {t('Pricing')}
        </Typography>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <Typography variant="subtitle1" className="mb-2">
              {t('Input')}
            </Typography>
            <div className="space-y-4">
              <FormItem
                name="baseInputRate"
                label={t('Base')}
                helpText={errors.baseInputRate?.message}
              >
                <Input
                  type="number"
                  placeholder="0"
                  value={watch('baseInputRate')}
                  onChange={(e) => setValue('baseInputRate', Number(e.target.value), { shouldValidate: true })}
                  error={errors.baseInputRate?.message as string}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="fineTuningInputRate"
                label={t('Fine-tuning')}
                helpText={errors.fineTuningInputRate?.message}
              >
                <Input
                  type="number"
                  placeholder="0"
                  value={watch('fineTuningInputRate')}
                  onChange={(e) => setValue('fineTuningInputRate', Number(e.target.value), { shouldValidate: true })}
                  error={errors.fineTuningInputRate?.message as string}
                  fullWidth
                />
              </FormItem>
            </div>
          </div>

          <div>
            <Typography variant="subtitle1" className="mb-2">
              {t('Output')}
            </Typography>
            <div className="space-y-4">
              <FormItem
                name="baseOutputRate"
                label={t('Base')}
                helpText={errors.baseOutputRate?.message}
              >
                <Input
                  type="number"
                  placeholder="0"
                  value={watch('baseOutputRate')}
                  onChange={(e) => setValue('baseOutputRate', Number(e.target.value), { shouldValidate: true })}
                  error={errors.baseOutputRate?.message as string}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="fineTuningOutputRate"
                label={t('Fine-tuning')}
                helpText={errors.fineTuningOutputRate?.message}
              >
                <Input
                  type="number"
                  placeholder="0"
                  value={watch('fineTuningOutputRate')}
                  onChange={(e) => setValue('fineTuningOutputRate', Number(e.target.value), { shouldValidate: true })}
                  error={errors.fineTuningOutputRate?.message as string}
                  fullWidth
                />
              </FormItem>
            </div>
          </div>

          <div>
            <Typography variant="subtitle1" className="mb-2">
              {t('Train')}
            </Typography>
            <div className="space-y-4">
              <FormItem
                name="baseTrainRate"
                label={t('Base')}
                helpText={errors.baseTrainRate?.message}
              >
                <Input
                  type="number"
                  placeholder="0"
                  value={watch('baseTrainRate')}
                  onChange={(e) => setValue('baseTrainRate', Number(e.target.value), { shouldValidate: true })}
                  error={errors.baseTrainRate?.message as string}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="fineTuningTrainRate"
                label={t('Fine-tuning')}
                helpText={errors.fineTuningTrainRate?.message}
              >
                <Input
                  type="number"
                  placeholder="0"
                  value={watch('fineTuningTrainRate')}
                  onChange={(e) => setValue('fineTuningTrainRate', Number(e.target.value), { shouldValidate: true })}
                  error={errors.fineTuningTrainRate?.message as string}
                  fullWidth
                />
              </FormItem>
            </div>
          </div>
        </div>

        <Typography variant="h6" className="mb-4">
          {t('Config')}
        </Typography>

        <div className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-[150px]">
            <Toggle
              label={t('Top P')}
              checked={watch('config.hasTopP')}
              onChange={(checked) => handleToggleChange('hasTopP', checked)}
            />
          </div>
          <div className="flex-1 min-w-[150px]">
            <Toggle
              label={t('Top K')}
              checked={watch('config.hasTopK')}
              onChange={(checked) => handleToggleChange('hasTopK', checked)}
            />
          </div>
          <div className="flex-1 min-w-[150px]">
            <Toggle
              label={t('Tool')}
              checked={watch('config.hasFunction')}
              onChange={(checked) => handleToggleChange('hasFunction', checked)}
            />
          </div>
          <div className="flex-1 min-w-[150px]">
            <Toggle
              label={t('Temperature')}
              checked={watch('config.hasTemperature')}
              onChange={(checked) => handleToggleChange('hasTemperature', checked)}
            />
          </div>
          <div className="flex-1 min-w-[150px]">
            <Toggle
              label={t('Text')}
              checked={watch('config.hasText')}
              onChange={(checked) => handleToggleChange('hasText', checked)}
            />
          </div>
        </div>

        <div className="flex flex-wrap gap-4 mt-4">
          <div className="flex-1 min-w-[150px]">
            <Toggle
              label={t('Image')}
              checked={watch('config.hasImage')}
              onChange={(checked) => handleToggleChange('hasImage', checked)}
            />
          </div>
          <div className="flex-1 min-w-[150px]">
            <Toggle
              label={t('Audio')}
              checked={watch('config.hasAudio')}
              onChange={(checked) => handleToggleChange('hasAudio', checked)}
            />
          </div>
          <div className="flex-1 min-w-[150px]">
            <Toggle
              label={t('Video')}
              checked={watch('config.hasVideo')}
              onChange={(checked) => handleToggleChange('hasVideo', checked)}
            />
          </div>
          <div className="flex-1 min-w-[150px]">
            <Toggle
              label={t('Tool call')}
              checked={watch('config.hasParallelToolCall')}
              onChange={(checked) => handleToggleChange('hasParallelToolCall', checked)}
            />
          </div>
          <div className="flex-1 min-w-[150px]">
            {/* Phần tử trống để giữ cân đối */}
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <Button type="submit" isLoading={createModelBase.isPending}>
            {t('Tạo Model Base')}
          </Button>
        </div>
      </Card>
    </form>
  );
};

export default ModelBaseForm;
