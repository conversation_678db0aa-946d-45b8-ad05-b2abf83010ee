import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { Inventory } from '@modules/business/entities';
import { InventoryRepository} from '@modules/business/repositories';
import { ValidationHelper } from '../helpers/validation.helper';
import {
  CreateInventoryDto,
  UpdateInventoryDto,
  QueryInventoryDto,
  InventoryResponseDto,
} from '../dto/inventory';
import { PaginatedResult } from '@/common/response';

/**
 * Service xử lý logic nghiệp vụ cho tồn kho của người dùng
 */
@Injectable()
export class UserInventoryService {
  private readonly logger = new Logger(UserInventoryService.name);

  constructor(
    private readonly inventoryRepository: InventoryRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Tạo mới bản ghi tồn kho
   * @param createDto DTO chứa thông tin tạo tồn kho mới
   * @returns Thông tin tồn kho đã tạo
   */
  @Transactional()
  async createInventory(createDto: CreateInventoryDto): Promise<InventoryResponseDto> {
    try {
      // Kiểm tra tính hợp lệ của dữ liệu
      await this.validationHelper.validateCreateInventory(createDto);

      // Tạo đối tượng Inventory mới và tự động tính toán các số lượng
      const newInventory = new Inventory();
      newInventory.productId = createDto.productId;
      newInventory.warehouseId = createDto.warehouseId;
      newInventory.availableQuantity = createDto.availableQuantity || 0;
      newInventory.reservedQuantity = createDto.reservedQuantity || 0;
      newInventory.defectiveQuantity = createDto.defectiveQuantity || 0;

      // Tự động tính toán số lượng hiện tại và tổng số lượng
      newInventory.currentQuantity = newInventory.availableQuantity + newInventory.reservedQuantity + newInventory.defectiveQuantity;
      newInventory.totalQuantity = newInventory.currentQuantity;
      newInventory.lastUpdated = Date.now();

      // Lưu vào cơ sở dữ liệu
      const savedInventory = await this.inventoryRepository.createInventory(newInventory);

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(InventoryResponseDto, savedInventory, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tạo tồn kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
        `Lỗi khi tạo tồn kho: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin tồn kho theo ID
   * @param id ID của tồn kho
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của tồn kho
   */
  async getInventoryById(id: number, userId: number): Promise<InventoryResponseDto> {
    try {
      // Tìm tồn kho theo ID và kết hợp với thông tin kho
      const inventoryWithWarehouseDetails = await this.inventoryRepository.findByIdWithWarehouseDetails(id);

      // Kiểm tra tồn kho tồn tại
      this.validationHelper.validateInventoryExists(inventoryWithWarehouseDetails, id);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Inventory không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Inventory hoặc sử dụng trường khác để xác định quyền sở hữu

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(InventoryResponseDto, inventoryWithWarehouseDetails, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin tồn kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_FETCH_FAILED,
        `Lỗi khi lấy thông tin tồn kho: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách tồn kho theo các điều kiện lọc
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách tồn kho với phân trang
   */
  async getInventories(queryDto: QueryInventoryDto): Promise<PaginatedResult<InventoryResponseDto>> {
    try {
      // Đảm bảo chỉ lấy tồn kho của người dùng hiện tại
      if (!queryDto.userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVENTORY_ACCESS_DENIED,
          `Không thể lấy danh sách tồn kho: Thiếu thông tin người dùng`
        );
      }

      // Lấy danh sách tồn kho từ repository
      const result = await this.inventoryRepository.findAll(queryDto);

      // Chuyển đổi từ entity sang DTO
      const items = result.items.map(inventory =>
        plainToInstance(InventoryResponseDto, inventory, {
          excludeExtraneousValues: true,
        }),
      );

      // Trả về kết quả với phân trang
      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách tồn kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_FETCH_FAILED,
        `Lỗi khi lấy danh sách tồn kho: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin tồn kho
   * @param id ID của tồn kho
   * @param updateDto DTO chứa thông tin cập nhật
   * @returns Thông tin tồn kho đã cập nhật
   */
  @Transactional()
  async updateInventory(id: number, updateDto: UpdateInventoryDto, userId: number): Promise<InventoryResponseDto> {
    try {
      // Kiểm tra tính hợp lệ của dữ liệu
      await this.validationHelper.validateUpdateInventory(id, updateDto);

      // Lấy thông tin tồn kho hiện tại
      const currentInventory = await this.inventoryRepository.findById(id);
      this.validationHelper.validateInventoryExists(currentInventory, id);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Inventory không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Inventory hoặc sử dụng trường khác để xác định quyền sở hữu

      // Chuẩn bị dữ liệu cập nhật
      const updatedData: Partial<Inventory> = {
        ...updateDto,
        lastUpdated: Date.now(),
      };

      // Tự động tính toán số lượng hiện tại và tổng số lượng
      const availableQuantity = updatedData.availableQuantity !== undefined ? updatedData.availableQuantity : currentInventory.availableQuantity;
      const reservedQuantity = updatedData.reservedQuantity !== undefined ? updatedData.reservedQuantity : currentInventory.reservedQuantity;
      const defectiveQuantity = updatedData.defectiveQuantity !== undefined ? updatedData.defectiveQuantity : currentInventory.defectiveQuantity;

      // Tính toán số lượng hiện tại mới
      updatedData.currentQuantity = availableQuantity + reservedQuantity + defectiveQuantity;

      // Đảm bảo tổng số lượng luôn lớn hơn hoặc bằng số lượng hiện tại
      if (updatedData.totalQuantity === undefined || updatedData.totalQuantity < updatedData.currentQuantity) {
        updatedData.totalQuantity = updatedData.currentQuantity;
      }

      // Cập nhật tồn kho trong cơ sở dữ liệu
      const updatedInventory = await this.inventoryRepository.updateInventory(id, updatedData);

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(InventoryResponseDto, updatedInventory, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật tồn kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_UPDATE_FAILED,
        `Lỗi khi cập nhật tồn kho: ${error.message}`,
      );
    }
  }

  /**
   * Xóa tồn kho
   * @param id ID của tồn kho
   * @param userId ID của người dùng
   * @returns Thông báo kết quả xóa
   */
  @Transactional()
  async deleteInventory(id: number, userId: number): Promise<{ success: boolean; message: string }> {
    try {
      // Tìm tồn kho theo ID
      const inventory = await this.inventoryRepository.findById(id);

      // Kiểm tra tồn kho tồn tại
      this.validationHelper.validateInventoryExists(inventory, id);

      // Kiểm tra quyền truy cập - Bỏ qua kiểm tra userId vì entity Inventory không có trường này
      // Trong tương lai, nên thêm trường userId vào entity Inventory hoặc sử dụng trường khác để xác định quyền sở hữu

      // Xóa tồn kho
      const result = await this.inventoryRepository.deleteInventory(id);

      // Trả về kết quả
      return {
        success: result,
        message: result ? 'Xóa tồn kho thành công' : 'Không thể xóa tồn kho',
      };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa tồn kho: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_DELETE_FAILED,
        `Lỗi khi xóa tồn kho: ${error.message}`,
      );
    }
  }
}
