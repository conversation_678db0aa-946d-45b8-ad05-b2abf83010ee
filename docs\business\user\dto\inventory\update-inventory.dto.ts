import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, Min, ValidateIf } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc cập nhật bản ghi tồn kho
 */
export class UpdateInventoryDto {
  /**
   * ID kho chứa sản phẩm
   * @example 2
   */
  @ApiProperty({
    description: 'ID kho chứa sản phẩm',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID kho phải là số' })
  @Type(() => Number)
  warehouseId?: number;

  /**
   * Số lượng hiện tại trong kho (tự động tính toán)
   * @example 120
   */
  @ApiProperty({
    description: 'Số lượng hiện tại trong kho (tự động tính toán từ các số lượng thành phần)',
    example: 120,
    readOnly: true,
    required: false,
    writeOnly: false
  })
  @ValidateIf(() => false)
  currentQuantity?: number;

  /**
   * Tổng số lượng đã nhập vào kho (tự động tính toán)
   * @example 200
   */
  @ApiProperty({
    description: 'Tổng số lượng đã nhập vào kho (tự động tính toán, luôn lớn hơn hoặc bằng số lượng hiện tại)',
    example: 200,
    readOnly: true,
    required: false,
    writeOnly: false
  })
  @ValidateIf(() => false)
  totalQuantity?: number;

  /**
   * Số lượng sẵn sàng để bán hoặc sử dụng
   * @example 110
   */
  @ApiProperty({
    description: 'Số lượng sẵn sàng để bán hoặc sử dụng',
    example: 110,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số lượng sẵn sàng phải là số' })
  @Min(0, { message: 'Số lượng sẵn sàng không được nhỏ hơn 0' })
  @Type(() => Number)
  availableQuantity?: number;

  /**
   * Số lượng bị giữ chỗ
   * @example 8
   */
  @ApiProperty({
    description: 'Số lượng bị giữ chỗ',
    example: 8,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số lượng giữ chỗ phải là số' })
  @Min(0, { message: 'Số lượng giữ chỗ không được nhỏ hơn 0' })
  @Type(() => Number)
  reservedQuantity?: number;

  /**
   * Số lượng sản phẩm lỗi
   * @example 2
   */
  @ApiProperty({
    description: 'Số lượng sản phẩm lỗi',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số lượng sản phẩm lỗi phải là số' })
  @Min(0, { message: 'Số lượng sản phẩm lỗi không được nhỏ hơn 0' })
  @Type(() => Number)
  defectiveQuantity?: number;
}
