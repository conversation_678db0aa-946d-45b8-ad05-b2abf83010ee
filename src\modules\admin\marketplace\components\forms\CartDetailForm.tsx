import React from 'react';
import { useTranslation } from 'react-i18next';
import { Form, FormItem, Input, Button, Typography, Table } from '@/shared/components/common';
import { Cart, CartItem } from '@/modules/admin/marketplace/types/cart.types';

export interface CartDetailFormProps {
  cart: Cart | null;
  onClose: () => void;
  onDelete?: () => void;
  readOnly?: boolean;
}

/**
 * Form component for viewing cart details
 */
const CartDetailForm: React.FC<CartDetailFormProps> = ({ cart, onClose, onDelete }) => {
  const { t } = useTranslation();

  // Form ref
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const formRef = React.useRef<any>(null);

  // Handle form submission (not used in read-only mode)
  const handleSubmit = (values: Record<string, unknown>) => {
    // This function is not used in the current implementation
    console.log('Form submitted:', values);
  };

  // <PERSON><PERSON><PERSON> nghĩa các cột cho bảng chi tiết sản phẩm
  const itemColumns = [
    {
      key: 'product',
      title: t('admin.marketplace.cart.table.product', 'Sản phẩm'),
      width: '35%',
      render: (_: unknown, record: CartItem) => (
        <div className="flex flex-col">
          <span className="font-medium">{record.product.name}</span>
          <span className="text-xs text-gray-500">ID: {record.product.id}</span>
        </div>
      ),
    },
    {
      key: 'price',
      title: t('admin.marketplace.cart.table.price', 'Đơn giá'),
      width: '20%',
      render: (_: unknown, record: CartItem) => (
        <span>{record.product.discountedPrice} points</span>
      ),
    },
    {
      key: 'quantity',
      title: t('admin.marketplace.cart.table.quantity', 'Số lượng'),
      dataIndex: 'quantity',
      width: '15%',
    },
    {
      key: 'total',
      title: t('admin.marketplace.cart.table.total', 'Thành tiền'),
      width: '20%',
      render: (_: unknown, record: CartItem) => {
        const total = record.quantity * record.product.discountedPrice;
        return <span>{total} points</span>;
      },
    },
  ];

  return (
    <div className="p-6">
      <Typography variant="h6" className="mb-4">
        {t('admin.marketplace.cart.details', 'Chi tiết giỏ hàng')}
        {cart?.id && <span className="ml-2 text-gray-500">#{cart.id}</span>}
      </Typography>

      <Form
        ref={formRef}
        defaultValues={cart || undefined}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        className="space-y-4"
      >
        {/* Thông tin giỏ hàng */}
        <div className="bg-gray-50 p-4 rounded-md mb-4">
          <Typography variant="subtitle2" className="mb-2">
            {t('admin.marketplace.cart.info', 'Thông tin giỏ hàng')}
          </Typography>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem name="id" label={t('admin.marketplace.cart.form.id', 'ID giỏ hàng')}>
              <Input fullWidth readOnly value={cart?.id || ''} />
            </FormItem>

            <FormItem
              name="userId"
              label={t('admin.marketplace.cart.form.userId', 'ID người dùng')}
            >
              <Input fullWidth readOnly value={cart?.user?.id?.toString() || ''} />
            </FormItem>

            <FormItem
              name="userName"
              label={t('admin.marketplace.cart.form.userName', 'Tên người dùng')}
            >
              <Input fullWidth readOnly value={cart?.user?.name || ''} />
            </FormItem>

            <FormItem name="userEmail" label={t('admin.marketplace.cart.form.userEmail', 'Email')}>
              <Input fullWidth readOnly value={cart?.user?.email || ''} />
            </FormItem>

            <FormItem
              name="totalItems"
              label={t('admin.marketplace.cart.form.totalItems', 'Số sản phẩm')}
            >
              <Input fullWidth readOnly value={(cart?.items?.length || 0).toString()} />
            </FormItem>

            <FormItem
              name="totalValue"
              label={t('admin.marketplace.cart.form.totalValue', 'Tổng tiền')}
            >
              <Input
                fullWidth
                readOnly
                value={cart?.totalValue ? `${cart.totalValue} points` : '0 points'}
              />
            </FormItem>

            <FormItem
              name="createdAt"
              label={t('admin.marketplace.cart.form.createdAt', 'Ngày tạo')}
            >
              <Input
                fullWidth
                readOnly
                value={cart?.createdAt ? new Date(cart.createdAt).toLocaleString() : ''}
              />
            </FormItem>
          </div>
        </div>

        {/* Sản phẩm trong giỏ hàng */}
        <div className="mb-4">
          <Typography variant="subtitle2" className="mb-2">
            {t('admin.marketplace.cart.items', 'Sản phẩm trong giỏ hàng')}
          </Typography>

          {cart?.items && cart.items.length > 0 ? (
            <div className="border border-gray-200 rounded-md overflow-hidden">
              <Table columns={itemColumns} data={cart.items} rowKey="id" pagination={false} />
            </div>
          ) : (
            <Typography className="text-gray-500 italic">
              {t('admin.marketplace.cart.items.empty', 'Không có sản phẩm nào')}
            </Typography>
          )}
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={onClose}>
            {t('common.back', 'Quay lại')}
          </Button>
          {onDelete && (
            <Button variant="danger" onClick={onDelete}>
              {t('admin.marketplace.cart.delete', 'Xóa giỏ hàng')}
            </Button>
          )}
        </div>
      </Form>
    </div>
  );
};

export default CartDetailForm;
