import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AdminToolGroupService } from '../services/tool-group.service';
import {
  ToolGroupQueryParams,
  ToolGroupDetail,
  ToolGroupListItem,
  CreateToolGroupParams,
  UpdateToolGroupParams,
  UpdateToolGroupToolsParams,
  PaginatedResult,
} from '../types/tool.types';

// Khởi tạo service
const toolGroupService = new AdminToolGroupService();

// Các key cho React Query
export const ADMIN_TOOL_GROUP_QUERY_KEYS = {
  all: ['admin', 'tool-groups'] as const,
  lists: () => [...ADMIN_TOOL_GROUP_QUERY_KEYS.all, 'list'] as const,
  list: (params: ToolGroupQueryParams) => [...ADMIN_TOOL_GROUP_QUERY_KEYS.lists(), params] as const,
  details: () => [...ADMIN_TOOL_GROUP_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...ADMIN_TOOL_GROUP_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách nhóm tool
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useAdminToolGroups = (params: ToolGroupQueryParams = {}) => {
  return useQuery<PaginatedResult<ToolGroupListItem>>({
    queryKey: ADMIN_TOOL_GROUP_QUERY_KEYS.list(params),
    queryFn: () => toolGroupService.getToolGroups(params),
  });
};

/**
 * Hook để lấy thông tin chi tiết nhóm tool
 * @param id ID của nhóm tool
 * @returns Query object
 */
export const useAdminToolGroupDetail = (id: number) => {
  return useQuery<ToolGroupDetail>({
    queryKey: ADMIN_TOOL_GROUP_QUERY_KEYS.detail(id),
    queryFn: () => toolGroupService.getToolGroupById(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo nhóm tool mới
 * @returns Mutation object
 */
export const useCreateAdminToolGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateToolGroupParams) => toolGroupService.createToolGroup(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách nhóm tool
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_GROUP_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật thông tin nhóm tool
 * @returns Mutation object
 */
export const useUpdateAdminToolGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateToolGroupParams }) =>
      toolGroupService.updateToolGroup(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết nhóm tool và danh sách nhóm tool
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_GROUP_QUERY_KEYS.detail(variables.id),
      });
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_GROUP_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để xóa nhóm tool
 * @returns Mutation object
 */
export const useDeleteAdminToolGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => toolGroupService.deleteToolGroup(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách nhóm tool
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_GROUP_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật danh sách tool trong nhóm
 * @returns Mutation object
 */
export const useUpdateAdminToolGroupTools = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateToolGroupToolsParams }) =>
      toolGroupService.updateToolGroupTools(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết nhóm tool
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_GROUP_QUERY_KEYS.detail(variables.id),
      });
    },
  });
};
