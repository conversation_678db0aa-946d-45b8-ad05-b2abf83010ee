import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Button,
  Table,
  ActionMenu,
  ActionMenuItem,
  EmptyState
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import { useGoogleAdsAccounts, useGoogleAdsCampaigns } from '../../hooks';
import {
  GoogleAdsAccountDto,
  GoogleAdsAccountStatus,
  GoogleAdsCampaignQueryDto,
  GoogleAdsCampaignDto,
  GoogleAdsCampaignStatus
} from '../../types';
import { GoogleAdsCampaignTable } from '../../components/google-ads';

/**
 * Trang quản lý Google Ads
 */
const GoogleAdsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const [activeTab, setActiveTab] = useState<string>('accounts');
  const [selectedAccountId, setSelectedAccountId] = useState<number | null>(null);

  // Truy vấn danh sách tài khoản Google Ads
  const { useAccounts } = useGoogleAdsAccounts();
  const { data: accountsData, isLoading: isLoadingAccounts } = useAccounts({});

  // Truy vấn danh sách chiến dịch Google Ads
  const { useCampaigns, useUpdateCampaignStatus } = useGoogleAdsCampaigns();
  const campaignQueryParams: GoogleAdsCampaignQueryDto = {
    accountId: selectedAccountId || undefined,
  };
  const { data: campaignsData, isLoading: isLoadingCampaigns } = useCampaigns(campaignQueryParams);

  // Mutation để cập nhật trạng thái chiến dịch
  const { mutate: updateCampaignStatus } = useUpdateCampaignStatus();

  // Định nghĩa các cột cho bảng tài khoản
  const accountColumns: TableColumn<GoogleAdsAccountDto>[] = [
    {
      key: 'name',
      title: t('marketing:googleAds.name', 'Tên tài khoản'),
      dataIndex: 'name',
      width: '30%',
    },
    {
      key: 'customerId',
      title: t('marketing:googleAds.customerId', 'Customer ID'),
      dataIndex: 'customerId',
      width: '30%',
    },
    {
      key: 'status',
      title: t('common:status', 'Trạng thái'),
      dataIndex: 'status',
      width: '20%',
      render: (value: unknown) => {
        const status = value as GoogleAdsAccountStatus;
        let color = '';
        let textColor = '';

        switch (status) {
          case GoogleAdsAccountStatus.ACTIVE:
            color = 'bg-green-100 dark:bg-green-900';
            textColor = 'text-green-800 dark:text-green-200';
            break;
          case GoogleAdsAccountStatus.INACTIVE:
            color = 'bg-yellow-100 dark:bg-yellow-900';
            textColor = 'text-yellow-800 dark:text-yellow-200';
            break;
          case GoogleAdsAccountStatus.ERROR:
            color = 'bg-red-100 dark:bg-red-900';
            textColor = 'text-red-800 dark:text-red-200';
            break;
          case GoogleAdsAccountStatus.PENDING:
            color = 'bg-blue-100 dark:bg-blue-900';
            textColor = 'text-blue-800 dark:text-blue-200';
            break;
          default:
            color = 'bg-gray-100 dark:bg-gray-700';
            textColor = 'text-gray-800 dark:text-gray-200';
        }

        return (
          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${color} ${textColor}`}>
            {status}
          </div>
        );
      },
    },
    {
      key: 'actions',
      title: t('common:actions', 'Thao tác'),
      width: '20%',
      render: (_: unknown, record: GoogleAdsAccountDto) => {
        const actionItems: ActionMenuItem[] = [
          {
            id: 'edit',
            label: t('common:edit', 'Chỉnh sửa'),
            icon: 'edit',
            onClick: () => {
              // Xử lý khi người dùng nhấp vào nút chỉnh sửa
              console.log('Edit account', record);
            },
          },
          {
            id: 'delete',
            label: t('common:delete', 'Xóa'),
            icon: 'trash',
            onClick: () => {
              // Xử lý khi người dùng nhấp vào nút xóa
              console.log('Delete account', record);
            },
          },
          {
            id: 'viewCampaigns',
            label: t('marketing:googleAds.viewCampaigns', 'Xem chiến dịch'),
            icon: 'eye',
            onClick: () => {
              // Chuyển sang tab chiến dịch và lọc theo tài khoản này
              setSelectedAccountId(record.id);
              setActiveTab('campaigns');
            },
          },
        ];

        return (
          <div className="flex justify-center">
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm hành động')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              menuIcon="more-horizontal"
              showAllInMenu={true}
              preferRight={true}
              preferTop={false}
            />
          </div>
        );
      },
    },
  ];

  // Xử lý khi thay đổi tab
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    // Nếu chuyển sang tab tài khoản, reset selectedAccountId
    if (tabId === 'accounts') {
      setSelectedAccountId(null);
    }
  };

  // Xử lý khi người dùng thay đổi trạng thái chiến dịch
  const handleCampaignStatusChange = (campaign: GoogleAdsCampaignDto, newStatus: GoogleAdsCampaignStatus) => {
    updateCampaignStatus({ id: campaign.id, status: newStatus });
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold mb-2">{t('marketing:googleAds.title', 'Google Ads')}</h1>
        <p className="text-gray-500">{t('marketing:googleAds.description', 'Tích hợp và quản lý chiến dịch Google Ads từ hệ thống')}</p>
      </div>

      <Card>
        <div className="flex justify-between items-center mb-6">
          <div className="flex space-x-4 border-b border-gray-200 dark:border-gray-700">
            <button
              className={`px-4 py-2 ${activeTab === 'accounts' ? 'border-b-2 border-primary-500 font-medium text-primary-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => handleTabChange('accounts')}
            >
              {t('marketing:googleAds.tabs.accounts', 'Tài khoản')}
            </button>
            <button
              className={`px-4 py-2 ${activeTab === 'campaigns' ? 'border-b-2 border-primary-500 font-medium text-primary-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => handleTabChange('campaigns')}
            >
              {t('marketing:googleAds.tabs.campaigns', 'Chiến dịch')}
            </button>
            <button
              className={`px-4 py-2 ${activeTab === 'performance' ? 'border-b-2 border-primary-500 font-medium text-primary-600' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => handleTabChange('performance')}
            >
              {t('marketing:googleAds.tabs.performance', 'Hiệu suất')}
            </button>
          </div>

          {activeTab === 'accounts' && (
            <Button
              onClick={() => console.log('Connect new account')}
            >
              <span className="flex items-center">
                <span className="mr-1">+</span>
                {t('marketing:googleAds.connectAccount', 'Kết nối tài khoản')}
              </span>
            </Button>
          )}

          {activeTab === 'campaigns' && (
            <Button
              onClick={() => console.log('Create new campaign')}
            >
              <span className="flex items-center">
                <span className="mr-1">+</span>
                {t('marketing:googleAds.createCampaign', 'Tạo chiến dịch mới')}
              </span>
            </Button>
          )}
        </div>

        {activeTab === 'accounts' && (
          <div>
            <Table
              columns={accountColumns}
              data={accountsData?.items || []}
              rowKey="id"
              loading={isLoadingAccounts}
              pagination={{
                current: accountsData?.meta.currentPage || 1,
                pageSize: accountsData?.meta.itemsPerPage || 10,
                total: accountsData?.meta.totalItems || 0,
                onChange: (page, pageSize) => {
                  console.log('Page changed', page, pageSize);
                },
              }}
            />
          </div>
        )}

        {activeTab === 'campaigns' && (
          <div>
            {selectedAccountId && (
              <div className="mb-4">
                <Button
                  onClick={() => setSelectedAccountId(null)}
                >
                  <span className="flex items-center">
                    <span className="mr-1">×</span>
                    {t('common:clearFilter', 'Xóa bộ lọc')}
                  </span>
                </Button>
              </div>
            )}
            <GoogleAdsCampaignTable
              campaigns={campaignsData?.items || []}
              loading={isLoadingCampaigns}
              onEdit={(campaign) => console.log('Edit campaign', campaign)}
              onDelete={(campaign) => console.log('Delete campaign', campaign)}
              onStatusChange={handleCampaignStatusChange}
              pagination={{
                current: campaignsData?.meta.currentPage || 1,
                pageSize: campaignsData?.meta.itemsPerPage || 10,
                total: campaignsData?.meta.totalItems || 0,
                onChange: (page, pageSize) => {
                  console.log('Page changed', page, pageSize);
                },
              }}
            />
          </div>
        )}

        {activeTab === 'performance' && (
          <EmptyState
            title={t('marketing:googleAds.performanceComingSoon', 'Báo cáo hiệu suất đang được phát triển')}
            description={t('marketing:googleAds.comingSoon', 'Chức năng đang được phát triển. Vui lòng quay lại sau!')}
            icon="bar-chart"
          />
        )}
      </Card>
    </div>
  );
};

export default GoogleAdsPage;