import React, { useState, useRef, useEffect, useMemo } from 'react';
import {
  Typography,
  ScrollArea,
  Tooltip
} from '@/shared/components/common';
import { DatasetMessage } from '../types/dataset.types';
import { Trash2, Send, User, Bot, Settings } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface ChatPanelProps {
  /**
   * Tiêu đề của panel
   */
  title: string;

  /**
   * Danh sách message
   */
  messages: DatasetMessage[];

  /**
   * Callback khi thêm message
   */
  onAddMessage: (message: DatasetMessage) => void;

  /**
   * Callback khi xóa message
   */
  onDeleteMessage: (index: number) => void;

  /**
   * Placeholder cho input
   */
  placeholder?: string;
}

/**
 * Component hiển thị panel chat cho dataset
 */
const ChatPanel: React.FC<ChatPanelProps> = ({
  title,
  messages,
  onAddMessage,
  onDeleteMessage,
  placeholder = 'Nhập tin nhắn...'
}) => {
  const { t } = useTranslation();
  const [role, setRole] = useState<'system' | 'user' | 'assistant'>('user');
  const [content, setContent] = useState('');
  const [showRoleMenu, setShowRoleMenu] = useState(false);

  // Danh sách role options
  const roleOptions = useMemo(() => [
    {
      value: 'system',
      label: t('System Role'),
      icon: <Settings size={16} className="mr-2" />,
      color: 'text-gray-500 dark:text-gray-400'
    },
    {
      value: 'user',
      label: t('User Role'),
      icon: <User size={16} className="mr-2" />,
      color: 'text-blue-500 dark:text-blue-400'
    },
    {
      value: 'assistant',
      label: t('Assistant Role'),
      icon: <Bot size={16} className="mr-2" />,
      color: 'text-green-500 dark:text-green-400'
    },
  ], [t]);

  // Xử lý khi gửi message
  const handleSendMessage = () => {
    if (content.trim()) {
      onAddMessage({
        role,
        content,
      });
      setContent('');

      // Đặt focus lại vào textarea sau khi gửi
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    }
  };

  // Xử lý khi nhấn Enter hoặc /
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Gửi tin nhắn khi nhấn Enter (không phải Shift+Enter)
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }

    // Xử lý khi nhấn / để mở menu
    if (e.key === '/' && content === '') {
      e.preventDefault();
      // Đây là nơi sẽ mở menu, nhưng hiện tại chỉ log ra console
      console.log('Open menu triggered');
    }
  };

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Adjust textarea height based on content
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 150)}px`;
    }
  }, [content]);

  // Đặt focus vào textarea khi component mount
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []);

  // Tạo một component con để hiển thị tin nhắn
  const MessageItem = ({ message, index }: { message: DatasetMessage; index: number }) => {
    // Xác định vị trí hiển thị dựa trên role
    const messagePosition =
      message.role === 'assistant' ? 'justify-start' :
        message.role === 'user' ? 'justify-end' :
          'justify-center';

    // Xác định màu nền dựa trên role
    const messageBgColor =
      message.role === 'assistant' ? 'bg-blue-50 dark:bg-blue-900/20' :
        message.role === 'user' ? 'bg-green-50 dark:bg-green-900/20' :
          'bg-gray-100 dark:bg-gray-800';

    // Xác định màu viền dựa trên role
    const messageBorderColor =
      message.role === 'assistant' ? 'border-blue-200 dark:border-blue-800' :
        message.role === 'user' ? 'border-green-200 dark:border-green-800' :
          'border-gray-300 dark:border-gray-700';

    // Xác định icon dựa trên role
    const roleIcon = roleOptions.find(option => option.value === message.role)?.icon;

    return (
      <div className={`flex items-start gap-2 ${messagePosition} mb-4`}>
        {/* Avatar cho assistant */}
        {message.role === 'assistant' && (
          <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0 bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
            <Bot size={18} className="text-green-600 dark:text-green-400" />
          </div>
        )}

        {/* Message content */}
        <div className="flex flex-col items-start gap-1 max-w-[80%]">
          <div className={`px-3 py-2 rounded-lg border ${messageBgColor} ${messageBorderColor}`}>
            <div className="flex items-center text-xs font-medium mb-1 text-gray-500 dark:text-gray-400">
              {roleIcon}
              {message.role.charAt(0).toUpperCase() + message.role.slice(1)}
            </div>
            <div className="whitespace-pre-wrap break-words">
              {message.content}
            </div>
          </div>
          <div className="flex items-center">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {new Date().toLocaleTimeString()}
            </span>
            <Tooltip content={t('Xóa tin nhắn')} position="top">
              <button
                className="ml-2 p-1 rounded hover:bg-red-100 dark:hover:bg-red-900/20 text-red-500"
                onClick={() => onDeleteMessage(index)}
              >
                <Trash2 size={14} />
              </button>
            </Tooltip>
          </div>
        </div>

        {/* Avatar cho user */}
        {message.role === 'user' && (
          <div className="w-8 h-8 rounded-full overflow-hidden bg-blue-100 dark:bg-blue-900/30 flex-shrink-0 flex items-center justify-center">
            <User size={18} className="text-blue-600 dark:text-blue-400" />
          </div>
        )}

        {/* Avatar cho system */}
        {message.role === 'system' && (
          <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700 flex-shrink-0 flex items-center justify-center">
            <Settings size={18} className="text-gray-600 dark:text-gray-400" />
          </div>
        )}
      </div>
    );
  };

  return (
    <div>
      <div className="flex items-center justify-between px-4 pt-4 pb-2 border-b border-gray-200 dark:border-gray-700">
        <Typography variant="h6" className="flex items-center">
          <span className="mr-2">{title}</span>
        </Typography>

        <div className="flex items-center">
          <Tooltip content={t('Chọn role hiện tại')} position="top">
            <div
              className="flex items-center px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-800 cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700"
              onClick={() => setShowRoleMenu(!showRoleMenu)}
            >
              {roleOptions.find(option => option.value === role)?.icon}
              <span className={`text-sm ${roleOptions.find(option => option.value === role)?.color}`}>
                {roleOptions.find(option => option.value === role)?.label}
              </span>
            </div>
          </Tooltip>
        </div>
      </div>
      <ScrollArea
        className="flex-1 px-4 py-4"
        height="calc(100% - 140px)" // Trừ đi chiều cao của input area
        invisible={true}
      >
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <div className="text-4xl mb-4">💬</div>
            <h2 className="text-xl font-semibold mb-2">{t('Bắt đầu cuộc trò chuyện')}</h2>
            <p className="text-gray-500 dark:text-gray-400 max-w-md">
              {t('Thêm tin nhắn để tạo dataset huấn luyện')}
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {messages.map((message, index) => (
              <MessageItem key={index} message={message} index={index} />
            ))}
          </div>
        )}
        <div ref={messagesEndRef} />
      </ScrollArea>

      {/* Input area - fixed at bottom */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-3 mt-auto">
        <div className="relative flex flex-col bg-white dark:bg-gray-800 rounded-xl shadow-lg w-full chat-input-box-container">
          <div className="w-full px-3 py-3">
            <textarea
              ref={textareaRef}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={t(placeholder)}
              className="w-full bg-transparent border-0 focus:ring-0 focus:outline-none dark:text-white text-gray-800 resize-none max-h-[150px] custom-scrollbar"
              rows={1}
            />
          </div>

          {/* Action buttons row */}
          <div className="flex items-center px-2 py-2 space-x-1 border-t border-gray-100 dark:border-gray-700">
            {/* Role selector button */}
            <div className="relative">
              <Tooltip content={t('Chọn role cho tin nhắn')} position="top">
                <button
                  className="p-2 w-10 h-10 flex items-center justify-center text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors flex-shrink-0"
                  aria-label={t('Chọn role')}
                  onClick={() => setShowRoleMenu(!showRoleMenu)}
                >
                  <div className="w-6 h-6 flex items-center justify-center">
                    {role === 'assistant' && <Bot size={20} className="text-green-500" />}
                    {role === 'user' && <User size={20} className="text-blue-500" />}
                    {role === 'system' && <Settings size={20} className="text-gray-500" />}
                  </div>
                </button>
              </Tooltip>

              {/* Role selection menu */}
              {showRoleMenu && (
                <div className="absolute left-0 bottom-full mb-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-20 w-56">
                  <div className="p-2">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-2 px-2 font-medium">{t('Chọn role cho tin nhắn')}</div>
                    {roleOptions.map((option) => (
                      <button
                        key={option.value}
                        className={`w-full text-left px-3 py-2 rounded-md transition-colors flex items-center ${role === option.value
                          ? 'bg-primary/10 text-primary dark:bg-primary/20'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                          }`}
                        onClick={() => {
                          setRole(option.value as 'system' | 'user' | 'assistant');
                          setShowRoleMenu(false);
                          if (textareaRef.current) {
                            textareaRef.current.focus();
                          }
                        }}
                      >
                        {option.icon}
                        <span className={option.color}>{option.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="flex-grow"></div>

            {/* Send button */}
            <Tooltip content={t('Gửi tin nhắn')} position="top">
              <button
                onClick={handleSendMessage}
                disabled={!content.trim()}
                className={`p-2 w-10 h-10 flex items-center justify-center rounded-full transition-colors ${content.trim()
                  ? 'text-white bg-primary hover:bg-primary/90'
                  : 'text-gray-400 dark:text-gray-600 cursor-not-allowed bg-gray-100 dark:bg-gray-700'
                  }`}
                aria-label={t('Gửi')}
              >
                <Send size={18} />
              </button>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatPanel;
