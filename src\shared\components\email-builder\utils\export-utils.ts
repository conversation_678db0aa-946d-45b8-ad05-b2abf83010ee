import { EmailData, EmailElement, Asset } from '../types';
import { generateHTML, extractCssFromHtml } from '../utils';

/**
 * Interface cho dữ liệu dự án GrapesJS
 */
export interface GrapesJSProject {
  id: string;
  name: string;
  createdAt: string;
  html: string;
  css: string;
  components: string;
  assets: string;
  metadata: {
    subject: string;
    preheader: string;
    version: string;
    type: string;
    framework: string;
  };
}

/**
 * Tạo dữ liệu dự án GrapesJS từ các phần tử email
 * @param emailElements Các phần tử email
 * @param emailData Dữ liệu email
 * @param assets Tài nguyên
 * @returns Dữ liệu dự án GrapesJS
 */
export const createGrapesJSProject = (
  emailElements: EmailElement[],
  emailData: EmailData,
  assets: Asset[]
): GrapesJSProject => {
  // Tạo HTML từ các phần tử
  const html = generateHTML(emailElements, emailData);
  
  // Trích xuất CSS từ HTML
  const css = extractCssFromHtml(html);

  // Tạo dữ liệu dự án
  return {
    id: `project-${Date.now()}`,
    name: emailData.name || 'Email Template',
    createdAt: new Date().toISOString(),
    html,
    css,
    components: JSON.stringify(emailElements),
    assets: JSON.stringify(assets),
    metadata: {
      subject: emailData.subject || '',
      preheader: emailData.preheader || '',
      version: '1.0.0',
      type: 'email',
      framework: 'grapesjs'
    }
  };
};

/**
 * Lưu dự án GrapesJS dưới dạng file .grapesjs
 * @param project Dữ liệu dự án GrapesJS
 * @returns Promise void
 */
export const saveGrapesJSProject = (project: GrapesJSProject): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      // Chuyển đổi thành chuỗi JSON
      const projectJson = JSON.stringify(project, null, 2);

      // Tạo một blob và tạo URL để download
      const blob = new Blob([projectJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      // Tạo một element a tạm thời để download
      const a = document.createElement('a');
      a.href = url;
      a.download = `${project.name || 'email-template'}.grapesjs`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      resolve();
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Lưu HTML và CSS dưới dạng file .html
 * @param html Mã HTML
 * @param css Mã CSS
 * @param fileName Tên file
 * @returns Promise void
 */
export const saveHTMLFile = (html: string, css: string, fileName: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      // Tạo HTML đầy đủ với CSS nhúng
      const fullHTML = `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${fileName}</title>
  <style>
${css}
  </style>
</head>
<body>
${html}
</body>
</html>`;

      // Tạo một blob và tạo URL để download
      const blob = new Blob([fullHTML], { type: 'text/html' });
      const url = URL.createObjectURL(blob);

      // Tạo một element a tạm thời để download
      const a = document.createElement('a');
      a.href = url;
      a.download = `${fileName || 'email-template'}.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      resolve();
    } catch (error) {
      reject(error);
    }
  });
};
