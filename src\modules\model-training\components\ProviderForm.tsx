import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormItem,
  Input,
  Button,
} from '@/shared/components/common';
import { CreateProviderSchema } from '../schemas/provider.schema';
import { ProviderType } from '../types/provider.types';
import { z } from 'zod';

type FormValues = z.infer<typeof CreateProviderSchema>;

interface ProviderFormProps {
  initialValues?: Partial<FormValues>;
  onSubmit: (data: FormValues) => void;
  isLoading?: boolean;
}

/**
 * Component form tạo/chỉnh sửa nhà cung cấp
 */
const ProviderForm: React.FC<ProviderFormProps> = ({
  initialValues,
  onSubmit,
  isLoading = false,
}) => {
  const form = useForm<FormValues>({
    resolver: zodResolver(CreateProviderSchema),
    defaultValues: initialValues || {
      name: '',
      type: ProviderType.OPENAI,
      apiKey: '',
    },
  });

  return (
    <Form onSubmit={(data) => onSubmit(data as FormValues)}>
      <div className="space-y-4">
        <FormItem
          name="name"
          label="Name"
          required
        >
          <Input
            placeholder="Nhập tên nhà cung cấp"
            {...form.register('name')}
            helperText={form.formState.errors.name?.message}
            fullWidth
          />
        </FormItem>
        <FormItem
          name="apiKey"
          label="API Key"
          required
        >
          <Input
            placeholder="Nhập API Key"
            {...form.register('apiKey')}
            helperText={form.formState.errors.apiKey?.message}
            type="password"
            fullWidth
          />
        </FormItem>
        <div className="flex justify-end pt-4">
          <Button type="submit" isLoading={isLoading}>
            {initialValues?.name ? 'Cập nhật' : 'Tích hợp'}
          </Button>
        </div>
      </div>
    </Form>
  );
};

export default ProviderForm;
