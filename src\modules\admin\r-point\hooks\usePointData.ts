import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { PointService } from '../services';
import { CreatePointDto, PointQueryParams, UpdatePointDto } from '../types';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';

/**
 * Hook quản lý dữ liệu gói point
 */
export const usePointData = () => {
  const queryClient = useQueryClient();

  /**
   * Hook lấy danh sách gói point
   * @param params Tham số truy vấn
   * @returns Danh sách gói point và thông tin phân trang
   */
  const usePoints = (params: PointQueryParams) => {
    return useQuery({
      queryKey: ['points', params],
      queryFn: () => PointService.getPoints(params),
      select: data => data.result,
    });
  };

  /**
   * Hook lấy thông tin chi tiết của một gói point
   * @param id ID của gói point
   * @returns Thông tin chi tiết gói point
   */
  const usePointDetail = (id: number) => {
    return useQuery({
      queryKey: ['point', id],
      queryFn: () => PointService.getPointById(id),
      select: data => data.result,
      enabled: !!id,
    });
  };

  /**
   * Hook tạo mới gói point
   */
  const useCreatePoint = () => {
    return useMutation({
      mutationFn: (data: CreatePointDto) => PointService.createPoint(data),
      onSuccess: data => {
        NotificationUtil.success({ message: data.message || 'Tạo mới gói point thành công' });
        queryClient.invalidateQueries({ queryKey: ['points'] });
      },
      onError: (error: AxiosError<{ message: string }>) => {
        NotificationUtil.error({
          message: error.response?.data?.message || 'Tạo mới gói point thất bại',
        });
      },
    });
  };

  /**
   * Hook cập nhật thông tin gói point
   */
  const useUpdatePoint = () => {
    return useMutation({
      mutationFn: ({ id, data }: { id: number; data: UpdatePointDto }) =>
        PointService.updatePoint(id, data),
      onSuccess: data => {
        NotificationUtil.success({ message: data.message || 'Cập nhật gói point thành công' });
        queryClient.invalidateQueries({ queryKey: ['points'] });
        queryClient.invalidateQueries({ queryKey: ['point'] });
      },
      onError: (error: AxiosError<{ message: string }>) => {
        NotificationUtil.error({
          message: error.response?.data?.message || 'Cập nhật gói point thất bại',
        });
      },
    });
  };

  /**
   * Hook xóa gói point
   */
  const useDeletePoint = () => {
    return useMutation({
      mutationFn: (id: number) => PointService.deletePoint(id),
      onSuccess: data => {
        NotificationUtil.success({ message: data.message || 'Xóa gói point thành công' });
        queryClient.invalidateQueries({ queryKey: ['points'] });
      },
      onError: (error: AxiosError<{ message: string }>) => {
        NotificationUtil.error({
          message: error.response?.data?.message || 'Xóa gói point thất bại',
        });
      },
    });
  };

  return {
    usePoints,
    usePointDetail,
    useCreatePoint,
    useUpdatePoint,
    useDeletePoint,
  };
};
