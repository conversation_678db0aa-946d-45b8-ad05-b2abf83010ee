import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/shared/components/common';
import { Url } from '@/modules/admin/data/url/types/url.types';

/**
 * Props cho URLForm
 */
interface URLFormProps {
  /**
   * Giá trị ban đầu cho form (dùng khi chỉnh sửa hoặc xem chi tiết)
   */
  initialValues?: Url;
  
  /**
   * Callback khi form được submit
   */
  onSubmit: (values: Record<string, unknown>) => void;
  
  /**
   * Callback khi người dùng hủy thao tác
   */
  onCancel: () => void;
  
  /**
   * Chế độ chỉ đọc (dùng khi xem chi tiết)
   */
  readOnly?: boolean;
}

/**
 * Form để tạo mới, chỉnh sửa hoặc xem chi tiết URL
 */
const URLForm: React.FC<URLFormProps> = ({ initialValues, onSubmit, onCancel, readOnly = false }) => {
  const { t } = useTranslation();
  const [formValues, setFormValues] = useState<Record<string, unknown>>({
    url: initialValues?.url || '',
    title: initialValues?.title || '',
    content: initialValues?.content || '',
    type: initialValues?.type || '',
    tags: initialValues?.tags?.join(', ') || '',
    ownedBy: initialValues?.ownedBy || '',
    isActive: initialValues?.isActive ?? true,
  });

  /**
   * Xử lý khi giá trị của các trường thay đổi
   */
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormValues(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormValues(prev => ({ ...prev, [name]: value }));
    }
  };

  /**
   * Xử lý khi form được submit
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Xử lý tags từ chuỗi thành mảng
    const processedValues = { ...formValues };
    if (typeof processedValues.tags === 'string') {
      processedValues.tags = (processedValues.tags as string)
        .split(',')
        .map(tag => tag.trim())
        .filter(Boolean);
    }
    onSubmit(processedValues);
  };

  return (
    <div className="p-4">
 
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block mb-1">{t('data:url.form.url')}</label>
          <input
            type="text"
            name="url"
            value={formValues.url as string}
            onChange={handleChange}
            className="w-full p-2 border rounded"
            disabled={readOnly}
            required
          />
        </div>
        <div>
          <label className="block mb-1">{t('data:url.form.title')}</label>
          <input
            type="text"
            name="title"
            value={formValues.title as string}
            onChange={handleChange}
            className="w-full p-2 border rounded"
            disabled={readOnly}
            required
          />
        </div>
        <div>
          <label className="block mb-1">{t('data:url.form.description')}</label>
          <textarea
            name="content"
            value={formValues.content as string}
            onChange={handleChange}
            className="w-full p-2 border rounded"
            rows={5}
            disabled={readOnly}
            required
          />
        </div>
        <div>
          <label className="block mb-1">{t('data:url.form.type')}</label>
          <input
            type="text"
            name="type"
            value={formValues.type as string}
            onChange={handleChange}
            className="w-full p-2 border rounded"
            disabled={readOnly}
          />
        </div>
        <div>
          <label className="block mb-1">{t('data:url.form.tags')}</label>
          <input
            type="text"
            name="tags"
            value={formValues.tags as string}
            onChange={handleChange}
            className="w-full p-2 border rounded"
            disabled={readOnly}
          />
        </div>
        <div>
          <label className="block mb-1">{t('data:url.form.ownedBy')}</label>
          <input
            type="number"
            name="ownedBy"
            value={formValues.ownedBy as string}
            onChange={handleChange}
            className="w-full p-2 border rounded"
            disabled={readOnly}
            required
          />
        </div>
        <div className="flex items-center">
          <input
            type="checkbox"
            name="isActive"
            checked={formValues.isActive as boolean}
            onChange={e => setFormValues(prev => ({ ...prev, isActive: e.target.checked }))}
            className="mr-2"
            disabled={readOnly}
          />
          <label>{t('data:url.form.isActive')}</label>
        </div>
        {!readOnly && (
          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={onCancel}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button type="submit" variant="primary">
              {initialValues ? t('common.update', 'Cập nhật') : t('common.create', 'Tạo mới')}
            </Button>
          </div>
        )}
        {readOnly && (
          <div className="flex justify-end pt-4">
            <Button variant="outline" onClick={onCancel}>
              {t('common.close', 'Đóng')}
            </Button>
          </div>
        )}
      </form>
    </div>
  );
};

export default URLForm;
