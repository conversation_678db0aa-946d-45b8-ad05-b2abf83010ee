/**
 * <PERSON><PERSON><PERSON> dữ liệu cho URL
 */
export interface Url {
  id: string;
  url: string;
  title: string;
  content: string;
  type?: string;
  tags?: string[];
  ownedBy: number;
  createdAt: number;
  updatedAt: number;
  isActive: boolean;
}

/**
 * Kiểu dữ liệu cho phân trang
 */
export interface PaginationMeta {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Kiểu dữ liệu cho kết quả phân trang
 */
export interface PaginatedResult<T> {
  items: T[];
  meta: PaginationMeta;
}

/**
 * Kiểu dữ liệu cho danh sách URL có phân trang
 */
export type UrlListResponse = PaginatedResult<Url>;

/**
 * Kiểu dữ liệu cho tham số tìm kiếm URL
 */
export interface UrlSearchParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  keyword?: string;
  type?: string;
  tags?: string[];
  userId?: number;
  isActive?: boolean;
}

/**
 * <PERSON><PERSON><PERSON> dữ liệu cho tạo URL mới
 */
export interface CreateUrlParams {
  url: string;
  title: string;
  content: string;
  type?: string;
  tags?: string[];
  ownedBy: number;
  isActive?: boolean;
}

/**
 * Kiểu dữ liệu cho cập nhật URL
 */
export interface UpdateUrlParams {
  url?: string;
  title?: string;
  content?: string;
  type?: string;
  tags?: string[];
  ownedBy?: number;
  isActive?: boolean;
}

/**
 * Kiểu dữ liệu cho crawl URL
 */
export interface CrawlUrlParams {
  url: string;
  depth: number;
  ignoreRobotsTxt?: boolean;
  maxUrls?: number;
  ownedBy?: number;
}

/**
 * Kiểu dữ liệu cho kết quả crawl URL
 */
export interface CrawlUrlResult {
  status: string;
  message: string;
  urlsProcessed: number;
  urlsCreated: number;
  urlsSkipped: number;
  urlsWithErrors: number;
  urls: Url[];
}
