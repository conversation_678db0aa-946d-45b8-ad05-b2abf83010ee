/**
 * <PERSON><PERSON>t để cài đặt các plugin ESLint bổ sung
 * 
 * <PERSON><PERSON><PERSON> sử dụng:
 * - <PERSON><PERSON><PERSON> đặt tất cả: node scripts/install-eslint-plugins.js all
 * - Cài đặt một plugin: node scripts/install-eslint-plugins.js accessibility
 * 
 * Các plugin có sẵn:
 * - accessibility: eslint-plugin-jsx-a11y
 * - security: eslint-plugin-security
 * - quality: eslint-plugin-sonarjs
 * - performance: eslint-plugin-react-perf
 * - i18n: eslint-plugin-i18n-json
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Danh sách các plugin
const plugins = {
  accessibility: 'eslint-plugin-jsx-a11y',
  security: 'eslint-plugin-security',
  quality: 'eslint-plugin-sonarjs',
  performance: 'eslint-plugin-react-perf',
  i18n: 'eslint-plugin-i18n-json',
};

// Hàm cài đặt plugin
function installPlugin(plugin) {
  try {
    console.log(`Đang cài đặt ${plugin}...`);
    execSync(`npm install --save-dev ${plugin}`, { stdio: 'inherit' });
    console.log(`Đã cài đặt thành công ${plugin}`);
    return true;
  } catch (error) {
    console.error(`Lỗi khi cài đặt ${plugin}:`, error.message);
    return false;
  }
}

// Hàm kích hoạt plugin trong file .eslintrc.json
function activatePlugin(pluginName) {
  const eslintrcPath = path.join(process.cwd(), '.eslintrc.json');
  
  try {
    // Đọc file .eslintrc.json
    let eslintrc = fs.readFileSync(eslintrcPath, 'utf8');
    
    // Xác định plugin cần kích hoạt
    let shortName = '';
    switch (pluginName) {
      case 'eslint-plugin-jsx-a11y':
        shortName = 'jsx-a11y';
        break;
      case 'eslint-plugin-security':
        shortName = 'security';
        break;
      case 'eslint-plugin-sonarjs':
        shortName = 'sonarjs';
        break;
      case 'eslint-plugin-react-perf':
        shortName = 'react-perf';
        break;
      case 'eslint-plugin-i18n-json':
        shortName = 'i18n-json';
        break;
      default:
        return false;
    }
    
    // Kích hoạt plugin trong phần extends
    eslintrc = eslintrc.replace(
      `// "plugin:${shortName}/recommended",`,
      `"plugin:${shortName}/recommended",`
    );
    
    // Kích hoạt plugin trong phần plugins
    eslintrc = eslintrc.replace(
      `// "${shortName}",`,
      `"${shortName}",`
    );
    
    // Kích hoạt các quy tắc
    const rulePattern = new RegExp(`// "${shortName}/[^"]+": "error"`, 'g');
    eslintrc = eslintrc.replace(rulePattern, (match) => match.replace('// ', ''));
    
    // Ghi lại file .eslintrc.json
    fs.writeFileSync(eslintrcPath, eslintrc, 'utf8');
    console.log(`Đã kích hoạt plugin ${shortName} trong .eslintrc.json`);
    return true;
  } catch (error) {
    console.error(`Lỗi khi kích hoạt plugin ${pluginName}:`, error.message);
    return false;
  }
}

// Xử lý tham số dòng lệnh
const arg = process.argv[2];

if (!arg) {
  console.log('Vui lòng cung cấp tham số:');
  console.log('- all: Cài đặt tất cả các plugin');
  console.log('- accessibility: Cài đặt plugin jsx-a11y');
  console.log('- security: Cài đặt plugin security');
  console.log('- quality: Cài đặt plugin sonarjs');
  console.log('- performance: Cài đặt plugin react-perf');
  console.log('- i18n: Cài đặt plugin i18n-json');
  process.exit(1);
}

if (arg === 'all') {
  // Cài đặt tất cả các plugin
  console.log('Đang cài đặt tất cả các plugin...');
  const allPlugins = Object.values(plugins);
  const installCommand = `npm install --save-dev ${allPlugins.join(' ')}`;
  
  try {
    execSync(installCommand, { stdio: 'inherit' });
    console.log('Đã cài đặt thành công tất cả các plugin');
    
    // Kích hoạt tất cả các plugin
    allPlugins.forEach(plugin => {
      activatePlugin(plugin);
    });
  } catch (error) {
    console.error('Lỗi khi cài đặt các plugin:', error.message);
  }
} else if (plugins[arg]) {
  // Cài đặt plugin cụ thể
  const plugin = plugins[arg];
  const success = installPlugin(plugin);
  
  if (success) {
    activatePlugin(plugin);
  }
} else {
  console.error(`Plugin không hợp lệ: ${arg}`);
  console.log('Các plugin có sẵn:');
  Object.keys(plugins).forEach(key => {
    console.log(`- ${key}: ${plugins[key]}`);
  });
}
