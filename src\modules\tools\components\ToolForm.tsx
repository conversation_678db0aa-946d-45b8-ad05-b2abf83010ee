import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { <PERSON>ton, Card, Typography, Loading, Chip } from '@/shared/components/common';
import { ToolDetail } from '../types/tool.types';
import { ToolStatus } from '../types/common.types';

interface ToolFormProps {
  tool?: ToolDetail;
  isLoading?: boolean;
  readOnly?: boolean;
  onClose: () => void;
  onUpdateFromAdmin?: (toolId: string, adminVersionId: string) => void;
  onRollbackToAdmin?: (toolId: string, adminVersionId: string) => void;
  onEditVersion?: (toolId: string, versionId: string) => void;
}

/**
 * Component form xem chi tiết tool
 */
const ToolForm: React.FC<ToolFormProps> = ({
  tool,
  isLoading = false,
  // readOnly không sử dụng trong component này
  onClose,
  onUpdateFromAdmin,
  onRollbackToAdmin,
  onEditVersion,
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('info');

  // Xác định variant cho status chip
  const getStatusVariant = (
    status: ToolStatus
  ): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
    switch (status) {
      case ToolStatus.APPROVED:
        return 'success';
      case ToolStatus.DRAFT:
        return 'warning';
      case ToolStatus.DEPRECATED:
        return 'danger';
      default:
        return 'primary';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading size="lg" />
      </div>
    );
  }

  if (!tool) {
    return (
      <Card className="p-6">
        <Typography variant="h5" className="text-center text-red-500">
          {t('tools.notFound', 'Không tìm thấy công cụ')}
        </Typography>
        <div className="flex justify-center mt-4">
          <Button variant="primary" onClick={onClose}>
            {t('common.close', 'Đóng')}
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex justify-between items-center">
          <Typography variant="h5" className="font-bold">
            {tool.name}
          </Typography>
        </div>
        <Typography variant="body2" className="text-gray-600 mt-1">
          {tool.description || t('tools.noDescription', 'Không có mô tả')}
        </Typography>
        <div className="mt-2 flex flex-wrap gap-2">
          <Chip variant={getStatusVariant(tool.status)} size="sm" className="font-normal">
            {tool.status === ToolStatus.APPROVED
              ? t('tools.status.approved', 'Đã duyệt')
              : tool.status === ToolStatus.DRAFT
                ? t('tools.status.draft', 'Bản nháp')
                : t('tools.status.deprecated', 'Không dùng')}
          </Chip>
          {tool.hasUpdate && (
            <Chip variant="info" size="sm" className="font-normal">
              {t('tools.hasUpdate', 'Có cập nhật')}
            </Chip>
          )}
          {tool.groupName && (
            <Chip variant="primary" size="sm" className="font-normal">
              {tool.groupName}
            </Chip>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="px-4 pt-4 border-b">
        <div className="flex space-x-4">
          <button
            className={`pb-2 px-1 ${
              activeTab === 'info'
                ? 'text-primary border-b-2 border-primary font-medium'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('info')}
          >
            {t('tools.info', 'Thông tin')}
          </button>
          <button
            className={`pb-2 px-1 ${
              activeTab === 'versions'
                ? 'text-primary border-b-2 border-primary font-medium'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('versions')}
          >
            {t('tools.versions', 'Phiên bản')}
          </button>
         
        </div>
      </div>

      {/* Tab content */}
      <div className="flex-grow overflow-auto p-4">
        {activeTab === 'info' && (
          <div className="space-y-4">
            <Typography variant="h6">{t('tools.toolInfo', 'Thông tin công cụ')}</Typography>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Typography variant="subtitle2" className="text-gray-500">
                  {t('tools.id', 'ID')}
                </Typography>
                <Typography>{tool.id}</Typography>
              </div>
              <div>
                <Typography variant="subtitle2" className="text-gray-500">
                  {t('tools.createdAt', 'Ngày tạo')}
                </Typography>
                <Typography>{new Date(tool.createdAt).toLocaleDateString()}</Typography>
              </div>
              <div>
                <Typography variant="subtitle2" className="text-gray-500">
                  {t('tools.updatedAt', 'Cập nhật lần cuối')}
                </Typography>
                <Typography>{new Date(tool.updatedAt).toLocaleDateString()}</Typography>
              </div>
              <div>
                <Typography variant="subtitle2" className="text-gray-500">
                  {t('tools.originalId', 'ID gốc từ Admin')}
                </Typography>
                <Typography>{tool.originalId || t('tools.noOriginalId', 'Không có')}</Typography>
              </div>
            </div>

            {tool.hasUpdate && tool.originalId && onUpdateFromAdmin && (
              <div className="mt-4">
                <Button
                  variant="primary"
                  onClick={() => onUpdateFromAdmin(tool.id, tool.originalId || '')}
                >
                  {t('tools.updateFromAdmin', 'Cập nhật từ Admin')}
                </Button>
              </div>
            )}
          </div>
        )}

        {activeTab === 'versions' && (
          <div className="space-y-4">
            <Typography variant="h6">{t('tools.versions', 'Phiên bản')}</Typography>
            <div className="space-y-4">
              {tool.versions.map(version => (
                <Card key={version.id} className="p-4 border">
                  <div className="flex justify-between items-start">
                    <div>
                      <Typography variant="subtitle1" className="font-semibold">
                        {t('tools.version', 'Phiên bản')} {version.versionNumber}
                        {tool.defaultVersion?.id === version.id && (
                          <span className="ml-2 text-green-600">
                            ({t('tools.default', 'Mặc định')})
                          </span>
                        )}
                      </Typography>
                      <Typography variant="body2" className="mt-1">
                        {version.toolName}
                      </Typography>
                      <Typography variant="body2" className="mt-1">
                        {version.versionName}
                      </Typography>
                      <Typography variant="body2" className="text-gray-600 mt-1">
                        {version.toolDescription || t('tools.noDescription', 'Không có mô tả')}
                      </Typography>
                      {version.changeDescription && (
                        <Typography variant="body2" className="text-gray-600 mt-2">
                          <span className="font-semibold">
                            {t('tools.changeDescription', 'Mô tả thay đổi')}:
                          </span>{' '}
                          {version.changeDescription}
                        </Typography>
                      )}
                      <Typography variant="caption" className="block mt-2 text-gray-500">
                        {t('tools.createdAt', 'Ngày tạo')}:{' '}
                        {new Date(version.createdAt).toLocaleDateString()}
                      </Typography>
                    </div>
                    <div className="flex space-x-2">
                      {version.edited && onRollbackToAdmin && tool.originalId && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onRollbackToAdmin(tool.id, tool.originalId || '')}
                        >
                          {t('tools.rollback', 'Khôi phục gốc')}
                        </Button>
                      )}
                      {onEditVersion && (
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={() => onEditVersion(tool.id, version.id)}
                        >
                          {t('common.edit', 'Chỉnh sửa')}
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

       
      </div>

      {/* Footer */}
      <div className="p-4 border-t mt-auto">
        <div className="flex justify-end">
          <Button variant="outline" onClick={onClose}>
            {t('common.close', 'Đóng')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ToolForm;
