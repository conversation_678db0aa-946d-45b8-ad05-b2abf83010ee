import { z } from 'zod';
import { ToolStatus, AccessType, ToolSortBy } from '../types/tool.types';

/**
 * Schema cho tham số truy vấn danh sách tool
 */
export const toolQuerySchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  status: z.nativeEnum(ToolStatus).optional(),
  accessType: z.nativeEnum(AccessType).optional(),
  sortBy: z.nativeEnum(ToolSortBy).optional().default(ToolSortBy.CREATED_AT),
  sortDirection: z.enum(['ASC', 'DESC']).optional().default('DESC'),
});

/**
 * Schema cho tham số của tool
 */
export const toolParameterSchema = z.object({
  name: z.string().min(1).max(50),
  type: z.string().min(1),
  description: z.string().optional(),
  required: z.boolean(),
  defaultValue: z.any().optional(),
  options: z.array(z.any()).optional(),
});

/**
 * Schema cho dữ liệu tạo tool mới
 */
export const createToolSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  toolName: z.string().min(1).max(64),
  toolDescription: z.string().optional(),
  parameters: z.record(z.any()),
  status: z.nativeEnum(ToolStatus).optional().default(ToolStatus.DRAFT),
  accessType: z.nativeEnum(AccessType).optional().default(AccessType.PUBLIC),
});

/**
 * Schema cho dữ liệu cập nhật tool
 */
export const updateToolSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  status: z.nativeEnum(ToolStatus).optional(),
  accessType: z.nativeEnum(AccessType).optional(),
});

/**
 * Schema cho dữ liệu tạo phiên bản tool mới
 */
export const createToolVersionSchema = z.object({
  toolName: z.string().min(1).max(64),
  toolDescription: z.string().optional(),
  parameters: z.record(z.any()),
  changeDescription: z.string().optional(),
  status: z.nativeEnum(ToolStatus).optional().default(ToolStatus.DRAFT),
});

/**
 * Schema cho dữ liệu cập nhật phiên bản tool
 */
export const updateToolVersionSchema = z.object({
  toolName: z.string().min(1).max(64).optional(),
  toolDescription: z.string().optional(),
  parameters: z.record(z.any()).optional(),
  changeDescription: z.string().optional(),
  status: z.nativeEnum(ToolStatus).optional(),
});

/**
 * Schema cho tham số truy vấn danh sách nhóm tool
 */
export const toolGroupQuerySchema = z.object({
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt']).optional().default('createdAt'),
  sortDirection: z.enum(['ASC', 'DESC']).optional().default('DESC'),
});

/**
 * Schema cho dữ liệu tạo nhóm tool mới
 */
export const createToolGroupSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  toolIds: z.array(z.string().uuid()).optional(),
});

/**
 * Schema cho dữ liệu cập nhật nhóm tool
 */
export const updateToolGroupSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
});

/**
 * Schema cho dữ liệu cập nhật danh sách tool trong nhóm
 */
export const updateToolGroupToolsSchema = z.object({
  toolIds: z.array(z.string().uuid()),
});
