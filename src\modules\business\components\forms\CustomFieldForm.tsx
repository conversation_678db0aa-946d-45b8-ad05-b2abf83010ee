import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Toggle,
  Card,
  Textarea,
} from '@/shared/components/common';
import { z } from 'zod';
import { useCreateCustomField, useUpdateCustomField } from '../../hooks/useCustomFieldQuery';
import { CustomFieldDetail, CreateCustomFieldData } from '../../services/custom-field.service';

interface CustomFieldFormProps {
  onSubmit: () => void;
  onCancel: () => void;
  initialData?: CustomFieldDetail;
  isSubmitting?: boolean;
}

/**
 * Form tạo và chỉnh sửa trường tùy chỉnh
 */
const CustomFieldForm: React.FC<CustomFieldFormProps> = ({
  onSubmit,
  onCancel,
  initialData,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const { mutateAsync: createCustomField, isPending: isCreating } = useCreateCustomField();
  const { mutateAsync: updateCustomField, isPending: isUpdating } = useUpdateCustomField();

  // Schema cho form
  const customFieldSchema = z.object({
    component: z.string().min(1, t('business:customField.form.componentRequired')),
    label: z.string().min(1, t('business:customField.form.labelRequired')),
    type: z.string().min(1, t('business:customField.form.typeRequired')),
    required: z.boolean().optional(),
    placeholder: z.string().optional(),
    defaultValue: z.string().optional(),
    description: z.string().optional(),
    validation: z.object({
      minLength: z.string().optional(),
      maxLength: z.string().optional(),
      pattern: z.string().optional(),
    }).optional(),
    options: z.string().optional(),
  });

  // Giá trị mặc định cho form
  const defaultValues = {
    component: 'input',
    label: '',
    type: 'text',
    required: false,
    placeholder: '',
    defaultValue: '',
    description: '',
    validation: {
      minLength: '',
      maxLength: '',
      pattern: '',
    },
    options: '',
  };

  // Định nghĩa kiểu dữ liệu cho form values
  type CustomFieldFormValues = z.infer<typeof customFieldSchema>;

  // Xử lý khi submit form
  const handleSubmit = async (values: CustomFieldFormValues) => {
    try {
      // Chuẩn bị dữ liệu cho API
      const formData: CreateCustomFieldData = {
        component: String(values.component),
        config: {
          label: String(values.label),
          type: String(values.type),
          required: Boolean(values.required),
          placeholder: values.placeholder ? String(values.placeholder) : undefined,
          defaultValue: values.defaultValue ? String(values.defaultValue) : undefined,
          description: values.description ? String(values.description) : undefined,
          validation: {
            minLength: values.validation && values.validation.minLength ? Number(values.validation.minLength) : undefined,
            maxLength: values.validation && values.validation.maxLength ? Number(values.validation.maxLength) : undefined,
            pattern: values.validation && values.validation.pattern ? String(values.validation.pattern) : undefined,
          },
        },
      };

      // Thêm options nếu có
      if (values.options && ['select', 'radio', 'checkbox', 'multi-select'].includes(String(values.component))) {
        try {
          // Parse options từ chuỗi JSON hoặc từ danh sách các giá trị ngăn cách bởi dấu phẩy
          const optionsString = String(values.options);
          const optionsArray = optionsString.includes('{')
            ? JSON.parse(optionsString)
            : optionsString.split(',').map(option => ({
                label: option.trim(),
                value: option.trim().toLowerCase().replace(/\s+/g, '_'),
              }));

          formData.config.options = optionsArray;
        } catch (error) {
          console.error('Error parsing options:', error);
        }
      }

      if (initialData) {
        // Cập nhật trường tùy chỉnh
        await updateCustomField({
          id: initialData.id,
          data: formData,
        });
      } else {
        // Tạo trường tùy chỉnh mới
        await createCustomField(formData);
      }

      // Gọi callback onSubmit
      onSubmit();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  // Chuẩn bị giá trị mặc định từ dữ liệu ban đầu
  const getInitialValues = () => {
    if (!initialData) return defaultValues;

    // Lấy dữ liệu validation từ configJson
    const validation = initialData.configJson?.validation as Record<string, unknown> | undefined;

    return {
      component: initialData.component,
      label: initialData.label,
      type: initialData.type,
      required: initialData.required,
      placeholder: initialData.configJson?.placeholder as string || '',
      defaultValue: initialData.configJson?.defaultValue as string || '',
      description: initialData.configJson?.description as string || '',
      validation: {
        minLength: validation?.minLength ? String(validation.minLength) : '',
        maxLength: validation?.maxLength ? String(validation.maxLength) : '',
        pattern: validation?.pattern ? String(validation.pattern) : '',
      },
      options: initialData.configJson?.options
        ? JSON.stringify(initialData.configJson.options)
        : '',
    };
  };

  return (
    <Card title={initialData ? t('business:customField.edit') : t('business:customField.add')}>
      <Form
        schema={customFieldSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        defaultValues={getInitialValues()}
        className="p-4 space-y-4"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem
            name="component"
            label={t('business:customField.component')}
            required
          >
            <Select
              fullWidth
              options={[
                { value: 'input', label: t('business:customField.components.input') },
                { value: 'textarea', label: t('business:customField.components.textarea') },
                { value: 'select', label: t('business:customField.components.select') },
                { value: 'checkbox', label: t('business:customField.components.checkbox') },
                { value: 'radio', label: t('business:customField.components.radio') },
                { value: 'date', label: t('business:customField.components.date') },
                { value: 'number', label: t('business:customField.components.number') },
                { value: 'file', label: t('business:customField.components.file') },
                { value: 'multi-select', label: t('business:customField.components.multiSelect') },
              ]}
            />
          </FormItem>

          <FormItem
            name="type"
            label={t('business:customField.type')}
            required
          >
            <Select
              fullWidth
              options={[
                { value: 'text', label: t('business:customField.types.text') },
                { value: 'number', label: t('business:customField.types.number') },
                { value: 'boolean', label: t('business:customField.types.boolean') },
                { value: 'date', label: t('business:customField.types.date') },
                { value: 'object', label: t('business:customField.types.object') },
                { value: 'array', label: t('business:customField.types.array') },
              ]}
            />
          </FormItem>
        </div>

        <FormItem
          name="label"
          label={t('business:customField.label')}
          required
        >
          <Input fullWidth placeholder={t('business:customField.form.labelPlaceholder')} />
        </FormItem>

        <FormItem
          name="description"
          label={t('business:customField.description')}
        >
          <Textarea
            fullWidth
            rows={3}
            placeholder={t('business:customField.form.descriptionPlaceholder')}
          />
        </FormItem>

        <FormItem
          name="placeholder"
          label={t('business:customField.placeholder')}
        >
          <Input fullWidth placeholder={t('business:customField.form.placeholderPlaceholder')} />
        </FormItem>

        <FormItem
          name="defaultValue"
          label={t('business:customField.defaultValue')}
        >
          <Input fullWidth placeholder={t('business:customField.form.defaultValuePlaceholder')} />
        </FormItem>

        <FormItem
          name="options"
          label={t('business:customField.options')}
        >
          <Textarea
            fullWidth
            rows={3}
            placeholder={t('business:customField.form.optionsPlaceholder')}
          />
        </FormItem>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormItem
            name="validation.minLength"
            label={t('business:customField.validation.minLength')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>

          <FormItem
            name="validation.maxLength"
            label={t('business:customField.validation.maxLength')}
          >
            <Input fullWidth type="number" min="0" placeholder="100" />
          </FormItem>

          <FormItem
            name="validation.pattern"
            label={t('business:customField.validation.pattern')}
          >
            <Input fullWidth placeholder="^[A-Za-z0-9]+$" />
          </FormItem>
        </div>

        <FormItem
          name="required"
          label={t('business:customField.required')}
        >
          <Toggle />
        </FormItem>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting || isCreating || isUpdating}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            isLoading={isSubmitting || isCreating || isUpdating}
          >
            {t('common:save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default CustomFieldForm;
