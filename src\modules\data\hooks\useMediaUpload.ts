import { useCallback } from 'react';
import { useCorsAwareFileUpload } from '@/shared/hooks/common/useCorsAwareFileUpload';
import { MEDIA_QUERY_KEYS } from '@/modules/data/media/hooks/useMediaQuery';
import { useQueryClient } from '@tanstack/react-query';
import { NotificationUtil } from '@/shared/utils/notification';
import { apiClient } from '@/shared/api/axios';

// Định nghĩa interface cho MediaUploadDto
interface MediaUploadDto {
  name: string;
  description?: string; // Làm cho description trở thành optional
  size: number;
  tags: string[];
  type: string;
  viewUrl: string;
  file: File;
}

/**
 * Hook để upload media với TaskQueue
 */
export const useMediaUpload = () => {
  // Không cần hook useCreatePresignedUrl vì chúng ta gọi API trực tiếp

  // Query client để cập nhật cache
  const queryClient = useQueryClient();

  // <PERSON> để upload file với TaskQueue và xử lý lỗi CORS
  const fileUploadWithQueue = useCorsAwareFileUpload({
    defaultTaskTitle: 'Upload media',
    autoAddToQueue: true,
  });

  /**
   * Upload media với TaskQueue
   */
  const uploadMedia = useCallback(
    async (mediaList: MediaUploadDto[]) => {
      try {
        if (!mediaList || !Array.isArray(mediaList) || mediaList.length === 0) {
          throw new Error('Danh sách media không hợp lệ');
        }

        // Chuẩn bị dữ liệu để gửi đến API
        // API mong đợi một mảng MediaDto
        const mediaDataList = mediaList.map(media => ({
          name: media.name,
          description: media.description,
          size: media.size,
          tags: media.tags || [],
          type: media.type,
          viewUrl: media.viewUrl
        }));

        // Gọi API trực tiếp vì useCreatePresignedUrl không hỗ trợ mảng
        const response = await apiClient.post('/media/presigned-urls', mediaDataList);

        // Lấy danh sách URL từ response
        const presignedUrls = response?.result || [];

        if (!presignedUrls || !Array.isArray(presignedUrls) || presignedUrls.length === 0) {
          throw new Error('Không nhận được URL tải lên từ server');
        }

        // Upload từng file lên URL tạm thời
        const uploadPromises = presignedUrls.map((urlInfo, index) => {
          const mediaData = mediaList[index];

          // Lấy file từ mediaData
          const { file } = mediaData;

          if (!file || !(file instanceof File)) {
            throw new Error(`Không tìm thấy file hợp lệ cho media ${index + 1}`);
          }

          return fileUploadWithQueue.uploadToUrlWithQueue({
            file,
            presignedUrl: urlInfo,
            taskTitle: `Upload: ${mediaData.name}`,
            taskDescription: `Kích thước: ${(mediaData.size / 1024).toFixed(1)} KB`,
          });
        });

        // Chờ tất cả các file upload xong
        await Promise.all(uploadPromises);

        // Cập nhật lại danh sách media sau khi upload xong
        queryClient.invalidateQueries({ queryKey: MEDIA_QUERY_KEYS.lists() });

        // Hiển thị thông báo thành công
        NotificationUtil.success({
          message: 'Tải lên media thành công',
          duration: 3000,
        });

        return presignedUrls;
      } catch (error) {
        console.error('Lỗi khi upload media:', error);

        // Hiển thị thông báo lỗi
        NotificationUtil.error({
          message: 'Có lỗi xảy ra khi tải lên media. Vui lòng thử lại sau.',
          duration: 5000,
        });

        throw error;
      }
    },
    [fileUploadWithQueue, queryClient]
  );

  return {
    uploadMedia,
    isUploading: false, // Không cần theo dõi trạng thái isUploading vì TaskQueue đã xử lý
  };
};

export default useMediaUpload;
