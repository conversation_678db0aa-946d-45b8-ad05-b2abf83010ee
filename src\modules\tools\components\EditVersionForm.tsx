import React from 'react';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import {
  Button,
  
  Textarea,
  Select,
  Typography,
} from '@/shared/components/common';
import { ToolStatus } from '../types/common.types';
import { EditUserToolVersionParams, UserToolVersion } from '../types/user-tool.types';

// Schema validation cho form
const editVersionSchema = z.object({
  toolName: z.string().min(1, 'Tool name is required'),
  toolDescription: z.string().optional(),
  parameters: z.record(z.unknown()),
  changeDescription: z.string().optional(),
  status: z.nativeEnum(ToolStatus).optional(),
});

type EditVersionFormData = z.infer<typeof editVersionSchema>;

interface EditVersionFormProps {
  version?: UserToolVersion;
  isLoading?: boolean;
  onSubmit: (data: EditUserToolVersionParams) => void;
  onCancel: () => void;
}

/**
 * Component form chỉnh sửa phiên bản tool
 */
const EditVersionForm: React.FC<EditVersionFormProps> = ({
  version,
  isLoading = false,
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation(['tools']);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    control,
  } = useForm<EditVersionFormData>({
    resolver: zodResolver(editVersionSchema),
    defaultValues: {
      toolName: version?.toolName || '',
      toolDescription: version?.toolDescription || '',
      parameters: version?.parameters || {},
      changeDescription: version?.changeDescription || '',
      status: version?.status || ToolStatus.DRAFT,
    },
  });

  const parametersValue = watch('parameters');

  const handleFormSubmit = (data: EditVersionFormData) => {
    onSubmit({
      toolName: data.toolName,
      toolDescription: data.toolDescription,
      parameters: data.parameters,
      changeDescription: data.changeDescription,
      status: data.status,
    });
  };

  const handleParametersChange = (value: string) => {
    try {
      const parsed = JSON.parse(value);
      setValue('parameters', parsed);
    } catch (error) {
      // Invalid JSON, keep the string value for user to fix
      console.warn('Invalid JSON in parameters:', error);
    }
  };

  const statusOptions = [
    { value: ToolStatus.DRAFT, label: t('tools:status.draft', 'Bản nháp') },
    { value: ToolStatus.APPROVED, label: t('tools:status.approved', 'Đã duyệt') },
    { value: ToolStatus.DEPRECATED, label: t('tools:status.deprecated', 'Không dùng') },
  ];

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b">
        <Typography variant="h5" className="font-bold">
          {version
            ? t('tools:editVersion', 'Chỉnh sửa phiên bản')
            : t('tools:createVersion', 'Tạo phiên bản mới')
          }
        </Typography>
        {version && (
          <Typography variant="body2" className="text-gray-600 mt-1">
            {t('tools:version', 'Phiên bản')} {version.versionNumber}
          </Typography>
        )}
      </div>

      {/* Form content */}
      <div className="flex-grow overflow-auto p-4">
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
         

          {/* Tool Description */}
          <div className="space-y-1">
            <label className="text-sm font-medium text-foreground">
              {t('tools:form.toolDescription', 'Mô tả công cụ')}
            </label>
            <Textarea
              {...register('toolDescription')}
              placeholder={t('tools:form.toolDescriptionPlaceholder', 'Nhập mô tả công cụ')}
              rows={3}
              disabled={isLoading}
            />
            {errors.toolDescription && (
              <p className="text-sm text-red-500">{errors.toolDescription.message}</p>
            )}
          </div>

          {/* Parameters */}
          <div className="space-y-1">
            <label className="text-sm font-medium text-foreground">
              {t('tools:form.parameters', 'Tham số (JSON)')}
            </label>
            <Textarea
              value={JSON.stringify(parametersValue, null, 2)}
              onChange={(e) => handleParametersChange(e.target.value)}
              placeholder={t('tools:form.parametersPlaceholder', 'Nhập tham số dưới dạng JSON')}
              rows={6}
              disabled={isLoading}
              className="font-mono text-sm"
            />
            {errors.parameters && (
              <p className="text-sm text-red-500">{String(errors.parameters.message || 'Invalid parameters')}</p>
            )}
          </div>

          {/* Change Description */}
          <div className="space-y-1">
            <label className="text-sm font-medium text-foreground">
              {t('tools:form.changeDescription', 'Mô tả thay đổi')}
            </label>
            <Textarea
              {...register('changeDescription')}
              placeholder={t('tools:form.changeDescriptionPlaceholder', 'Mô tả những thay đổi trong phiên bản này')}
              rows={3}
              disabled={isLoading}
            />
            {errors.changeDescription && (
              <p className="text-sm text-red-500">{errors.changeDescription.message}</p>
            )}
          </div>

          {/* Status */}
          <div className="space-y-1">
            <label className="text-sm font-medium text-foreground">
              {t('tools:form.status', 'Trạng thái')}
            </label>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <Select
                  value={field.value}
                  onChange={(value) => field.onChange(value)}
                  options={statusOptions}
                  placeholder={t('tools:form.statusPlaceholder', 'Chọn trạng thái')}
                  disabled={isLoading}
                />
              )}
            />
            {errors.status && (
              <p className="text-sm text-red-500">{errors.status.message}</p>
            )}
          </div>
        </form>
      </div>

      {/* Footer */}
      <div className="p-4 border-t">
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            {t('tools:common.cancel', 'Hủy')}
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit(handleFormSubmit)}
            disabled={isLoading}
          >
            {isLoading
              ? t('tools:common.saving', 'Đang lưu...')
              : t('tools:common.save', 'Lưu')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default EditVersionForm;
