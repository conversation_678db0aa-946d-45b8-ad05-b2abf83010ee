import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AgentGrid } from '../components';
import { aiAgents, AIAgent } from '../data/agents';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';

/**
 * Trang hiển thị danh sách AI Agents
 */
const AIAgentsPage: React.FC = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  const handleSelectAgent = (agent: AIAgent) => {
    console.log('Selected agent:', agent);
    // Xử lý logic khi chọn agent
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const handleFilterChange = (type: string) => {
    setFilterType(type);
  };

  const handleAddAgent = () => {
    console.log('Add new agent');
    // Xử lý logic khi thêm agent mới
  };

  // Lọc agents theo từ khóa tìm kiếm và loại
  const filteredAgents = aiAgents.filter(agent => {
    const matchesSearch =
      agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      agent.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      agent.category.toLowerCase().includes(searchTerm.toLowerCase());

    if (filterType === 'all') return matchesSearch;

    // Lọc theo category
    if (filterType === 'assistant') {
      // Chỉ hiển thị Assistant (category = general)
      return matchesSearch && agent.category === 'general';
    } else {
      // Hiển thị tất cả các agents khác (không phải general)
      return matchesSearch && agent.category !== 'general';
    }
  });

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddAgent}
        items={[
          {
            id: 'all',
            label: t('common.all'),
            icon: 'list',
            onClick: () => handleFilterChange('all'),
          },
          {
            id: 'assistant',
            label: t('chat.aiAssistants'),
            icon: 'assistant',
            onClick: () => handleFilterChange('assistant'),
          },
          {
            id: 'agent',
            label: t('chat.specializedAgents', 'Specialized Agents'),
            icon: 'robot',
            onClick: () => handleFilterChange('agent'),
          },
        ]}
      />

      {filteredAgents.length > 0 ? (
        <AgentGrid agents={filteredAgents} onSelectAgent={handleSelectAgent} />
      ) : (
        <div className="flex flex-col items-center justify-center h-64">
          <p className="text-gray-500 dark:text-gray-400">{t('common.noResults')}</p>
        </div>
      )}
    </div>
  );
};

export default AIAgentsPage;
