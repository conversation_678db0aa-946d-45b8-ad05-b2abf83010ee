import React from 'react';
import { Input, Typography } from '@/shared/components/common';
import { EmailElement } from '../types';

interface LayoutEditorProps {
  selectedElement: EmailElement;
  updateSelectedElement: (property: string, value: unknown) => void;
}

const LayoutEditor: React.FC<LayoutEditorProps> = ({
  selectedElement,
  updateSelectedElement
}) => {
  return (
    <div className="space-y-4">
      {/* Dimensions Section */}
      <div className="p-4 rounded-md">
        <Typography variant="body1" className="font-medium mb-2">Dimensions</Typography>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <Typography variant="body2" className="text-xs mb-1">Width</Typography>
            <div className="flex items-center">
              <Input
                value={selectedElement.style?.width || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  // Kiểm tra nếu là số thì thêm đơn vị px, nếu không thì giữ nguyên (có thể là %, auto, etc.)
                  const finalValue = !isNaN(Number(value)) && value !== '' ? Number(value) : value;
                  updateSelectedElement('style.width', finalValue);
                }}
                className="w-full"
                placeholder="auto"
              />
            </div>
          </div>
          
          <div>
            <Typography variant="body2" className="text-xs mb-1">Height</Typography>
            <div className="flex items-center">
              <Input
                value={selectedElement.style?.height || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  const finalValue = !isNaN(Number(value)) && value !== '' ? Number(value) : value;
                  updateSelectedElement('style.height', finalValue);
                }}
                className="w-full"
                placeholder="auto"
              />
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Typography variant="body2" className="text-xs mb-1">Max Width</Typography>
            <div className="flex items-center">
              <Input
                value={selectedElement.style?.maxWidth || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  const finalValue = !isNaN(Number(value)) && value !== '' ? Number(value) : value;
                  updateSelectedElement('style.maxWidth', finalValue);
                }}
                className="w-full"
                placeholder="none"
              />
            </div>
          </div>
          
          <div>
            <Typography variant="body2" className="text-xs mb-1">Max Height</Typography>
            <div className="flex items-center">
              <Input
                value={selectedElement.style?.maxHeight || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  const finalValue = !isNaN(Number(value)) && value !== '' ? Number(value) : value;
                  updateSelectedElement('style.maxHeight', finalValue);
                }}
                className="w-full"
                placeholder="none"
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Spacing Section */}
      <div className="p-4 rounded-md">
        <Typography variant="body1" className="font-medium mb-2">Spacing</Typography>
        
        <div className="mb-4">
          <Typography variant="body2" className="text-xs mb-1">Padding</Typography>
          <div className="grid grid-cols-4 gap-2">
            <div>
              <Typography variant="body2" className="text-xs text-center mb-1">Top</Typography>
              <Input
                type="number"
                value={selectedElement.style?.paddingTop || 0}
                onChange={(e) => updateSelectedElement('style.paddingTop', parseInt(e.target.value) || 0)}
                className="w-full"
              />
            </div>
            <div>
              <Typography variant="body2" className="text-xs text-center mb-1">Right</Typography>
              <Input
                type="number"
                value={selectedElement.style?.paddingRight || 0}
                onChange={(e) => updateSelectedElement('style.paddingRight', parseInt(e.target.value) || 0)}
                className="w-full"
              />
            </div>
            <div>
              <Typography variant="body2" className="text-xs text-center mb-1">Bottom</Typography>
              <Input
                type="number"
                value={selectedElement.style?.paddingBottom || 0}
                onChange={(e) => updateSelectedElement('style.paddingBottom', parseInt(e.target.value) || 0)}
                className="w-full"
              />
            </div>
            <div>
              <Typography variant="body2" className="text-xs text-center mb-1">Left</Typography>
              <Input
                type="number"
                value={selectedElement.style?.paddingLeft || 0}
                onChange={(e) => updateSelectedElement('style.paddingLeft', parseInt(e.target.value) || 0)}
                className="w-full"
              />
            </div>
          </div>
        </div>
        
        <div>
          <Typography variant="body2" className="text-xs mb-1">Margin</Typography>
          <div className="grid grid-cols-4 gap-2">
            <div>
              <Typography variant="body2" className="text-xs text-center mb-1">Top</Typography>
              <Input
                type="number"
                value={selectedElement.style?.marginTop || 0}
                onChange={(e) => updateSelectedElement('style.marginTop', parseInt(e.target.value) || 0)}
                className="w-full"
              />
            </div>
            <div>
              <Typography variant="body2" className="text-xs text-center mb-1">Right</Typography>
              <Input
                type="number"
                value={selectedElement.style?.marginRight || 0}
                onChange={(e) => updateSelectedElement('style.marginRight', parseInt(e.target.value) || 0)}
                className="w-full"
              />
            </div>
            <div>
              <Typography variant="body2" className="text-xs text-center mb-1">Bottom</Typography>
              <Input
                type="number"
                value={selectedElement.style?.marginBottom || 0}
                onChange={(e) => updateSelectedElement('style.marginBottom', parseInt(e.target.value) || 0)}
                className="w-full"
              />
            </div>
            <div>
              <Typography variant="body2" className="text-xs text-center mb-1">Left</Typography>
              <Input
                type="number"
                value={selectedElement.style?.marginLeft || 0}
                onChange={(e) => updateSelectedElement('style.marginLeft', parseInt(e.target.value) || 0)}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LayoutEditor;
