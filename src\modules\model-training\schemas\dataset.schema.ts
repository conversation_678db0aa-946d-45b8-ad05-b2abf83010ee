import { z } from 'zod';
import { SortDirection } from '@/shared/dto/request/query.dto';

/**
 * Schema cho message trong dataset
 */
export const DatasetMessageSchema = z.object({
  role: z.enum(['system', 'user', 'assistant']),
  content: z.string().min(1, 'Nội dung không được để trống'),
});

/**
 * Schema cho một conversation trong dataset
 */
export const DatasetConversationSchema = z.object({
  messages: z.array(DatasetMessageSchema).min(1, 'Cần ít nhất một message'),
});

/**
 * Schema cho việc tạo dataset
 */
export const CreateDatasetSchema = z.object({
  name: z.string().min(1, 'Tên không được để trống'),
  description: z.string().min(1, '<PERSON><PERSON> tả không được để trống'),
  trainData: z.array(DatasetConversationSchema).min(1, 'Cần ít nhất một conversation cho dữ liệu huấn luyện'),
  validationData: z.array(DatasetConversationSchema).min(1, 'Cần ít nhất một conversation cho dữ liệu kiểm tra'),
});

/**
 * Schema cho response khi tạo dataset thành công
 */
export const DatasetResponseSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  status: z.enum(['DRAFT', 'PROCESSING', 'COMPLETED', 'FAILED']),
  author: z.string(),
  avatar: z.string(),
});

/**
 * Schema cho query danh sách dataset
 */
export const QueryDatasetSchema = z.object({
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).optional().default(10),
  search: z.string().optional(),
  sortBy: z.string().optional().default('createdAt'),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.DESC),
  status: z.enum(['DRAFT', 'PROCESSING', 'COMPLETED', 'FAILED']).optional(),
});
