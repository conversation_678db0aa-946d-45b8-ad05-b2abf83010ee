import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import {
  OrderDetailResponse,
  OrderFilterParams,
  OrderListResponse,
  OrderStatus,
} from '../types/order.types';

// Đường dẫn API chung cho marketplace
const MARKETPLACE_API_PATH = '/admin/marketplace';

/**
 * Service cho quản lý đơn hàng trong marketplace
 */
export const OrderService = {
  /**
   * Lấy danh sách đơn hàng
   * @param params Tham số filter
   * @returns Promise với danh sách đơn hàng
   */
  getOrders: async (params?: OrderFilterParams): Promise<ApiResponseDto<OrderListResponse>> => {
    const response = await apiClient.get<OrderListResponse>(`${MARKETPLACE_API_PATH}/orders`, {
      params,
    });
    return response;
  },

  /**
   * L<PERSON>y chi tiết đơn hàng
   * @param id ID của đơn hàng
   * @returns Promise với chi tiết đơn hàng
   */
  getOrder: async (id: string): Promise<ApiResponseDto<OrderDetailResponse>> => {
    const response = await apiClient.get<OrderDetailResponse>(
      `${MARKETPLACE_API_PATH}/orders/${id}`
    );
    return response;
  },

  /**
   * Cập nhật trạng thái đơn hàng
   * @param id ID của đơn hàng
   * @param status Trạng thái mới
   * @returns Promise với đơn hàng đã cập nhật
   */
  updateOrderStatus: async (
    id: string,
    status: OrderStatus
  ): Promise<ApiResponseDto<OrderDetailResponse>> => {
    const response = await apiClient.patch<OrderDetailResponse>(
      `${MARKETPLACE_API_PATH}/orders/${id}/status`,
      { status }
    );
    return response;
  },
};
