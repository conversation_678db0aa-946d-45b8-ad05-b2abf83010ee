import React from 'react';
import { AIAgent } from '../data/agents';
import AgentCard from './AgentCard';
import { ResponsiveGrid } from '@/shared/components/common';

interface AgentGridProps {
  agents: AIAgent[];
  onSelectAgent?: (agent: AIAgent) => void;
}

/**
 * Component hiển thị danh sách AI Agents dạng grid
 * Sử dụng ResponsiveGrid để tự động điều chỉnh số cột dựa trên kích thước màn hình và trạng thái chatpanel
 *
 * Responsive:
 * - Mobile (<640px): 1 column
 * - Small Tablet (640px-767px): 1-2 columns (depends on chat panel state)
 * - Tablet (768px-1023px): 2 columns (1 column when chat panel open)
 * - Desktop (1024px-1279px): 2 columns (2 column when chat panel open)
 * - Large Desktop (≥1280px): 3 columns (2 columns when chat panel open)
 */
const AgentGrid: React.FC<AgentGridProps> = ({ agents, onSelectAgent }) => {
  const handleSelectAgent = (agent: AIAgent) => {
    if (onSelectAgent) {
      onSelectAgent(agent);
    }
  };

  return (
    <ResponsiveGrid
      maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
      gap={{ xs: 4, md: 5, lg: 6 }}
    >
      {agents.map(agent => (
        <div key={agent.id} className="h-full" onClick={() => handleSelectAgent(agent)}>
          <AgentCard agent={agent} />
        </div>
      ))}
    </ResponsiveGrid>
  );
};

export default AgentGrid;
