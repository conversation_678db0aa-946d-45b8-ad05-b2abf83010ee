import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { WarehouseService } from '../services/warehouse.service';
import {
  CreateWarehouseDto,
  UpdateWarehouseDto,
  WarehouseQueryParams,
} from '../types/warehouse.types';

// Định nghĩa các query key
export const WAREHOUSE_QUERY_KEYS = {
  all: ['warehouses'] as const,
  lists: () => [...WAREHOUSE_QUERY_KEYS.all, 'list'] as const,
  list: (filters: WarehouseQueryParams) => [...WAREHOUSE_QUERY_KEYS.lists(), filters] as const,
  details: () => [...WAREHOUSE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...WAREHOUSE_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách kho
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useWarehouses = (params?: WarehouseQueryParams) => {
  return useQuery({
    queryKey: WAREHOUSE_QUERY_KEYS.list(params || { page: 1, limit: 10 }),
    queryFn: () => WarehouseService.getWarehouses(params),
  });
};

/**
 * Hook để lấy chi tiết kho theo ID
 * @param id ID của kho
 * @returns Query object
 */
export const useWarehouse = (id: number) => {
  return useQuery({
    queryKey: WAREHOUSE_QUERY_KEYS.detail(id),
    queryFn: () => WarehouseService.getWarehouseById(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo kho mới
 * @returns Mutation object
 */
export const useCreateWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateWarehouseDto) => WarehouseService.createWarehouse(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WAREHOUSE_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để cập nhật kho
 * @returns Mutation object
 */
export const useUpdateWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateWarehouseDto }) =>
      WarehouseService.updateWarehouse(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: WAREHOUSE_QUERY_KEYS.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: WAREHOUSE_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa kho
 * @returns Mutation object
 */
export const useDeleteWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => WarehouseService.deleteWarehouse(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WAREHOUSE_QUERY_KEYS.lists() });
    },
  });
};
