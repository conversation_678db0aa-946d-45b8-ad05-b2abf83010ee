import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Input, Textarea, Select, FormItem } from '@/shared/components/common';
import { ToolGroupDetail, CreateToolGroupParams, UpdateToolGroupParams } from '../types/tool.types';
import { useAdminTools } from '../hooks';

interface ToolGroupFormProps {
  initialValues?: ToolGroupDetail;
  onSubmit: (values: CreateToolGroupParams | UpdateToolGroupParams) => void;
  onCancel: () => void;
  readOnly?: boolean;
  isLoading?: boolean;
  isEdit?: boolean;
}

/**
 * Component form để tạo/chỉnh sửa nhóm tool
 */
const ToolGroupForm: React.FC<ToolGroupFormProps> = ({
  initialValues,
  onSubmit,
  onCancel,
  readOnly = false,
  isLoading = false,
  isEdit = false,
}) => {
  const { t } = useTranslation();

  // State cho form
  const [name, setName] = useState(initialValues?.name || '');
  const [description, setDescription] = useState(initialValues?.description || '');
  const [selectedToolIds, setSelectedToolIds] = useState<string[]>(
    initialValues?.tools?.map(tool => tool.id) || []
  );
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Lấy danh sách tools để hiển thị trong dropdown
  const { data: toolsData } = useAdminTools({
    page: 1,
    limit: 100, // Lấy tối đa 100 tools
  });

  // Cập nhật state khi có initialValues
  useEffect(() => {
    if (initialValues) {
      setName(initialValues.name || '');
      setDescription(initialValues.description || '');
      setSelectedToolIds(initialValues.tools?.map(tool => tool.id) || []);
    }
  }, [initialValues]);

  // Xử lý submit form
  const handleSubmit = () => {
    // Validate form
    const newErrors: Record<string, string> = {};

    if (!name.trim()) {
      newErrors.name = t('admin.tool.validation.nameRequired', 'Tên nhóm tool là bắt buộc');
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Chuẩn bị dữ liệu để submit
    const formData: CreateToolGroupParams | UpdateToolGroupParams = {
      name,
      description: description || undefined,
      toolIds: selectedToolIds.length > 0 ? selectedToolIds : undefined,
    };

    onSubmit(formData);
  };

  // Xử lý thay đổi danh sách tools được chọn
  const handleToolSelectionChange = (value: string | number | string[] | number[]) => {
    if (Array.isArray(value)) {
      setSelectedToolIds(value as string[]);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <Typography variant="h5">
          {isEdit
            ? t('admin.tool.editToolGroup', 'Chỉnh sửa nhóm tool')
            : t('admin.tool.createToolGroup', 'Tạo nhóm tool mới')}
        </Typography>
      </div>

      <div className="space-y-4">
        <FormItem label={t('admin.tool.name', 'Tên nhóm')} helpText={errors.name} required>
          <Input
            value={name}
            onChange={e => {
              setName(e.target.value);
              if (errors.name) setErrors(prev => ({ ...prev, name: '' }));
            }}
            placeholder={t('admin.tool.namePlaceholder', 'Nhập tên nhóm tool')}
            disabled={readOnly || isLoading}
          />
        </FormItem>

        <FormItem label={t('admin.tool.description', 'Mô tả')}>
          <Textarea
            value={description || ''}
            onChange={e => setDescription(e.target.value)}
            placeholder={t('admin.tool.descriptionPlaceholder', 'Nhập mô tả nhóm tool')}
            disabled={readOnly || isLoading}
            rows={3}
          />
        </FormItem>

        <FormItem label={t('admin.tool.selectTools', 'Chọn tools')}>
          <Select
            multiple
            value={selectedToolIds}
            onChange={handleToolSelectionChange}
            options={
              toolsData?.items?.map(tool => ({
                value: tool.id,
                label: tool.name,
              })) || []
            }
            disabled={readOnly || isLoading}
            placeholder={t('admin.tool.selectToolsPlaceholder', 'Chọn tools cho nhóm')}
          />
        </FormItem>
      </div>

      {!readOnly && (
        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button variant="primary" onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? t('common.processing', 'Đang xử lý...') : t('common.save', 'Lưu')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default ToolGroupForm;
