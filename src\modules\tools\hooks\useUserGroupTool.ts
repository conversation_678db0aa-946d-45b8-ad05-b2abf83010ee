import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { userGroupToolService } from '../services/user-group-tool.service';
import { CreateUserGroupToolParams, UpdateUserGroupToolParams, UserGroupToolQueryParams } from '../types';

// Định nghĩa các query key
export const USER_GROUP_TOOL_QUERY_KEYS = {
  all: ['user-group-tools'] as const,
  lists: () => [...USER_GROUP_TOOL_QUERY_KEYS.all, 'list'] as const,
  list: (params: UserGroupToolQueryParams) => [...USER_GROUP_TOOL_QUERY_KEYS.lists(), params] as const,
  details: () => [...USER_GROUP_TOOL_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...USER_GROUP_TOOL_QUERY_KEYS.details(), id] as const,
  byTypeAgent: () => [...USER_GROUP_TOOL_QUERY_KEYS.all, 'by-type-agent'] as const,
  byTypeAgentId: (typeAgentId: number) => [...USER_GROUP_TOOL_QUERY_KEYS.byTypeAgent(), typeAgentId] as const,
};

/**
 * Hook lấy danh sách nhóm tool
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useUserGroupTools = (params: UserGroupToolQueryParams) => {
  return useQuery({
    queryKey: USER_GROUP_TOOL_QUERY_KEYS.list(params),
    queryFn: () => userGroupToolService.getGroupTools(params),
  });
};

/**
 * Hook lấy thông tin chi tiết nhóm tool
 * @param id ID của nhóm tool
 * @returns Query object
 */
export const useUserGroupToolDetail = (id: number) => {
  return useQuery({
    queryKey: USER_GROUP_TOOL_QUERY_KEYS.detail(id),
    queryFn: () => userGroupToolService.getGroupToolById(id),
    enabled: !!id,
  });
};

/**
 * Hook lấy danh sách nhóm tool theo loại agent
 * @param typeAgentId ID của loại agent
 * @returns Query object
 */
export const useUserGroupToolsByTypeAgent = (typeAgentId: number) => {
  return useQuery({
    queryKey: USER_GROUP_TOOL_QUERY_KEYS.byTypeAgentId(typeAgentId),
    queryFn: () => userGroupToolService.getGroupToolsByTypeAgentId(typeAgentId),
    enabled: !!typeAgentId,
  });
};

/**
 * Hook tạo mới nhóm tool
 * @returns Mutation object
 */
export const useCreateUserGroupTool = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (params: CreateUserGroupToolParams) => userGroupToolService.createGroupTool(params),
    onSuccess: () => {
      // Invalidate và refetch danh sách nhóm tool
      queryClient.invalidateQueries({
        queryKey: USER_GROUP_TOOL_QUERY_KEYS.lists(),
      });
      // Invalidate danh sách nhóm tool theo loại agent
      queryClient.invalidateQueries({
        queryKey: USER_GROUP_TOOL_QUERY_KEYS.byTypeAgent(),
      });
    },
  });
};

/**
 * Hook cập nhật thông tin nhóm tool
 * @returns Mutation object
 */
export const useUpdateUserGroupTool = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateUserGroupToolParams }) => 
      userGroupToolService.updateGroupTool(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết nhóm tool
      queryClient.invalidateQueries({
        queryKey: USER_GROUP_TOOL_QUERY_KEYS.detail(variables.id),
      });
      // Invalidate danh sách nhóm tool
      queryClient.invalidateQueries({
        queryKey: USER_GROUP_TOOL_QUERY_KEYS.lists(),
      });
      // Invalidate danh sách nhóm tool theo loại agent
      queryClient.invalidateQueries({
        queryKey: USER_GROUP_TOOL_QUERY_KEYS.byTypeAgent(),
      });
    },
  });
};

/**
 * Hook xóa nhóm tool
 * @returns Mutation object
 */
export const useDeleteUserGroupTool = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: number) => userGroupToolService.deleteGroupTool(id),
    onSuccess: (_, id) => {
      // Invalidate và refetch danh sách nhóm tool
      queryClient.invalidateQueries({
        queryKey: USER_GROUP_TOOL_QUERY_KEYS.lists(),
      });
      // Invalidate chi tiết nhóm tool
      queryClient.invalidateQueries({
        queryKey: USER_GROUP_TOOL_QUERY_KEYS.detail(id),
      });
      // Invalidate danh sách nhóm tool theo loại agent
      queryClient.invalidateQueries({
        queryKey: USER_GROUP_TOOL_QUERY_KEYS.byTypeAgent(),
      });
    },
  });
};

/**
 * Hook cập nhật danh sách tool của nhóm
 * @returns Mutation object
 */
export const useUpdateUserGroupToolTools = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, toolIds }: { id: number; toolIds: string[] }) => 
      userGroupToolService.updateGroupToolTools(id, toolIds),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết nhóm tool
      queryClient.invalidateQueries({
        queryKey: USER_GROUP_TOOL_QUERY_KEYS.detail(variables.id),
      });
    },
  });
};
