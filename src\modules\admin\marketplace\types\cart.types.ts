/**
 * Đ<PERSON><PERSON> nghĩ<PERSON> các types cho Cart trong Admin Marketplace
 */
import { Product } from './product.types';

/**
 * Interface cho item trong giỏ hàng
 */
export interface CartItem {
  id: string;
  productId: string;
  quantity: number;
  product: Product;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho giỏ hàng
 */
export interface Cart {
  id: string;
  user: User;
  items: CartItem[];
  totalValue: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho dữ liệu thêm sản phẩm vào giỏ hàng
 */
export interface AddToCartDto {
  productId: string;
  quantity: number;
}

/**
 * Interface cho dữ liệu cập nhật số lượng sản phẩm trong giỏ hàng
 */
export interface UpdateCartItemDto {
  quantity: number;
}

/**
 * Interface cho thông tin người dùng
 */
export interface User {
  id: number;
  name: string;
  email: string;
  type?: string;
}

/**
 * Interface cho phản hồi API giỏ hàng
 */
export interface CartResponse {
  id: string;
  user: User;
  items: CartItem[];
  totalValue: number;
  createdAt: string;
  updatedAt: string;
}
