import React from 'react';
import { useTranslation } from 'react-i18next';

import { Card, IconCard, Tooltip, Chip } from '@/shared/components/common';
import { ToolListItem } from '../types/tool.types';
import { ToolStatus } from '../types/common.types';

interface ToolCardProps {
  tool: ToolListItem;
  onView?: (tool: ToolListItem) => void;
  onDelete?: (tool: ToolListItem) => void;
  onViewVersions?: (toolId: string) => void;
}

/**
 * Component hiển thị thông tin của một Tool
 */
const ToolCard: React.FC<ToolCardProps> = ({ tool, onView, onDelete, onViewVersions }) => {
  const { t } = useTranslation();

  const handleViewDetails = () => {
    if (onView) {
      onView(tool);
    }
  };

  const handleDeleteTool = () => {
    if (onDelete) {
      onDelete(tool);
    }
  };

  const handleViewVersions = () => {
    if (onViewVersions) {
      onViewVersions(tool.id);
    }
  };

  // Xác đ<PERSON>nh variant cho status chip
  const getStatusVariant = (
    status: ToolStatus
  ): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
    switch (status) {
      case ToolStatus.APPROVED:
        return 'success';
      case ToolStatus.DRAFT:
        return 'warning';
      case ToolStatus.DEPRECATED:
        return 'danger';
      default:
        return 'primary';
    }
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  return (
    <Card
      className="h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
      variant="elevated"
    >
      <div className="p-4">
        <div className="flex flex-col space-y-4">
          {/* Hàng 1: Tên, trạng thái và cập nhật */}
          <div className="flex items-center gap-3 overflow-hidden">
            {/* Icon tool */}
            <div className="relative w-12 h-12 flex-shrink-0 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <span className="text-xl">🛠️</span>
            </div>

            {/* Thông tin tool: tên, trạng thái và cập nhật */}
            <div className="flex flex-col min-w-0 flex-grow">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
                <div className="min-w-0">
                  <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                    {tool.name}
                  </h3>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {formatDate(tool.createdAt)}
                  </div>
                </div>
                <div className="flex-shrink-0 mt-1 sm:mt-0 flex gap-2">
                  <Chip variant={getStatusVariant(tool.status)} size="sm" className="font-normal">
                    {tool.status === ToolStatus.APPROVED
                      ? t('tools.status.approved', 'Đã duyệt')
                      : tool.status === ToolStatus.DRAFT
                        ? t('tools.status.draft', 'Bản nháp')
                        : t('tools.status.deprecated', 'Không dùng')}
                  </Chip>
                  {tool.hasUpdate && (
                    <Chip variant="info" size="sm" className="font-normal">
                      {t('tools.update', 'Cập nhật')}
                    </Chip>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Hàng 2: Mô tả */}
          <div className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
            {tool.description || t('tools.noDescription', 'Không có mô tả')}
          </div>

          {/* Hàng 3: Nhóm và các nút chức năng */}
          <div className="flex justify-between items-center">
            <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
              {tool.groupName && (
                <>
                  <span>{t('tools.group', 'Nhóm')}:</span>
                  <span className="font-medium">{tool.groupName}</span>
                </>
              )}
            </div>

            {/* Các nút chức năng */}
            <div className="flex justify-end space-x-2">
              {onDelete && (
                <Tooltip content={t('common.delete', 'Xóa')} position="top">
                  <IconCard icon="trash" variant="secondary" size="sm" onClick={handleDeleteTool} />
                </Tooltip>
              )}
              {onViewVersions && (
                <Tooltip content={t('tools.versions', 'Phiên bản')} position="top">
                  <IconCard
                    icon="layers"
                    variant="secondary"
                    size="sm"
                    onClick={handleViewVersions}
                  />
                </Tooltip>
              )}
              {onView && (
                <Tooltip content={t('common.view', 'Xem')} position="top">
                  <IconCard icon="eye" variant="primary" size="sm" onClick={handleViewDetails} />
                </Tooltip>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ToolCard;
